"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/utils/eventCollisionUtils.ts":
/*!******************************************!*\
  !*** ./src/utils/eventCollisionUtils.ts ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateAllDayLayout: function() { return /* binding */ calculateAllDayLayout; },\n/* harmony export */   calculateEventLayout: function() { return /* binding */ calculateEventLayout; },\n/* harmony export */   calculateLayout: function() { return /* binding */ calculateLayout; },\n/* harmony export */   calculateSegmentLayout: function() { return /* binding */ calculateSegmentLayout; },\n/* harmony export */   eventsOverlap: function() { return /* binding */ eventsOverlap; }\n/* harmony export */ });\n/* harmony import */ var _barrel_optimize_names_isSameDay_date_fns__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! __barrel_optimize__?names=isSameDay!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n\n// --- NEW CONSTANTS ---\nconst GUTTER_WIDTH_PERCENT = 10; // Space on the right for creating new events\nconst CASCADE_STAGGER_PERCENT = 15; // How much to offset each cascading event\nconst MIN_EVENT_WIDTH_PERCENT = 50; // Minimum width for events to remain readable\n/**\r\n * Checks if two event segments overlap in time on the same day.\r\n */ const segmentsOverlap = (segment1, segment2)=>{\n    // Must be on the same day\n    if (!(0,_barrel_optimize_names_isSameDay_date_fns__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(segment1.date, segment2.date)) {\n        return false;\n    }\n    return segment1.startTime < segment2.endTime && segment2.startTime < segment1.endTime;\n};\n/**\r\n * The primary function to calculate layout for event segments, incorporating a \"+N more\" button\r\n * and a gutter for creating new events.\r\n */ const calculateLayout = (segments)=>{\n    const finalLayout = {\n        segmentLayouts: []\n    };\n    if (!segments.length) {\n        return finalLayout;\n    }\n    // Sort all segments by start time, then by duration (longer events first to establish columns)\n    const sortedSegments = [\n        ...segments\n    ].sort((a, b)=>{\n        const startDiff = a.startTime.getTime() - b.startTime.getTime();\n        if (startDiff !== 0) return startDiff;\n        const durationB = b.endTime.getTime() - b.startTime.getTime();\n        const durationA = a.endTime.getTime() - a.startTime.getTime();\n        return durationB - durationA;\n    });\n    const processedSegments = new Set();\n    for (const segment of sortedSegments){\n        if (processedSegments.has(segment.id)) {\n            continue;\n        }\n        // Find all overlapping segments for the current segment\n        const overlappingGroup = sortedSegments.filter((s)=>segmentsOverlap(segment, s));\n        // This will hold the columns of segments for the current overlapping group\n        const columns = [];\n        // First, try to place each segment in its original time position\n        overlappingGroup.forEach((groupSegment)=>{\n            let placed = false;\n            // Try to place in existing columns first, preserving time\n            for (const column of columns){\n                if (column.every((s)=>!segmentsOverlap(groupSegment, s))) {\n                    column.push(groupSegment);\n                    placed = true;\n                    break;\n                }\n            }\n            // If it doesn't fit in any existing column, create a new one\n            if (!placed) {\n                columns.push([\n                    groupSegment\n                ]);\n            }\n        });\n        const maxColumns = columns.length;\n        const availableWidth = 100 - GUTTER_WIDTH_PERCENT;\n        // Calculate base event width and stagger\n        const baseWidth = Math.max(MIN_EVENT_WIDTH_PERCENT, availableWidth - (maxColumns - 1) * CASCADE_STAGGER_PERCENT);\n        const staggerStep = Math.min(CASCADE_STAGGER_PERCENT, (availableWidth - baseWidth) / Math.max(1, maxColumns - 1));\n        columns.forEach((column, colIndex)=>{\n            column.forEach((seg)=>{\n                if (!processedSegments.has(seg.id)) {\n                    const leftPosition = colIndex * staggerStep;\n                    const maxAllowedWidth = availableWidth - leftPosition;\n                    const finalWidth = Math.min(baseWidth, maxAllowedWidth);\n                    finalLayout.segmentLayouts.push({\n                        segment: seg,\n                        left: leftPosition,\n                        width: finalWidth,\n                        zIndex: 10 + colIndex,\n                        hasOverlap: maxColumns > 1,\n                        originalPosition: true // All events maintain their original time\n                    });\n                    processedSegments.add(seg.id);\n                }\n            });\n        });\n        // Mark all segments in the processed group\n        overlappingGroup.forEach((s)=>processedSegments.add(s.id));\n    }\n    return finalLayout;\n};\n// Deprecated old functions, replaced by calculateLayout\n/**\r\n * @deprecated Use calculateLayout instead. This function will be removed.\r\n */ const calculateSegmentLayout = (segments)=>{\n    console.warn(\"calculateSegmentLayout is deprecated. Use calculateLayout instead.\");\n    return [];\n};\n/**\r\n * @deprecated Use calculateLayout instead. This function will be removed.\r\n */ const calculateEventLayout = (events)=>{\n    console.warn(\"calculateEventLayout is deprecated. Use calculateLayout instead.\");\n    return [];\n};\n/**\r\n * Checks if two events overlap in time\r\n * This might still be useful elsewhere, so we keep it for now.\r\n */ const eventsOverlap = (event1, event2)=>{\n    const start1 = new Date(event1.start);\n    const end1 = new Date(event1.end);\n    const start2 = new Date(event2.start);\n    const end2 = new Date(event2.end);\n    return start1 < end2 && start2 < end1;\n};\n/**\r\n * A dedicated layout calculator for all-day events, which are laid out horizontally.\r\n * It determines which events to show and creates a \"more\" indicator for the rest.\r\n */ const calculateAllDayLayout = function(segments) {\n    let maxVisible = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3;\n    if (segments.length <= maxVisible) {\n        return {\n            visibleSegments: segments,\n            moreCount: 0\n        };\n    }\n    // Sort by start time, then duration\n    const sorted = [\n        ...segments\n    ].sort((a, b)=>{\n        const startDiff = a.startTime.getTime() - b.startTime.getTime();\n        if (startDiff !== 0) return startDiff;\n        return b.endTime.getTime() - b.startTime.getTime() - (a.endTime.getTime() - a.startTime.getTime());\n    });\n    const visibleSegments = sorted.slice(0, maxVisible - 1);\n    const moreCount = segments.length - visibleSegments.length;\n    return {\n        visibleSegments,\n        moreCount\n    };\n}; // ... keep other utility functions like groupOverlappingEvents if they might be used elsewhere ...\n // For this refactor, we are focusing on replacing the main layout calculation.\n // The rest of the file can be cleaned up later.\n // Remove the old grouping and layout functions as their logic is now inside calculateLayout\n /*\r\nexport const groupOverlappingSegments = (segments: EventSegment[]): EventSegment[][] => {\r\n    ...\r\n};\r\n*/ \n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/eventCollisionUtils.ts\n"));

/***/ })

});