"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx":
/*!*******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/MonthView.tsx ***!
  \*******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MonthView: function() { return /* binding */ MonthView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CalendarEventItem */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx\");\n/* harmony import */ var _NoEvents__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./NoEvents */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/NoEvents.tsx\");\n/* harmony import */ var _CalendarSideCard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./CalendarSideCard */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarSideCard.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst DayCell = (param)=>{\n    let { date, children, onClick, isCurrentMonth } = param;\n    _s();\n    const { setNodeRef, isOver } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__.useDroppable)({\n        id: \"daycell-\".concat((0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(date, \"yyyy-MM-dd\")),\n        data: {\n            date: date,\n            type: \"daycell\"\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: setNodeRef,\n        onClick: onClick,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-b border-r border-neutral-300 relative cursor-pointer transition-colors group flex flex-col p-2 min-h-[120px] lg:p-3 lg:min-h-[140px]\", isCurrentMonth ? \"bg-white hover:bg-neutral-50\" : \"bg-neutral-100 hover:bg-neutral-200\", isOver && \"bg-blue-50 border-blue-200\"),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DayCell, \"DmJTTt6A5xWIX/faBiFge3FOLrw=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__.useDroppable\n    ];\n});\n_c = DayCell;\n// New helper function to process events for the entire month with proper slot tracking\nconst useMonthEvents = (weeks, events)=>{\n    _s1();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const positionedEventsByWeek = new Map();\n        const cellSlotUsage = new Map(); // Track events occupying each cell\n        weeks.forEach((week, weekIndex)=>{\n            const weekStart = week[0];\n            const weekEnd = week[6];\n            const weekEvents = events.filter((event)=>{\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                return eventStart <= weekEnd && eventEnd >= weekStart;\n            });\n            const spanningEvents = [];\n            weekEvents.forEach((event)=>{\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                const startDayIndex = week.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(day, eventStart));\n                const endDayIndex = week.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(day, eventEnd));\n                const eventSpansWeek = startDayIndex !== -1 || endDayIndex !== -1 || eventStart < weekStart && eventEnd > weekEnd;\n                if (eventSpansWeek) {\n                    const start = startDayIndex !== -1 ? startDayIndex : 0;\n                    const end = endDayIndex !== -1 ? endDayIndex : 6;\n                    spanningEvents.push({\n                        event,\n                        startDayIndex: start,\n                        endDayIndex: end,\n                        colSpan: end - start + 1\n                    });\n                }\n            });\n            const positioned = [];\n            const rows = [];\n            const sortedEvents = spanningEvents.sort((a, b)=>a.startDayIndex - b.startDayIndex || b.colSpan - a.colSpan);\n            sortedEvents.forEach((event)=>{\n                let assigned = false;\n                for(let i = 0; i < rows.length; i++){\n                    const row = rows[i];\n                    if (!row.some((e)=>event.startDayIndex <= e.endDayIndex && event.endDayIndex >= e.startDayIndex)) {\n                        row.push(event);\n                        positioned.push({\n                            ...event,\n                            row: i\n                        });\n                        assigned = true;\n                        break;\n                    }\n                }\n                if (!assigned) {\n                    rows.push([\n                        event\n                    ]);\n                    positioned.push({\n                        ...event,\n                        row: rows.length - 1\n                    });\n                }\n            });\n            // Track slot usage for each cell in this week\n            positioned.forEach((positionedEvent)=>{\n                for(let dayIndex = positionedEvent.startDayIndex; dayIndex <= positionedEvent.endDayIndex; dayIndex++){\n                    const cellKey = \"\".concat(weekIndex, \"-\").concat(dayIndex);\n                    if (!cellSlotUsage.has(cellKey)) {\n                        cellSlotUsage.set(cellKey, []);\n                    }\n                    cellSlotUsage.get(cellKey).push(positionedEvent);\n                    // Debug logging (remove in production)\n                    if (true) {\n                        var _week_dayIndex;\n                        const dayName = (_week_dayIndex = week[dayIndex]) === null || _week_dayIndex === void 0 ? void 0 : _week_dayIndex.toLocaleDateString();\n                        console.log(\"Cell \".concat(cellKey, \" (\").concat(dayName, '): Event \"').concat(positionedEvent.event.title, '\" occupies slot. Total slots used: ').concat(cellSlotUsage.get(cellKey).length));\n                    }\n                }\n            });\n            positionedEventsByWeek.set(weekIndex, positioned);\n        });\n        return {\n            positionedEventsByWeek,\n            cellSlotUsage\n        };\n    }, [\n        weeks,\n        events\n    ]);\n};\n_s1(useMonthEvents, \"nwk+m61qLgjDVUp4IGV/072DDN4=\");\nconst MonthView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, setSelectedDate, openAddEventForm, canEditData, handleEventClick, activeDragData } = param;\n    _s2();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_5__.useMaybeRecord)();\n    const isInRecordTab = !!maybeRecord;\n    // Memoize month calculations\n    const monthCalculations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const monthStart = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(selectedDate);\n        const monthEnd = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(selectedDate);\n        const startDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(monthStart, {\n            weekStartsOn: 0\n        });\n        const endDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(monthEnd, {\n            weekStartsOn: 0\n        });\n        const days = [];\n        let day = startDay;\n        while(day <= endDay){\n            days.push(day);\n            day = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(day, 1);\n        }\n        const weeks = [];\n        for(let i = 0; i < days.length; i += 7){\n            weeks.push(days.slice(i, i + 7));\n        }\n        return {\n            monthStart,\n            monthEnd,\n            startDay,\n            endDay,\n            days,\n            weeks\n        };\n    }, [\n        selectedDate\n    ]);\n    // Memoize month events\n    const monthEvents = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>events.filter((event)=>{\n            const eventStart = new Date(event.start);\n            return eventStart >= monthCalculations.startDay && eventStart <= monthCalculations.endDay;\n        }), [\n        events,\n        monthCalculations.startDay,\n        monthCalculations.endDay\n    ]);\n    const { cellSlotUsage } = useMonthEvents(monthCalculations.weeks, events);\n    // Render empty state when no events\n    const renderEmptyState = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-7 border-b border-neutral-300 bg-white\",\n                    children: [\n                        \"Sunday\",\n                        \"Monday\",\n                        \"Tuesday\",\n                        \"Wednesday\",\n                        \"Thursday\",\n                        \"Friday\",\n                        \"Saturday\"\n                    ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-black\", \"py-2 text-xs\"),\n                            children: dayName.substring(0, 3)\n                        }, dayName, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 198,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NoEvents__WEBPACK_IMPORTED_MODULE_7__.NoEvents, {\n                    title: \"No events this month\",\n                    message: \"\".concat((0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate, \"MMMM yyyy\"), \" is completely free. Start planning your month!\"),\n                    showCreateButton: canEditData,\n                    onCreate: ()=>openAddEventForm(selectedDate)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n            lineNumber: 197,\n            columnNumber: 5\n        }, undefined);\n    // Render day cell content\n    const renderDayCellContent = (day, dayEvents, allCellEvents)=>{\n        const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n        const isCurrentDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(day);\n        const MAX_VISIBLE_EVENTS = 4;\n        const ROW_HEIGHT = 28;\n        // Use allCellEvents to determine if we have overflow, but only render dayEvents\n        // This ensures multi-day events consume slots even if they don't render in this cell\n        const sortedEvents = dayEvents.sort((a, b)=>a.row - b.row);\n        const visibleEvents = sortedEvents.slice(0, MAX_VISIBLE_EVENTS);\n        const hasMore = allCellEvents.length > MAX_VISIBLE_EVENTS;\n        // To fix the gap after the \"+more\" link, we must calculate the container's height\n        // instead of letting it expand. The link is placed at the start of the 5th row,\n        // and we'll give it 20px of height.\n        const maxRow = visibleEvents.reduce((max, event)=>Math.max(max, event.row), -1);\n        const containerHeight = hasMore ? MAX_VISIBLE_EVENTS * ROW_HEIGHT + 20 : (maxRow + 1) * ROW_HEIGHT;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center rounded-full font-semibold text-xs w-6 h-6\", isCurrentDay ? \"bg-black text-white\" : isCurrentMonth ? \"text-black hover:bg-neutral-100\" : \"text-neutral-400\"),\n                            children: (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(day, \"d\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, undefined),\n                        canEditData && isCurrentMonth && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            className: \"rounded-full h-6 w-6 p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-neutral-100 hidden lg:inline-flex\",\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                setTimeout(()=>openAddEventForm(day), 150);\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"h-3 w-3 text-black\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    style: {\n                        height: \"\".concat(containerHeight, \"px\")\n                    },\n                    children: [\n                        visibleEvents.map((pe)=>{\n                            var _activeDragData_payload;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute\",\n                                style: {\n                                    top: \"\".concat(pe.row * ROW_HEIGHT, \"px\"),\n                                    left: \"2px\",\n                                    width: pe.colSpan > 1 ? \"calc(\".concat(pe.colSpan * 100, \"% + \").concat((pe.colSpan - 1) * 19, \"px)\") : \"calc(100% - 4px)\",\n                                    // zIndex: pe.colSpan > 1 ? 10 : 1,\n                                    zIndex: 10 + pe.row\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__.CalendarEventItem, {\n                                    event: pe.event,\n                                    view: \"month\",\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        setSelectedEvent(pe.event.id);\n                                        handleEventClick(pe.event);\n                                    },\n                                    isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === pe.event.id\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, pe.event.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, undefined);\n                        }),\n                        hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-black hover:text-black font-medium text-xs cursor-pointer\",\n                            style: {\n                                position: \"absolute\",\n                                top: \"\".concat(MAX_VISIBLE_EVENTS * ROW_HEIGHT, \"px\"),\n                                left: \"2px\"\n                            },\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                setSelectedDate(day);\n                            },\n                            children: [\n                                \"+ \",\n                                allCellEvents.length - MAX_VISIBLE_EVENTS,\n                                \" more\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true);\n    };\n    // Render main view\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-background flex flex-col lg:flex-row\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col min-h-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        \"data-day-headers\": \"true\",\n                        className: \"grid grid-cols-7 border-b border-neutral-300 sticky top-0 z-10 bg-white\",\n                        children: [\n                            \"Sunday\",\n                            \"Monday\",\n                            \"Tuesday\",\n                            \"Wednesday\",\n                            \"Thursday\",\n                            \"Friday\",\n                            \"Saturday\"\n                        ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-black\", \"py-2 text-xs\"),\n                                children: dayName.substring(0, 3)\n                            }, dayName, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n                        className: \"flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-7 border-neutral-300 border-b\",\n                            children: monthCalculations.weeks.map((week, weekIndex)=>week.map((day, dayIndex)=>{\n                                    // Get all events that occupy this cell (both starting and passing through)\n                                    const cellKey = \"\".concat(weekIndex, \"-\").concat(dayIndex);\n                                    const cellEvents = cellSlotUsage.get(cellKey) || [];\n                                    // Filter to only show events that start on this day for rendering\n                                    // (multi-day events are rendered from their start cell but consume slots in all cells)\n                                    const dayEvents = cellEvents.filter((pe)=>pe.startDayIndex === dayIndex);\n                                    const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DayCell, {\n                                        date: day,\n                                        isCurrentMonth: isCurrentMonth,\n                                        onClick: ()=>setSelectedDate(day),\n                                        children: renderDayCellContent(day, dayEvents, cellEvents)\n                                    }, \"\".concat(weekIndex, \"-\").concat(dayIndex), false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 19\n                                    }, undefined);\n                                }))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 332,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 314,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarSideCard__WEBPACK_IMPORTED_MODULE_8__.CalendarSideCard, {\n                selectedDate: selectedDate,\n                events: events,\n                selectedEvent: selectedEvent,\n                setSelectedEvent: setSelectedEvent,\n                handleEventClick: handleEventClick\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 360,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 313,\n        columnNumber: 5\n    }, undefined);\n};\n_s2(MonthView, \"ZA7vf7RJJvLFeuWKLGWjE9vQsCg=\", false, function() {\n    return [\n        _providers_record__WEBPACK_IMPORTED_MODULE_5__.useMaybeRecord,\n        useMonthEvents\n    ];\n});\n_c1 = MonthView;\nvar _c, _c1;\n$RefreshReg$(_c, \"DayCell\");\n$RefreshReg$(_c1, \"MonthView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx\n"));

/***/ })

});