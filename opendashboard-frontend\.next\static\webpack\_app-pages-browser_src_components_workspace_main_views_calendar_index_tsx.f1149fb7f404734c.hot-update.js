"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/DayView.tsx":
/*!*****************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/DayView.tsx ***!
  \*****************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DayView: function() { return /* binding */ DayView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=format,isToday,setHours!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=format,isToday,setHours!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=format,isToday,setHours!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/setHours/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CalendarEventSegment */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx\");\n/* harmony import */ var _NoEvents__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./NoEvents */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/NoEvents.tsx\");\n/* harmony import */ var _utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/multiDayEventUtils */ \"(app-pages-browser)/./src/utils/multiDayEventUtils.ts\");\n/* harmony import */ var _utils_eventCollisionUtils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/eventCollisionUtils */ \"(app-pages-browser)/./src/utils/eventCollisionUtils.ts\");\n/* harmony import */ var _AllDayRow__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./AllDayRow */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/AllDayRow.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst TimeSlot = (param)=>{\n    let { hour, date, onDoubleClick, children } = param;\n    _s();\n    const [isHovering, setIsHovering] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDraggingOver, setIsDraggingOver] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const minuteSegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>Array.from({\n            length: 60\n        }, (_, i)=>i), []);\n    // Only create minute segments when hovering or dragging\n    const shouldShowSegments = isHovering || isDraggingOver;\n    // Track drag enter/leave for the entire cell\n    const { setNodeRef: setCellRef } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__.useDroppable)({\n        id: \"timeslot-cell-\".concat((0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(date, \"yyyy-MM-dd\"), \"-\").concat(hour),\n        data: {\n            date,\n            hour,\n            type: \"timeslot-cell\"\n        },\n        disabled: shouldShowSegments // Disable cell-level drop when segments are active\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: setCellRef,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-1 relative min-h-[60px] cursor-pointer\"),\n        style: {\n            height: \"60px\"\n        },\n        onMouseEnter: ()=>setIsHovering(true),\n        onMouseLeave: ()=>{\n            // Small delay to prevent flickering during drag\n            if (!isDraggingOver) {\n                setTimeout(()=>setIsHovering(false), 100);\n            }\n        },\n        children: [\n            shouldShowSegments && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex flex-col\",\n                onDragEnter: ()=>setIsDraggingOver(true),\n                onDragLeave: ()=>setIsDraggingOver(false),\n                children: minuteSegments.map((minute)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MinuteSegment, {\n                        date: date,\n                        hour: hour,\n                        minute: minute,\n                        style: {\n                            height: \"\".concat(100 / 60, \"%\"),\n                            top: \"\".concat(minute / 60 * 100, \"%\")\n                        },\n                        onDoubleClick: ()=>onDoubleClick(minute)\n                    }, minute, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                lineNumber: 77,\n                columnNumber: 9\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TimeSlot, \"POhpn0tEaD1kbIi8TnpraaC1zs4=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__.useDroppable\n    ];\n});\n_c = TimeSlot;\n// Optimize MinuteSegment with memo to prevent unnecessary re-renders\nconst MinuteSegment = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_s1((param)=>{\n    let { date, hour, minute, style, onDoubleClick } = param;\n    _s1();\n    const { setNodeRef, isOver } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__.useDroppable)({\n        id: \"timeslot-\".concat((0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(date, \"yyyy-MM-dd\"), \"-\").concat(hour, \"-\").concat(minute),\n        data: {\n            date,\n            hour,\n            minute,\n            type: \"timeslot-minute\"\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: setNodeRef,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"absolute w-full\", isOver && \"bg-blue-50\"),\n        style: style,\n        onDoubleClick: onDoubleClick\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n        lineNumber: 127,\n        columnNumber: 5\n    }, undefined);\n}, \"DmJTTt6A5xWIX/faBiFge3FOLrw=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__.useDroppable\n    ];\n}));\n_c1 = MinuteSegment;\nconst DayView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, openAddEventForm, canEditData, savedScrollTop, handleEventClick, activeDragData } = param;\n    _s2();\n    const hours = Array.from({\n        length: 24\n    }, (_, i)=>i);\n    // Memoize event segments to prevent unnecessary recalculations\n    const daySegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const allSegments = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.eventsToSegments)(events);\n        return (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.getSegmentsForDay)(allSegments, selectedDate);\n    }, [\n        events,\n        selectedDate\n    ]);\n    // Separate all-day and time-slot segments\n    const allDaySegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.getAllDaySegments)(daySegments), [\n        daySegments\n    ]);\n    const timeSlotSegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.getTimeSlotSegments)(daySegments), [\n        daySegments\n    ]);\n    // Calculate layout for overlapping segments\n    const { segmentLayouts } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return (0,_utils_eventCollisionUtils__WEBPACK_IMPORTED_MODULE_6__.calculateLayout)(timeSlotSegments);\n    }, [\n        timeSlotSegments\n    ]);\n    // Memoize current time position\n    const currentTimePosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate) ? {\n            hour: new Date().getHours(),\n            minutes: new Date().getMinutes()\n        } : null, [\n        selectedDate\n    ]);\n    // Render empty state when no events\n    const renderEmptyState = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NoEvents__WEBPACK_IMPORTED_MODULE_4__.NoEvents, {\n            title: \"No events scheduled\",\n            message: (0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate) ? \"You have a free day ahead! Add an event to get started.\" : \"\".concat((0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(selectedDate, \"EEEE, MMMM d\"), \" is completely free.\"),\n            showCreateButton: canEditData,\n            onCreate: ()=>{\n                const newDate = new Date(selectedDate);\n                newDate.setHours(9, 0, 0, 0);\n                openAddEventForm(newDate);\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n            lineNumber: 180,\n            columnNumber: 5\n        }, undefined);\n    // Render time slots with events\n    const renderTimeSlots = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 overflow-auto relative bg-white\",\n            id: \"day-view-container\",\n            children: [\n                hours.map((hour, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex border-b border-neutral-300 hover:bg-neutral-50 transition-colors\", i === hours.length - 1 && \"border-b-neutral-300\"),\n                        style: {\n                            height: \"60px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                \"data-time-labels\": \"true\",\n                                className: \"flex items-start justify-end pr-4 pt-2 text-xs font-medium text-black border-r border-neutral-300 sticky left-0 bg-white z-10 w-14 lg:w-20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs font-semibold\",\n                                            children: (0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(selectedDate, hour), \"h\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-black opacity-60\",\n                                            children: (0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(selectedDate, hour), \"a\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TimeSlot, {\n                                hour: hour,\n                                date: selectedDate,\n                                onDoubleClick: (minute)=>{\n                                    if (canEditData) {\n                                        const newDate = new Date(selectedDate);\n                                        newDate.setHours(hour, minute, 0, 0);\n                                        openAddEventForm(newDate);\n                                    }\n                                },\n                                children: segmentLayouts.map((layout)=>{\n                                    var _activeDragData_payload;\n                                    const segmentStart = layout.segment.startTime;\n                                    const isFirstHour = segmentStart.getHours() === hour;\n                                    if (!isFirstHour) return null;\n                                    const segmentHeight = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.getSegmentHeight)(layout.segment);\n                                    const topOffset = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.getSegmentTopOffset)(layout.segment);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__.CalendarEventSegment, {\n                                        segment: layout.segment,\n                                        style: {\n                                            height: \"\".concat(segmentHeight, \"px\"),\n                                            position: \"absolute\",\n                                            top: \"\".concat(topOffset, \"px\"),\n                                            left: \"\".concat(layout.left, \"%\"),\n                                            width: \"\".concat(layout.width, \"%\"),\n                                            zIndex: selectedEvent === layout.segment.originalEventId ? 20 : layout.zIndex,\n                                            paddingRight: \"2px\",\n                                            border: layout.hasOverlap ? \"1px solid white\" : \"none\"\n                                        },\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            const container = document.getElementById(\"day-view-container\");\n                                            if (container) {\n                                                savedScrollTop.current = container.scrollTop;\n                                            }\n                                            setSelectedEvent(layout.segment.originalEventId);\n                                            handleEventClick(layout.segment.originalEvent);\n                                        },\n                                        view: \"day\",\n                                        isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === layout.segment.id\n                                    }, layout.segment.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 17\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, i, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 9\n                    }, undefined)),\n                currentTimePosition && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute flex items-center z-30 pointer-events-none left-14 lg:left-20\",\n                    style: {\n                        top: \"\".concat((currentTimePosition.hour + currentTimePosition.minutes / 60) * 60, \"px\"),\n                        right: \"4px\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-3 h-3 rounded-full bg-red-500 border-2 border-white shadow-lg -ml-1.5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 border-t-2 border-red-500 shadow-sm\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n            lineNumber: 196,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center bg-white border-b border-neutral-300 py-2 px-2 lg:px-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"font-semibold text-black mb-1 text-xs\",\n                        children: (0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(selectedDate, \"EEEE\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center font-medium text-xs w-6 h-6 rounded-full\", (0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate) ? \"bg-black text-white\" : \"text-black hover:bg-neutral-100\"),\n                        children: (0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(selectedDate, \"d\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, undefined),\n            daySegments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AllDayRow__WEBPACK_IMPORTED_MODULE_7__.AllDayRow, {\n                selectedDate: selectedDate,\n                segments: allDaySegments,\n                selectedEvent: selectedEvent,\n                setSelectedEvent: setSelectedEvent,\n                handleEventClick: handleEventClick,\n                canEditData: canEditData,\n                openAddEventForm: openAddEventForm,\n                view: \"day\",\n                activeDragData: activeDragData\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                lineNumber: 309,\n                columnNumber: 9\n            }, undefined),\n            daySegments.length === 0 ? renderEmptyState() : renderTimeSlots()\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n        lineNumber: 291,\n        columnNumber: 5\n    }, undefined);\n};\n_s2(DayView, \"rKQaW4qzJjohXAHqKTEXOPEYpLE=\");\n_c2 = DayView;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"TimeSlot\");\n$RefreshReg$(_c1, \"MinuteSegment\");\n$RefreshReg$(_c2, \"DayView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/DayView.tsx\n"));

/***/ })

});