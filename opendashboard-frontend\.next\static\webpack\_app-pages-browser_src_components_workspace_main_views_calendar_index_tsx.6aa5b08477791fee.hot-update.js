"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx":
/*!*******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/MonthView.tsx ***!
  \*******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MonthView: function() { return /* binding */ MonthView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CalendarEventItem */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx\");\n/* harmony import */ var _NoEvents__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./NoEvents */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/NoEvents.tsx\");\n/* harmony import */ var _CalendarSideCard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./CalendarSideCard */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarSideCard.tsx\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// Helper function to count events in a cell\nconst getEventsForCell = (date, events, spanningEvents)=>{\n    const cellEvents = [];\n    // Add spanning (multi-day) events that pass through this cell\n    spanningEvents.forEach((spanningEvent)=>{\n        const eventStart = new Date(spanningEvent.segment.originalEvent.start);\n        const eventEnd = new Date(spanningEvent.segment.originalEvent.end);\n        if (date >= (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(eventStart) && date <= (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(eventEnd)) {\n            cellEvents.push({\n                event: spanningEvent.segment.originalEvent,\n                isMultiDay: true,\n                row: spanningEvent.row,\n                colSpan: spanningEvent.colSpan\n            });\n        }\n    });\n    // Add single-day events for this cell\n    events.forEach((event)=>{\n        const eventStart = new Date(event.start);\n        if ((0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(date, eventStart)) {\n            cellEvents.push({\n                event,\n                isMultiDay: false\n            });\n        }\n    });\n    return {\n        visibleEvents: cellEvents.slice(0, 4),\n        hiddenCount: Math.max(0, cellEvents.length - 4)\n    };\n};\n// New helper function to process events for the entire month\nconst useMonthEvents = (weeks, events)=>{\n    _s();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const positionedEventsByWeek = new Map();\n        weeks.forEach((week, weekIndex)=>{\n            const weekStart = week[0];\n            const weekEnd = week[6];\n            const weekEvents = events.filter((event)=>{\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                return eventStart <= weekEnd && eventEnd >= weekStart;\n            });\n            const spanningEvents = [];\n            weekEvents.forEach((event)=>{\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                const startDayIndex = week.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(day, eventStart));\n                const endDayIndex = week.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(day, eventEnd));\n                const eventSpansWeek = startDayIndex !== -1 || endDayIndex !== -1 || eventStart < weekStart && eventEnd > weekEnd;\n                if (eventSpansWeek) {\n                    const start = startDayIndex !== -1 ? startDayIndex : 0;\n                    const end = endDayIndex !== -1 ? endDayIndex : 6;\n                    spanningEvents.push({\n                        event,\n                        startDayIndex: start,\n                        endDayIndex: end,\n                        colSpan: end - start + 1\n                    });\n                }\n            });\n            const positioned = [];\n            const rows = [];\n            const sortedEvents = spanningEvents.sort((a, b)=>a.startDayIndex - b.startDayIndex || b.colSpan - a.colSpan);\n            sortedEvents.forEach((event)=>{\n                let assigned = false;\n                for(let i = 0; i < rows.length; i++){\n                    const row = rows[i];\n                    if (!row.some((e)=>event.startDayIndex <= e.endDayIndex && event.endDayIndex >= e.startDayIndex)) {\n                        row.push(event);\n                        positioned.push({\n                            ...event,\n                            row: i\n                        });\n                        assigned = true;\n                        break;\n                    }\n                }\n                if (!assigned) {\n                    rows.push([\n                        event\n                    ]);\n                    positioned.push({\n                        ...event,\n                        row: rows.length - 1\n                    });\n                }\n            });\n            positionedEventsByWeek.set(weekIndex, positioned);\n        });\n        return positionedEventsByWeek;\n    }, [\n        weeks,\n        events\n    ]);\n};\n_s(useMonthEvents, \"nwk+m61qLgjDVUp4IGV/072DDN4=\");\nconst MonthView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, setSelectedDate, openAddEventForm, canEditData, handleEventClick, activeDragData } = param;\n    _s1();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_5__.useMaybeRecord)();\n    const isInRecordTab = !!maybeRecord;\n    // Memoize month calculations\n    const monthCalculations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const monthStart = (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(selectedDate);\n        const monthEnd = (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(selectedDate);\n        const startDay = (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(monthStart, {\n            weekStartsOn: 0\n        });\n        const endDay = (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(monthEnd, {\n            weekStartsOn: 0\n        });\n        const days = [];\n        let day = startDay;\n        while(day <= endDay){\n            days.push(day);\n            day = (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(day, 1);\n        }\n        const weeks = [];\n        for(let i = 0; i < days.length; i += 7){\n            weeks.push(days.slice(i, i + 7));\n        }\n        return {\n            monthStart,\n            monthEnd,\n            startDay,\n            endDay,\n            days,\n            weeks\n        };\n    }, [\n        selectedDate\n    ]);\n    // Memoize month events\n    const monthEvents = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>events.filter((event)=>{\n            const eventStart = new Date(event.start);\n            return eventStart >= monthCalculations.startDay && eventStart <= monthCalculations.endDay;\n        }), [\n        events,\n        monthCalculations.startDay,\n        monthCalculations.endDay\n    ]);\n    const positionedEventsByWeek = useMonthEvents(monthCalculations.weeks, events);\n    // Render empty state when no events\n    const renderEmptyState = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-7 border-b border-neutral-300 bg-white\",\n                    children: [\n                        \"Sunday\",\n                        \"Monday\",\n                        \"Tuesday\",\n                        \"Wednesday\",\n                        \"Thursday\",\n                        \"Friday\",\n                        \"Saturday\"\n                    ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-black\", \"py-2 text-xs\"),\n                            children: dayName.substring(0, 3)\n                        }, dayName, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 11\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NoEvents__WEBPACK_IMPORTED_MODULE_7__.NoEvents, {\n                    title: \"No events this month\",\n                    message: \"\".concat((0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(selectedDate, \"MMMM yyyy\"), \" is completely free. Start planning your month!\"),\n                    showCreateButton: canEditData,\n                    onCreate: ()=>openAddEventForm(selectedDate)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n            lineNumber: 193,\n            columnNumber: 5\n        }, undefined);\n    // Render day cell content\n    const renderDayCellContent = (day, dayEvents)=>{\n        const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n        const isCurrentDay = (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_18__[\"default\"])(day);\n        const MAX_VISIBLE_EVENTS = 4;\n        const ROW_HEIGHT = 28;\n        const sortedEvents = dayEvents.sort((a, b)=>a.row - b.row);\n        const visibleEvents = sortedEvents.slice(0, MAX_VISIBLE_EVENTS);\n        const hasMore = sortedEvents.length > MAX_VISIBLE_EVENTS;\n        // To fix the gap after the \"+more\" link, we must calculate the container's height\n        // instead of letting it expand. The link is placed at the start of the 5th row,\n        // and we'll give it 20px of height.\n        const maxRow = visibleEvents.reduce((max, event)=>Math.max(max, event.row), -1);\n        const containerHeight = hasMore ? MAX_VISIBLE_EVENTS * ROW_HEIGHT + 20 : (maxRow + 1) * ROW_HEIGHT;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center rounded-full font-semibold text-xs w-6 h-6\", isCurrentDay ? \"bg-black text-white\" : isCurrentMonth ? \"text-black hover:bg-neutral-100\" : \"text-neutral-400\"),\n                            children: (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(day, \"d\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, undefined),\n                        canEditData && isCurrentMonth && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            className: \"rounded-full h-6 w-6 p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-neutral-100 hidden lg:inline-flex\",\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                setTimeout(()=>openAddEventForm(day), 150);\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                className: \"h-3 w-3 text-black\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 235,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    style: {\n                        height: \"\".concat(containerHeight, \"px\")\n                    },\n                    children: [\n                        visibleEvents.map((pe)=>{\n                            var _activeDragData_payload;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute\",\n                                style: {\n                                    top: \"\".concat(pe.row * ROW_HEIGHT, \"px\"),\n                                    left: \"2px\",\n                                    width: pe.colSpan > 1 ? \"calc(\".concat(pe.colSpan * 100, \"% + \").concat((pe.colSpan - 1) * 19, \"px)\") : \"calc(100% - 4px)\",\n                                    // zIndex: pe.colSpan > 1 ? 10 : 1,\n                                    zIndex: 10 + pe.row\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__.CalendarEventItem, {\n                                    event: pe.event,\n                                    view: \"month\",\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        setSelectedEvent(pe.event.id);\n                                        handleEventClick(pe.event);\n                                    },\n                                    isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === pe.event.id\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, pe.event.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 13\n                            }, undefined);\n                        }),\n                        hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-black hover:text-black font-medium text-xs cursor-pointer\",\n                            style: {\n                                position: \"absolute\",\n                                top: \"\".concat(MAX_VISIBLE_EVENTS * ROW_HEIGHT, \"px\"),\n                                left: \"2px\"\n                            },\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                setSelectedDate(day);\n                            },\n                            children: [\n                                \"+ \",\n                                sortedEvents.length - MAX_VISIBLE_EVENTS,\n                                \" more\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 256,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true);\n    };\n    // Render main view\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-background flex flex-col lg:flex-row\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col min-h-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        \"data-day-headers\": \"true\",\n                        className: \"grid grid-cols-7 border-b border-neutral-300 sticky top-0 z-10 bg-white\",\n                        children: [\n                            \"Sunday\",\n                            \"Monday\",\n                            \"Tuesday\",\n                            \"Wednesday\",\n                            \"Thursday\",\n                            \"Friday\",\n                            \"Saturday\"\n                        ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-black\", \"py-2 text-xs\"),\n                                children: dayName.substring(0, 3)\n                            }, dayName, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n                        className: \"flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-7 border-neutral-300 border-b\",\n                            children: monthCalculations.weeks.map((week, weekIndex)=>week.map((day, dayIndex)=>{\n                                    const weekEvents = positionedEventsByWeek.get(weekIndex) || [];\n                                    const dayEvents = weekEvents.filter((pe)=>pe.startDayIndex === dayIndex);\n                                    const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DayCell, {\n                                        date: day,\n                                        isCurrentMonth: isCurrentMonth,\n                                        onClick: ()=>setSelectedDate(day),\n                                        events: events,\n                                        spanningEvents: weekEvents,\n                                        onEventClick: handleEventClick,\n                                        selectedEvent: selectedEvent\n                                    }, \"\".concat(weekIndex, \"-\").concat(dayIndex), false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 19\n                                    }, undefined);\n                                }))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 308,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarSideCard__WEBPACK_IMPORTED_MODULE_8__.CalendarSideCard, {\n                selectedDate: selectedDate,\n                events: events,\n                selectedEvent: selectedEvent,\n                setSelectedEvent: setSelectedEvent,\n                handleEventClick: handleEventClick\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 351,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 307,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(MonthView, \"LfpiC9GNlfiXFS91dl5Hp92L/ME=\", false, function() {\n    return [\n        _providers_record__WEBPACK_IMPORTED_MODULE_5__.useMaybeRecord,\n        useMonthEvents\n    ];\n});\n_c = MonthView;\nfunction isMultiDay(event) {\n    return !(0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(new Date(event.start), new Date(event.end));\n}\nvar _c;\n$RefreshReg$(_c, \"MonthView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx\n"));

/***/ })

});