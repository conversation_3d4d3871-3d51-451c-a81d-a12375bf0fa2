"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: function() { return /* binding */ Card; },\n/* harmony export */   CardContent: function() { return /* binding */ CardContent; },\n/* harmony export */   CardDescription: function() { return /* binding */ CardDescription; },\n/* harmony export */   CardFooter: function() { return /* binding */ CardFooter; },\n/* harmony export */   CardHeader: function() { return /* binding */ CardHeader; },\n/* harmony export */   CardTitle: function() { return /* binding */ CardTitle; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-xl border bg-card text-card-foreground shadow\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined);\n});\n_c1 = Card;\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c2 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined);\n});\n_c3 = CardHeader;\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c4 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined);\n});\n_c5 = CardTitle;\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c6 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 48,\n        columnNumber: 3\n    }, undefined);\n});\n_c7 = CardDescription;\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c8 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 60,\n        columnNumber: 3\n    }, undefined);\n});\n_c9 = CardContent;\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c10 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 68,\n        columnNumber: 3\n    }, undefined);\n});\n_c11 = CardFooter;\nCardFooter.displayName = \"CardFooter\";\n\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11;\n$RefreshReg$(_c, \"Card$React.forwardRef\");\n$RefreshReg$(_c1, \"Card\");\n$RefreshReg$(_c2, \"CardHeader$React.forwardRef\");\n$RefreshReg$(_c3, \"CardHeader\");\n$RefreshReg$(_c4, \"CardTitle$React.forwardRef\");\n$RefreshReg$(_c5, \"CardTitle\");\n$RefreshReg$(_c6, \"CardDescription$React.forwardRef\");\n$RefreshReg$(_c7, \"CardDescription\");\n$RefreshReg$(_c8, \"CardContent$React.forwardRef\");\n$RefreshReg$(_c9, \"CardContent\");\n$RefreshReg$(_c10, \"CardFooter$React.forwardRef\");\n$RefreshReg$(_c11, \"CardFooter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/card.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarSideCard.tsx":
/*!**************************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/CalendarSideCard.tsx ***!
  \**************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CalendarSideCard: function() { return /* binding */ CalendarSideCard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_format_isSameDay_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=format,isSameDay!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_isSameDay_date_fns__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=format,isSameDay!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _providers_screenSize__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/screenSize */ \"(app-pages-browser)/./src/providers/screenSize.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst CalendarSideCard = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, handleEventClick } = param;\n    _s();\n    const { isMobile } = (0,_providers_screenSize__WEBPACK_IMPORTED_MODULE_5__.useScreenSize)();\n    const dayEvents = events.filter((event)=>(0,_barrel_optimize_names_format_isSameDay_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(new Date(event.start), selectedDate)).sort((a, b)=>new Date(a.start).getTime() - new Date(b.start).getTime());\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-side-card\": \"true\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-l border-neutral-300 flex flex-col bg-gradient-to-b from-neutral-50 to-neutral-100\", isMobile ? \"w-full border-t\" : \"w-96\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-b border-neutral-300 bg-gradient-to-r from-white to-neutral-50\", \"p-4 relative overflow-hidden\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 right-0 w-20 h-20 bg-neutral-100 rounded-full -translate-y-10 translate-x-10 opacity-30\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarSideCard.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-neutral-400 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarSideCard.tsx\",\n                                        lineNumber: 45,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"font-semibold text-black text-xs\",\n                                        children: (0,_barrel_optimize_names_format_isSameDay_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(selectedDate, \"MMM d, yyyy\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarSideCard.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarSideCard.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-3 h-3 text-neutral-500\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarSideCard.tsx\",\n                                            lineNumber: 52,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarSideCard.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-neutral-600\",\n                                        children: [\n                                            dayEvents.length,\n                                            \" \",\n                                            dayEvents.length === 1 ? \"event\" : \"events\",\n                                            \" scheduled\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarSideCard.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarSideCard.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarSideCard.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarSideCard.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n                className: \"flex-1 p-4\",\n                children: dayEvents.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center justify-center h-40 text-center relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-br from-neutral-100 to-transparent rounded-lg opacity-50\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarSideCard.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-white to-neutral-100 rounded-2xl flex items-center justify-center border border-neutral-200 shadow-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-7 h-7 text-neutral-400\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 1.5,\n                                            d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarSideCard.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarSideCard.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarSideCard.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-neutral-500 font-medium mb-1\",\n                                    children: \"No events scheduled\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarSideCard.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-neutral-400\",\n                                    children: \"Your day is completely free\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarSideCard.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarSideCard.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarSideCard.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: dayEvents.map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"cursor-pointer p-4 relative overflow-hidden\", selectedEvent === event.id ? \"bg-gradient-to-r from-neutral-200 to-neutral-300 text-black border-neutral-400 shadow-lg\" : \"bg-gradient-to-r from-white to-neutral-50 hover:from-neutral-50 hover:to-neutral-100 border-neutral-200\"),\n                            onClick: ()=>{\n                                setSelectedEvent(event.id);\n                                handleEventClick(event);\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-0 right-0 w-8 h-8 bg-gradient-to-br from-transparent to-neutral-200 opacity-30 rounded-bl-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarSideCard.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-semibold text-xs leading-relaxed\", selectedEvent === event.id ? \"text-black\" : \"text-black\"),\n                                                    children: event.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarSideCard.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"w-2 h-2 rounded-full flex-shrink-0 mt-1\", selectedEvent === event.id ? \"bg-neutral-600\" : \"bg-neutral-400\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarSideCard.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarSideCard.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center space-x-2 text-xs\", selectedEvent === event.id ? \"text-neutral-700\" : \"text-neutral-600\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"w-4 h-4 rounded-full flex items-center justify-center\", selectedEvent === event.id ? \"bg-neutral-400\" : \"bg-neutral-200\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-2.5 h-2.5\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2.5,\n                                                            d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarSideCard.tsx\",\n                                                            lineNumber: 118,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarSideCard.tsx\",\n                                                        lineNumber: 117,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarSideCard.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: [\n                                                        (0,_barrel_optimize_names_format_isSameDay_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(new Date(event.start), \"h:mm a\"),\n                                                        \" - \",\n                                                        (0,_barrel_optimize_names_format_isSameDay_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(new Date(event.end), \"h:mm a\")\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarSideCard.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarSideCard.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarSideCard.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, event.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarSideCard.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 15\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarSideCard.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarSideCard.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarSideCard.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CalendarSideCard, \"NhNlQCKT7mqGuQWPmw42Yz4hgLE=\", false, function() {\n    return [\n        _providers_screenSize__WEBPACK_IMPORTED_MODULE_5__.useScreenSize\n    ];\n});\n_c = CalendarSideCard;\nvar _c;\n$RefreshReg$(_c, \"CalendarSideCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarSideCard.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx":
/*!*******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/MonthView.tsx ***!
  \*******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MonthView: function() { return /* binding */ MonthView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CalendarEventItem */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx\");\n/* harmony import */ var _NoEvents__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./NoEvents */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/NoEvents.tsx\");\n/* harmony import */ var _CalendarSideCard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./CalendarSideCard */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarSideCard.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst DayCell = (param)=>{\n    let { date, children, onClick, isCurrentMonth } = param;\n    _s();\n    const { setNodeRef, isOver } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__.useDroppable)({\n        id: \"daycell-\".concat((0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(date, \"yyyy-MM-dd\")),\n        data: {\n            date: date,\n            type: \"daycell\"\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: setNodeRef,\n        onClick: onClick,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-b border-r border-neutral-300 relative cursor-pointer transition-colors group flex flex-col p-2 min-h-[120px] lg:p-3 lg:min-h-[140px]\", isCurrentMonth ? \"bg-white hover:bg-neutral-50\" : \"bg-neutral-100 hover:bg-neutral-200\", isOver && \"bg-blue-50 border-blue-200\"),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DayCell, \"DmJTTt6A5xWIX/faBiFge3FOLrw=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__.useDroppable\n    ];\n});\n_c = DayCell;\n// Helper function to check if an event affects a specific day\nconst eventAffectsDay = (event, day)=>{\n    const eventStart = new Date(event.start);\n    const eventEnd = new Date(event.end);\n    const dayStart = new Date(day);\n    const dayEnd = new Date(day);\n    dayEnd.setHours(23, 59, 59, 999);\n    return eventStart <= dayEnd && eventEnd >= dayStart;\n};\n// New helper function to process events for the entire month with proper slot tracking\nconst useMonthEvents = (weeks, events)=>{\n    _s1();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const positionedEventsByWeek = new Map();\n        const slotUsageByDay = new Map(); // Track slot usage per day\n        weeks.forEach((week, weekIndex)=>{\n            const weekStart = week[0];\n            const weekEnd = week[6];\n            // Get all events that intersect with this week\n            const weekEvents = events.filter((event)=>{\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                return eventStart <= weekEnd && eventEnd >= weekStart;\n            });\n            const spanningEvents = [];\n            weekEvents.forEach((event)=>{\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                // Find which days this event spans in this week\n                const startDayIndex = week.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(day, eventStart));\n                const endDayIndex = week.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(day, eventEnd));\n                // Calculate actual span within this week\n                let actualStart = 0;\n                let actualEnd = 6;\n                // For events that start in this week\n                if (startDayIndex !== -1) {\n                    actualStart = startDayIndex;\n                }\n                // For events that end in this week\n                if (endDayIndex !== -1) {\n                    actualEnd = endDayIndex;\n                }\n                // For events that span through this week entirely\n                if (startDayIndex === -1 && endDayIndex === -1 && eventStart < weekStart && eventEnd > weekEnd) {\n                    actualStart = 0;\n                    actualEnd = 6;\n                }\n                // Check if event intersects with this week\n                const eventSpansWeek = startDayIndex !== -1 || endDayIndex !== -1 || eventStart < weekStart && eventEnd > weekEnd;\n                if (eventSpansWeek) {\n                    spanningEvents.push({\n                        event,\n                        startDayIndex: actualStart,\n                        endDayIndex: actualEnd,\n                        colSpan: actualEnd - actualStart + 1\n                    });\n                }\n            });\n            // Sort events by start day, then by span length (longer events first)\n            const sortedEvents = spanningEvents.sort((a, b)=>{\n                if (a.startDayIndex !== b.startDayIndex) {\n                    return a.startDayIndex - b.startDayIndex;\n                }\n                return b.colSpan - a.colSpan;\n            });\n            // Position events and track slot usage\n            const positioned = [];\n            const rows = [];\n            sortedEvents.forEach((eventData)=>{\n                // Check if this event can be placed (all days it spans have available slots)\n                const affectedDays = week.slice(eventData.startDayIndex, eventData.endDayIndex + 1);\n                const canPlace = affectedDays.every((day)=>{\n                    const dayKey = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(day, \"yyyy-MM-dd\");\n                    const currentUsage = slotUsageByDay.get(dayKey) || 0;\n                    return currentUsage < 4; // Maximum 4 slots per day\n                });\n                if (!canPlace) {\n                    // Event cannot be placed, skip it (it will be in the \"+more\" count)\n                    return;\n                }\n                // Find available row\n                let assigned = false;\n                for(let i = 0; i < rows.length; i++){\n                    const row = rows[i];\n                    const hasConflict = row.some((existingEvent)=>eventData.startDayIndex <= existingEvent.endDayIndex && eventData.endDayIndex >= existingEvent.startDayIndex);\n                    if (!hasConflict) {\n                        row.push(eventData);\n                        positioned.push({\n                            ...eventData,\n                            row: i\n                        });\n                        assigned = true;\n                        break;\n                    }\n                }\n                if (!assigned) {\n                    rows.push([\n                        eventData\n                    ]);\n                    positioned.push({\n                        ...eventData,\n                        row: rows.length - 1\n                    });\n                }\n                // Update slot usage for all affected days\n                affectedDays.forEach((day)=>{\n                    const dayKey = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(day, \"yyyy-MM-dd\");\n                    const currentUsage = slotUsageByDay.get(dayKey) || 0;\n                    slotUsageByDay.set(dayKey, currentUsage + 1);\n                });\n            });\n            positionedEventsByWeek.set(weekIndex, positioned);\n        });\n        return {\n            positionedEventsByWeek,\n            slotUsageByDay\n        };\n    }, [\n        weeks,\n        events\n    ]);\n};\n_s1(useMonthEvents, \"nwk+m61qLgjDVUp4IGV/072DDN4=\");\nconst MonthView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, setSelectedDate, openAddEventForm, canEditData, handleEventClick, activeDragData } = param;\n    _s2();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_5__.useMaybeRecord)();\n    const isInRecordTab = !!maybeRecord;\n    // Memoize month calculations\n    const monthCalculations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const monthStart = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(selectedDate);\n        const monthEnd = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(selectedDate);\n        const startDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(monthStart, {\n            weekStartsOn: 0\n        });\n        const endDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(monthEnd, {\n            weekStartsOn: 0\n        });\n        const days = [];\n        let day = startDay;\n        while(day <= endDay){\n            days.push(day);\n            day = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(day, 1);\n        }\n        const weeks = [];\n        for(let i = 0; i < days.length; i += 7){\n            weeks.push(days.slice(i, i + 7));\n        }\n        return {\n            monthStart,\n            monthEnd,\n            startDay,\n            endDay,\n            days,\n            weeks\n        };\n    }, [\n        selectedDate\n    ]);\n    // Memoize month events\n    const monthEvents = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>events.filter((event)=>{\n            const eventStart = new Date(event.start);\n            const eventEnd = new Date(event.end);\n            return eventStart <= monthCalculations.endDay && eventEnd >= monthCalculations.startDay;\n        }), [\n        events,\n        monthCalculations.startDay,\n        monthCalculations.endDay\n    ]);\n    const { positionedEventsByWeek, slotUsageByDay } = useMonthEvents(monthCalculations.weeks, monthEvents);\n    // Render empty state when no events\n    const renderEmptyState = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-7 border-b border-neutral-300 bg-white\",\n                    children: [\n                        \"Sunday\",\n                        \"Monday\",\n                        \"Tuesday\",\n                        \"Wednesday\",\n                        \"Thursday\",\n                        \"Friday\",\n                        \"Saturday\"\n                    ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-black\", \"py-2 text-xs\"),\n                            children: dayName.substring(0, 3)\n                        }, dayName, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NoEvents__WEBPACK_IMPORTED_MODULE_7__.NoEvents, {\n                    title: \"No events this month\",\n                    message: \"\".concat((0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate, \"MMMM yyyy\"), \" is completely free. Start planning your month!\"),\n                    showCreateButton: canEditData,\n                    onCreate: ()=>openAddEventForm(selectedDate)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 263,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n            lineNumber: 251,\n            columnNumber: 5\n        }, undefined);\n    // Render day cell content\n    const renderDayCellContent = (day, dayEvents)=>{\n        const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n        const isCurrentDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(day);\n        const MAX_VISIBLE_EVENTS = 4;\n        const ROW_HEIGHT = 28;\n        const dayKey = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(day, \"yyyy-MM-dd\");\n        // Get all events that affect this day (for the +more count)\n        const allDayEvents = monthEvents.filter((event)=>eventAffectsDay(event, day));\n        const totalEventsForDay = allDayEvents.length;\n        const sortedEvents = dayEvents.sort((a, b)=>a.row - b.row);\n        const visibleEvents = sortedEvents.slice(0, MAX_VISIBLE_EVENTS);\n        const hasMore = totalEventsForDay > MAX_VISIBLE_EVENTS;\n        const hiddenEventsCount = Math.max(0, totalEventsForDay - MAX_VISIBLE_EVENTS);\n        // Calculate container height\n        const maxRow = visibleEvents.reduce((max, event)=>Math.max(max, event.row), -1);\n        const containerHeight = hasMore ? MAX_VISIBLE_EVENTS * ROW_HEIGHT + 20 : (maxRow + 1) * ROW_HEIGHT;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center rounded-full font-semibold text-xs w-6 h-6\", isCurrentDay ? \"bg-black text-white\" : isCurrentMonth ? \"text-black hover:bg-neutral-100\" : \"text-neutral-400\"),\n                            children: (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(day, \"d\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 11\n                        }, undefined),\n                        canEditData && isCurrentMonth && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            className: \"rounded-full h-6 w-6 p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-neutral-100 hidden lg:inline-flex\",\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                setTimeout(()=>openAddEventForm(day), 150);\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"h-3 w-3 text-black\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 298,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    style: {\n                        height: \"\".concat(containerHeight, \"px\")\n                    },\n                    children: [\n                        visibleEvents.map((pe)=>{\n                            var _activeDragData_payload;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute\",\n                                style: {\n                                    top: \"\".concat(pe.row * ROW_HEIGHT, \"px\"),\n                                    left: \"2px\",\n                                    width: pe.colSpan > 1 ? \"calc(\".concat(pe.colSpan * 100, \"% + \").concat((pe.colSpan - 1) * 19, \"px)\") : \"calc(100% - 4px)\",\n                                    zIndex: 10 + pe.row\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__.CalendarEventItem, {\n                                    event: pe.event,\n                                    view: \"month\",\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        setSelectedEvent(pe.event.id);\n                                        handleEventClick(pe.event);\n                                    },\n                                    isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === pe.event.id\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, pe.event.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 13\n                            }, undefined);\n                        }),\n                        hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-black hover:text-black font-medium text-xs cursor-pointer\",\n                            style: {\n                                position: \"absolute\",\n                                top: \"\".concat(MAX_VISIBLE_EVENTS * ROW_HEIGHT, \"px\"),\n                                left: \"2px\"\n                            },\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                setSelectedDate(day);\n                            },\n                            children: [\n                                \"+ \",\n                                hiddenEventsCount,\n                                \" more\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 319,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true);\n    };\n    // Render main view\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-background flex flex-col lg:flex-row\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col min-h-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        \"data-day-headers\": \"true\",\n                        className: \"grid grid-cols-7 border-b border-neutral-300 sticky top-0 z-10 bg-white\",\n                        children: [\n                            \"Sunday\",\n                            \"Monday\",\n                            \"Tuesday\",\n                            \"Wednesday\",\n                            \"Thursday\",\n                            \"Friday\",\n                            \"Saturday\"\n                        ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-black\", \"py-2 text-xs\"),\n                                children: dayName.substring(0, 3)\n                            }, dayName, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 372,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n                        className: \"flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-7 border-neutral-300 border-b\",\n                            children: monthCalculations.weeks.map((week, weekIndex)=>week.map((day, dayIndex)=>{\n                                    const weekEvents = positionedEventsByWeek.get(weekIndex) || [];\n                                    // Get events that start on this day OR span through this day\n                                    const dayEvents = weekEvents.filter((pe)=>pe.startDayIndex === dayIndex || pe.startDayIndex < dayIndex && pe.endDayIndex >= dayIndex);\n                                    const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DayCell, {\n                                        date: day,\n                                        isCurrentMonth: isCurrentMonth,\n                                        onClick: ()=>setSelectedDate(day),\n                                        children: renderDayCellContent(day, dayEvents)\n                                    }, \"\".concat(weekIndex, \"-\").concat(dayIndex), false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 19\n                                    }, undefined);\n                                }))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 388,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 387,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 370,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarSideCard__WEBPACK_IMPORTED_MODULE_8__.CalendarSideCard, {\n                selectedDate: selectedDate,\n                events: events,\n                selectedEvent: selectedEvent,\n                setSelectedEvent: setSelectedEvent,\n                handleEventClick: handleEventClick\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 415,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 369,\n        columnNumber: 5\n    }, undefined);\n};\n_s2(MonthView, \"t9SuGQQKM9r/+gjBud8WIP3gfyg=\", false, function() {\n    return [\n        _providers_record__WEBPACK_IMPORTED_MODULE_5__.useMaybeRecord,\n        useMonthEvents\n    ];\n});\n_c1 = MonthView;\nfunction isMultiDay(event) {\n    return !(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(new Date(event.start), new Date(event.end));\n} // import React, { useMemo } from 'react';\n // import { format, startOfMonth, endOfMonth, startOfWeek, endOfWeek, addDays, isSameDay, isToday } from 'date-fns';\n // import { cn } from '@/lib/utils';\n // import { Button } from '@/components/ui/button';\n // import { PlusIcon } from '@heroicons/react/24/outline';\n // import { ScrollArea } from '@/components/ui/scroll-area';\n // import { CalendarEvent } from '@/typings/page';\n // import { useMaybeRecord } from '@/providers/record';\n // import { CalendarEventItem } from './CalendarEventItem';\n // import { NoEvents } from './NoEvents';\n // import { CalendarSideCard } from './CalendarSideCard';\n // import { useDroppable } from '@dnd-kit/core';\n // interface MonthViewProps {\n //   selectedDate: Date;\n //   events: CalendarEvent[];\n //   selectedEvent: string | null;\n //   setSelectedEvent: (id: string) => void;\n //   setSelectedDate: (date: Date) => void;\n //   openAddEventForm: (date: Date) => void;\n //   canEditData: boolean;\n //   handleEventClick: (event: CalendarEvent) => void;\n //   activeDragData: any;\n // }\n // const DayCell = ({\n //   date,\n //   children,\n //   onClick,\n //   isCurrentMonth\n // }: {\n //   date: Date;\n //   children: React.ReactNode;\n //   onClick: () => void;\n //   isCurrentMonth: boolean;\n // }) => {\n //   const { setNodeRef, isOver } = useDroppable({\n //     id: `daycell-${format(date, 'yyyy-MM-dd')}`,\n //     data: {\n //       date: date,\n //       type: 'daycell'\n //     }\n //   });\n //   return (\n //     <div\n //       ref={setNodeRef}\n //       onClick={onClick}\n //       className={cn(\n //         \"border-b border-r border-neutral-300 relative cursor-pointer transition-colors group flex flex-col p-2 min-h-[120px] lg:p-3 lg:min-h-[140px]\",\n //         isCurrentMonth\n //           ? \"bg-white hover:bg-neutral-50\"\n //           : \"bg-neutral-100 hover:bg-neutral-200\",\n //         isOver && \"bg-blue-50 border-blue-200\"\n //       )}\n //     >\n //       {children}\n //     </div>\n //   );\n // };\n // // Helper function to check if an event affects a specific day\n // const eventAffectsDay = (event: CalendarEvent, day: Date): boolean => {\n //   const eventStart = new Date(event.start);\n //   const eventEnd = new Date(event.end);\n //   const dayStart = new Date(day);\n //   const dayEnd = new Date(day);\n //   dayEnd.setHours(23, 59, 59, 999);\n //   return eventStart <= dayEnd && eventEnd >= dayStart;\n // };\n // // New helper function to process events for the entire month with proper slot tracking\n // const useMonthEvents = (weeks: Date[][], events: CalendarEvent[]) => {\n //   return useMemo(() => {\n //     const positionedEventsByWeek = new Map<number, any[]>();\n //     const slotUsageByDay = new Map<string, number>(); // Track slot usage per day\n //     weeks.forEach((week, weekIndex) => {\n //       const weekStart = week[0];\n //       const weekEnd = week[6];\n //       // Get all events that intersect with this week\n //       const weekEvents = events.filter(event => {\n //         const eventStart = new Date(event.start);\n //         const eventEnd = new Date(event.end);\n //         return eventStart <= weekEnd && eventEnd >= weekStart;\n //       });\n //       const spanningEvents: any[] = [];\n //       weekEvents.forEach(event => {\n //         const eventStart = new Date(event.start);\n //         const eventEnd = new Date(event.end);\n //         // Find which days this event spans in this week\n //         const startDayIndex = week.findIndex(day => isSameDay(day, eventStart));\n //         const endDayIndex = week.findIndex(day => isSameDay(day, eventEnd));\n //         // Calculate actual span within this week\n //         let actualStart = 0;\n //         let actualEnd = 6;\n //         if (startDayIndex !== -1) {\n //           actualStart = startDayIndex;\n //         }\n //         if (endDayIndex !== -1) {\n //           actualEnd = endDayIndex;\n //         }\n //         // Check if event intersects with this week\n //         const eventSpansWeek = startDayIndex !== -1 || endDayIndex !== -1 || (eventStart < weekStart && eventEnd > weekEnd);\n //         if (eventSpansWeek) {\n //           spanningEvents.push({\n //             event,\n //             startDayIndex: actualStart,\n //             endDayIndex: actualEnd,\n //             colSpan: actualEnd - actualStart + 1,\n //           });\n //         }\n //       });\n //       // Sort events by start day, then by span length (longer events first)\n //       const sortedEvents = spanningEvents.sort((a, b) => {\n //         if (a.startDayIndex !== b.startDayIndex) {\n //           return a.startDayIndex - b.startDayIndex;\n //         }\n //         return b.colSpan - a.colSpan;\n //       });\n //       // Position events and track slot usage\n //       const positioned: any[] = [];\n //       const rows: any[][] = [];\n //       sortedEvents.forEach(eventData => {\n //         // Check if this event can be placed (all days it spans have available slots)\n //         const affectedDays = week.slice(eventData.startDayIndex, eventData.endDayIndex + 1);\n //         const canPlace = affectedDays.every(day => {\n //           const dayKey = format(day, 'yyyy-MM-dd');\n //           const currentUsage = slotUsageByDay.get(dayKey) || 0;\n //           return currentUsage < 4; // Maximum 4 slots per day\n //         });\n //         if (!canPlace) {\n //           // Event cannot be placed, skip it (it will be in the \"+more\" count)\n //           return;\n //         }\n //         // Find available row\n //         let assigned = false;\n //         for (let i = 0; i < rows.length; i++) {\n //           const row = rows[i];\n //           const hasConflict = row.some(existingEvent => \n //             eventData.startDayIndex <= existingEvent.endDayIndex && \n //             eventData.endDayIndex >= existingEvent.startDayIndex\n //           );\n //           if (!hasConflict) {\n //             row.push(eventData);\n //             positioned.push({ ...eventData, row: i });\n //             assigned = true;\n //             break;\n //           }\n //         }\n //         if (!assigned) {\n //           rows.push([eventData]);\n //           positioned.push({ ...eventData, row: rows.length - 1 });\n //         }\n //         // Update slot usage for all affected days\n //         affectedDays.forEach(day => {\n //           const dayKey = format(day, 'yyyy-MM-dd');\n //           const currentUsage = slotUsageByDay.get(dayKey) || 0;\n //           slotUsageByDay.set(dayKey, currentUsage + 1);\n //         });\n //       });\n //       positionedEventsByWeek.set(weekIndex, positioned);\n //     });\n //     return { positionedEventsByWeek, slotUsageByDay };\n //   }, [weeks, events]);\n // };\n // export const MonthView: React.FC<MonthViewProps> = ({\n //   selectedDate,\n //   events,\n //   selectedEvent,\n //   setSelectedEvent,\n //   setSelectedDate,\n //   openAddEventForm,\n //   canEditData,\n //   handleEventClick,\n //   activeDragData,\n // }) => {\n //   const maybeRecord = useMaybeRecord();\n //   const isInRecordTab = !!maybeRecord;\n //   // Memoize month calculations\n //   const monthCalculations = useMemo(() => {\n //     const monthStart = startOfMonth(selectedDate);\n //     const monthEnd = endOfMonth(selectedDate);\n //     const startDay = startOfWeek(monthStart, { weekStartsOn: 0 });\n //     const endDay = endOfWeek(monthEnd, { weekStartsOn: 0 });\n //     const days = [];\n //     let day = startDay;\n //     while (day <= endDay) {\n //       days.push(day);\n //       day = addDays(day, 1);\n //     }\n //     const weeks = [];\n //     for (let i = 0; i < days.length; i += 7) {\n //       weeks.push(days.slice(i, i + 7));\n //     }\n //     return { monthStart, monthEnd, startDay, endDay, days, weeks };\n //   }, [selectedDate]);\n //   // Memoize month events\n //   const monthEvents = useMemo(() => \n //     events.filter(event => {\n //       const eventStart = new Date(event.start);\n //       const eventEnd = new Date(event.end);\n //       return eventStart <= monthCalculations.endDay && \n //              eventEnd >= monthCalculations.startDay;\n //     }), \n //     [events, monthCalculations.startDay, monthCalculations.endDay]\n //   );\n //   const { positionedEventsByWeek, slotUsageByDay } = useMonthEvents(monthCalculations.weeks, monthEvents);\n //   // Render empty state when no events\n //   const renderEmptyState = () => (\n //     <div className=\"flex flex-col h-full bg-background\">\n //       <div className=\"grid grid-cols-7 border-b border-neutral-300 bg-white\">\n //         {['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'].map(dayName => (\n //           <div key={dayName} className={cn(\n //             \"text-center font-semibold text-black\",\n //             \"py-2 text-xs\"\n //           )}>\n //             {dayName.substring(0, 3)}\n //           </div>\n //         ))}\n //       </div>\n //       <NoEvents\n //         title=\"No events this month\"\n //         message={`${format(selectedDate, 'MMMM yyyy')} is completely free. Start planning your month!`}\n //         showCreateButton={canEditData}\n //         onCreate={() => openAddEventForm(selectedDate)}\n //       />\n //     </div>\n //   );\n //   // Render day cell content\n //   const renderDayCellContent = (day: Date, dayEvents: any[]) => {\n //     const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n //     const isCurrentDay = isToday(day);\n //     const MAX_VISIBLE_EVENTS = 4;\n //     const ROW_HEIGHT = 28;\n //     const dayKey = format(day, 'yyyy-MM-dd');\n //     // Get all events that affect this day (for the +more count)\n //     const allDayEvents = monthEvents.filter(event => eventAffectsDay(event, day));\n //     const totalEventsForDay = allDayEvents.length;\n //     const sortedEvents = dayEvents.sort((a, b) => a.row - b.row);\n //     const visibleEvents = sortedEvents.slice(0, MAX_VISIBLE_EVENTS);\n //     const hasMore = totalEventsForDay > MAX_VISIBLE_EVENTS;\n //     const hiddenEventsCount = Math.max(0, totalEventsForDay - MAX_VISIBLE_EVENTS);\n //     // Calculate container height\n //     const maxRow = visibleEvents.reduce((max, event) => Math.max(max, event.row), -1);\n //     const containerHeight = hasMore\n //       ? (MAX_VISIBLE_EVENTS * ROW_HEIGHT) + 20 \n //       : (maxRow + 1) * ROW_HEIGHT;\n //     return (\n //       <>\n //         <div className=\"flex items-center justify-between mb-2\">\n //           <span className={cn(\n //             \"inline-flex items-center justify-center rounded-full font-semibold text-xs w-6 h-6\",\n //             isCurrentDay ? \"bg-black text-white\" : isCurrentMonth ? \"text-black hover:bg-neutral-100\" : \"text-neutral-400\"\n //           )}>\n //             {format(day, 'd')}\n //           </span>\n //           {canEditData && isCurrentMonth && (\n //             <Button\n //               variant=\"ghost\"\n //               className=\"rounded-full h-6 w-6 p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-neutral-100 hidden lg:inline-flex\"\n //               onClick={(e) => {\n //                 e.stopPropagation();\n //                 openAddEventForm(day);\n //               }}\n //             >\n //               <PlusIcon className=\"h-3 w-3 text-black\" />\n //             </Button>\n //           )}\n //         </div>\n //         <div className=\"relative\" style={{ height: `${containerHeight}px` }}>\n //           {visibleEvents.map(pe => (\n //             <div\n //               key={pe.event.id}\n //               className=\"absolute\"\n //               style={{\n //                 top: `${pe.row * ROW_HEIGHT}px`,\n //                 left: '2px',\n //                 width: pe.colSpan > 1 \n //                   ? `calc(${pe.colSpan * 100}% + ${(pe.colSpan - 1) * 19}px)`\n //                   : 'calc(100% - 4px)',\n //                 zIndex: 10 + pe.row,\n //               }}\n //             >\n //               <CalendarEventItem\n //                 event={pe.event}\n //                 view=\"month\"\n //                 onClick={(e) => {\n //                   e.stopPropagation();\n //                   setSelectedEvent(pe.event.id);\n //                   handleEventClick(pe.event);\n //                 }}\n //                 isDragging={activeDragData?.payload?.id === pe.event.id}\n //               />\n //             </div>\n //           ))}\n //           {hasMore && (\n //             <div \n //               className=\"text-black hover:text-black font-medium text-xs cursor-pointer\"\n //               style={{\n //                 position: 'absolute',\n //                 top: `${MAX_VISIBLE_EVENTS * ROW_HEIGHT}px`,\n //                 left: '2px',\n //               }}\n //               onClick={(e) => {\n //                 e.stopPropagation();\n //                 setSelectedDate(day);\n //               }}\n //             >\n //               + {hiddenEventsCount} more\n //             </div>\n //           )}\n //         </div>\n //       </>\n //     );\n //   };\n //   // Render main view\n //   return (\n //     <div className=\"h-full bg-background flex flex-col lg:flex-row\">\n //       <div className=\"flex-1 flex flex-col min-h-0\">\n //         {/* Day Headers */}\n //         <div \n //           data-day-headers=\"true\"\n //           className=\"grid grid-cols-7 border-b border-neutral-300 sticky top-0 z-10 bg-white\"\n //         >\n //           {['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'].map(dayName => (\n //             <div key={dayName} className={cn(\n //               \"text-center font-semibold text-black\",\n //               \"py-2 text-xs\"\n //             )}>\n //               {dayName.substring(0, 3)}\n //             </div>\n //           ))}\n //         </div>\n //         {/* Month Grid */}\n //         <ScrollArea className=\"flex-1\">\n //           <div className=\"grid grid-cols-7 border-neutral-300 border-b\">\n //             {monthCalculations.weeks.map((week, weekIndex) =>\n //               week.map((day, dayIndex) => {\n //                 const weekEvents = positionedEventsByWeek.get(weekIndex) || [];\n //                 const dayEvents = weekEvents.filter(pe => pe.startDayIndex === dayIndex);\n //                 const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n //                 return (\n //                   <DayCell\n //                     key={`${weekIndex}-${dayIndex}`}\n //                     date={day}\n //                     isCurrentMonth={isCurrentMonth}\n //                     onClick={() => setSelectedDate(day)}\n //                   >\n //                     {renderDayCellContent(day, dayEvents)}\n //                   </DayCell>\n //                 );\n //               }),\n //             )}\n //           </div>\n //         </ScrollArea>\n //       </div>\n //       <CalendarSideCard\n //         selectedDate={selectedDate}\n //         events={events}\n //         selectedEvent={selectedEvent}\n //         setSelectedEvent={setSelectedEvent}\n //         handleEventClick={handleEventClick}\n //       />\n //     </div>\n //   );\n // };\n // function isMultiDay(event: CalendarEvent): boolean {\n //   return !isSameDay(new Date(event.start), new Date(event.end));\n // }\nvar _c, _c1;\n$RefreshReg$(_c, \"DayCell\");\n$RefreshReg$(_c1, \"MonthView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3dvcmtzcGFjZS9tYWluL3ZpZXdzL2NhbGVuZGFyL2NvbXBvbmVudHMvTW9udGhWaWV3LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUd1QztBQUMwRTtBQUNoRjtBQUNlO0FBQ087QUFDRTtBQUVMO0FBQ0k7QUFDbEI7QUFDZ0I7QUFDVDtBQWU3QyxNQUFNbUIsVUFBVTtRQUFDLEVBQ2ZDLElBQUksRUFDSkMsUUFBUSxFQUNSQyxPQUFPLEVBQ1BDLGNBQWMsRUFNZjs7SUFDQyxNQUFNLEVBQUVDLFVBQVUsRUFBRUMsTUFBTSxFQUFFLEdBQUdQLDJEQUFZQSxDQUFDO1FBQzFDUSxJQUFJLFdBQXNDLE9BQTNCeEIsMkpBQU1BLENBQUNrQixNQUFNO1FBQzVCTyxNQUFNO1lBQ0pQLE1BQU1BO1lBQ05RLE1BQU07UUFDUjtJQUNGO0lBRUEscUJBQ0UsOERBQUNDO1FBQ0NDLEtBQUtOO1FBQ0xGLFNBQVNBO1FBQ1RTLFdBQVdyQiw4Q0FBRUEsQ0FDWCxnSkFDQWEsaUJBQ0ksaUNBQ0EsdUNBQ0pFLFVBQVU7a0JBR1hKOzs7Ozs7QUFHUDtHQWxDTUY7O1FBVzJCRCx1REFBWUE7OztLQVh2Q0M7QUFvQ04sOERBQThEO0FBQzlELE1BQU1hLGtCQUFrQixDQUFDQyxPQUFzQkM7SUFDN0MsTUFBTUMsYUFBYSxJQUFJQyxLQUFLSCxNQUFNSSxLQUFLO0lBQ3ZDLE1BQU1DLFdBQVcsSUFBSUYsS0FBS0gsTUFBTU0sR0FBRztJQUNuQyxNQUFNQyxXQUFXLElBQUlKLEtBQUtGO0lBQzFCLE1BQU1PLFNBQVMsSUFBSUwsS0FBS0Y7SUFDeEJPLE9BQU9DLFFBQVEsQ0FBQyxJQUFJLElBQUksSUFBSTtJQUU1QixPQUFPUCxjQUFjTSxVQUFVSCxZQUFZRTtBQUM3QztBQUVBLHVGQUF1RjtBQUN2RixNQUFNRyxpQkFBaUIsQ0FBQ0MsT0FBaUJDOztJQUN2QyxPQUFPNUMsOENBQU9BLENBQUM7UUFDYixNQUFNNkMseUJBQXlCLElBQUlDO1FBQ25DLE1BQU1DLGlCQUFpQixJQUFJRCxPQUF1QiwyQkFBMkI7UUFFN0VILE1BQU1LLE9BQU8sQ0FBQyxDQUFDQyxNQUFNQztZQUNuQixNQUFNQyxZQUFZRixJQUFJLENBQUMsRUFBRTtZQUN6QixNQUFNRyxVQUFVSCxJQUFJLENBQUMsRUFBRTtZQUV2QiwrQ0FBK0M7WUFDL0MsTUFBTUksYUFBYVQsT0FBT1UsTUFBTSxDQUFDdEIsQ0FBQUE7Z0JBQy9CLE1BQU1FLGFBQWEsSUFBSUMsS0FBS0gsTUFBTUksS0FBSztnQkFDdkMsTUFBTUMsV0FBVyxJQUFJRixLQUFLSCxNQUFNTSxHQUFHO2dCQUNuQyxPQUFPSixjQUFja0IsV0FBV2YsWUFBWWM7WUFDOUM7WUFFQSxNQUFNSSxpQkFBd0IsRUFBRTtZQUNoQ0YsV0FBV0wsT0FBTyxDQUFDaEIsQ0FBQUE7Z0JBQ2pCLE1BQU1FLGFBQWEsSUFBSUMsS0FBS0gsTUFBTUksS0FBSztnQkFDdkMsTUFBTUMsV0FBVyxJQUFJRixLQUFLSCxNQUFNTSxHQUFHO2dCQUVuQyxnREFBZ0Q7Z0JBQ2hELE1BQU1rQixnQkFBZ0JQLEtBQUtRLFNBQVMsQ0FBQ3hCLENBQUFBLE1BQU8xQiwySkFBU0EsQ0FBQzBCLEtBQUtDO2dCQUMzRCxNQUFNd0IsY0FBY1QsS0FBS1EsU0FBUyxDQUFDeEIsQ0FBQUEsTUFBTzFCLDJKQUFTQSxDQUFDMEIsS0FBS0k7Z0JBRXpELHlDQUF5QztnQkFDekMsSUFBSXNCLGNBQWM7Z0JBQ2xCLElBQUlDLFlBQVk7Z0JBRWhCLHFDQUFxQztnQkFDckMsSUFBSUosa0JBQWtCLENBQUMsR0FBRztvQkFDeEJHLGNBQWNIO2dCQUNoQjtnQkFFQSxtQ0FBbUM7Z0JBQ25DLElBQUlFLGdCQUFnQixDQUFDLEdBQUc7b0JBQ3RCRSxZQUFZRjtnQkFDZDtnQkFFQSxrREFBa0Q7Z0JBQ2xELElBQUlGLGtCQUFrQixDQUFDLEtBQUtFLGdCQUFnQixDQUFDLEtBQUt4QixhQUFhaUIsYUFBYWQsV0FBV2UsU0FBUztvQkFDOUZPLGNBQWM7b0JBQ2RDLFlBQVk7Z0JBQ2Q7Z0JBRUEsMkNBQTJDO2dCQUMzQyxNQUFNQyxpQkFBaUJMLGtCQUFrQixDQUFDLEtBQUtFLGdCQUFnQixDQUFDLEtBQU14QixhQUFhaUIsYUFBYWQsV0FBV2U7Z0JBRTNHLElBQUlTLGdCQUFnQjtvQkFDbEJOLGVBQWVPLElBQUksQ0FBQzt3QkFDbEI5Qjt3QkFDQXdCLGVBQWVHO3dCQUNmRCxhQUFhRTt3QkFDYkcsU0FBU0gsWUFBWUQsY0FBYztvQkFDckM7Z0JBQ0Y7WUFDRjtZQUVBLHNFQUFzRTtZQUN0RSxNQUFNSyxlQUFlVCxlQUFlVSxJQUFJLENBQUMsQ0FBQ0MsR0FBR0M7Z0JBQzNDLElBQUlELEVBQUVWLGFBQWEsS0FBS1csRUFBRVgsYUFBYSxFQUFFO29CQUN2QyxPQUFPVSxFQUFFVixhQUFhLEdBQUdXLEVBQUVYLGFBQWE7Z0JBQzFDO2dCQUNBLE9BQU9XLEVBQUVKLE9BQU8sR0FBR0csRUFBRUgsT0FBTztZQUM5QjtZQUVBLHVDQUF1QztZQUN2QyxNQUFNSyxhQUFvQixFQUFFO1lBQzVCLE1BQU1DLE9BQWdCLEVBQUU7WUFFeEJMLGFBQWFoQixPQUFPLENBQUNzQixDQUFBQTtnQkFDbkIsNkVBQTZFO2dCQUM3RSxNQUFNQyxlQUFldEIsS0FBS3VCLEtBQUssQ0FBQ0YsVUFBVWQsYUFBYSxFQUFFYyxVQUFVWixXQUFXLEdBQUc7Z0JBQ2pGLE1BQU1lLFdBQVdGLGFBQWFHLEtBQUssQ0FBQ3pDLENBQUFBO29CQUNsQyxNQUFNMEMsU0FBUzFFLDJKQUFNQSxDQUFDZ0MsS0FBSztvQkFDM0IsTUFBTTJDLGVBQWU3QixlQUFlOEIsR0FBRyxDQUFDRixXQUFXO29CQUNuRCxPQUFPQyxlQUFlLEdBQUcsMEJBQTBCO2dCQUNyRDtnQkFFQSxJQUFJLENBQUNILFVBQVU7b0JBQ2Isb0VBQW9FO29CQUNwRTtnQkFDRjtnQkFFQSxxQkFBcUI7Z0JBQ3JCLElBQUlLLFdBQVc7Z0JBQ2YsSUFBSyxJQUFJQyxJQUFJLEdBQUdBLElBQUlWLEtBQUtXLE1BQU0sRUFBRUQsSUFBSztvQkFDcEMsTUFBTUUsTUFBTVosSUFBSSxDQUFDVSxFQUFFO29CQUNuQixNQUFNRyxjQUFjRCxJQUFJRSxJQUFJLENBQUNDLENBQUFBLGdCQUMzQmQsVUFBVWQsYUFBYSxJQUFJNEIsY0FBYzFCLFdBQVcsSUFDcERZLFVBQVVaLFdBQVcsSUFBSTBCLGNBQWM1QixhQUFhO29CQUd0RCxJQUFJLENBQUMwQixhQUFhO3dCQUNoQkQsSUFBSW5CLElBQUksQ0FBQ1E7d0JBQ1RGLFdBQVdOLElBQUksQ0FBQzs0QkFBRSxHQUFHUSxTQUFTOzRCQUFFVyxLQUFLRjt3QkFBRTt3QkFDdkNELFdBQVc7d0JBQ1g7b0JBQ0Y7Z0JBQ0Y7Z0JBRUEsSUFBSSxDQUFDQSxVQUFVO29CQUNiVCxLQUFLUCxJQUFJLENBQUM7d0JBQUNRO3FCQUFVO29CQUNyQkYsV0FBV04sSUFBSSxDQUFDO3dCQUFFLEdBQUdRLFNBQVM7d0JBQUVXLEtBQUtaLEtBQUtXLE1BQU0sR0FBRztvQkFBRTtnQkFDdkQ7Z0JBRUEsMENBQTBDO2dCQUMxQ1QsYUFBYXZCLE9BQU8sQ0FBQ2YsQ0FBQUE7b0JBQ25CLE1BQU0wQyxTQUFTMUUsMkpBQU1BLENBQUNnQyxLQUFLO29CQUMzQixNQUFNMkMsZUFBZTdCLGVBQWU4QixHQUFHLENBQUNGLFdBQVc7b0JBQ25ENUIsZUFBZXNDLEdBQUcsQ0FBQ1YsUUFBUUMsZUFBZTtnQkFDNUM7WUFDRjtZQUVBL0IsdUJBQXVCd0MsR0FBRyxDQUFDbkMsV0FBV2tCO1FBQ3hDO1FBRUEsT0FBTztZQUFFdkI7WUFBd0JFO1FBQWU7SUFDbEQsR0FBRztRQUFDSjtRQUFPQztLQUFPO0FBQ3BCO0lBdkhNRjtBQXlIQyxNQUFNNEMsWUFBc0M7UUFBQyxFQUNsREMsWUFBWSxFQUNaM0MsTUFBTSxFQUNONEMsYUFBYSxFQUNiQyxnQkFBZ0IsRUFDaEJDLGVBQWUsRUFDZkMsZ0JBQWdCLEVBQ2hCQyxXQUFXLEVBQ1hDLGdCQUFnQixFQUNoQkMsY0FBYyxFQUNmOztJQUNDLE1BQU1DLGNBQWNsRixpRUFBY0E7SUFFbEMsTUFBTW1GLGdCQUFnQixDQUFDLENBQUNEO0lBRXhCLDZCQUE2QjtJQUM3QixNQUFNRSxvQkFBb0JqRyw4Q0FBT0EsQ0FBQztRQUNoQyxNQUFNa0csYUFBYWhHLDJKQUFZQSxDQUFDcUY7UUFDaEMsTUFBTVksV0FBV2hHLDJKQUFVQSxDQUFDb0Y7UUFDNUIsTUFBTWEsV0FBV2hHLDJKQUFXQSxDQUFDOEYsWUFBWTtZQUFFRyxjQUFjO1FBQUU7UUFDM0QsTUFBTUMsU0FBU2pHLDJKQUFTQSxDQUFDOEYsVUFBVTtZQUFFRSxjQUFjO1FBQUU7UUFFckQsTUFBTUUsT0FBTyxFQUFFO1FBQ2YsSUFBSXRFLE1BQU1tRTtRQUNWLE1BQU9uRSxPQUFPcUUsT0FBUTtZQUNwQkMsS0FBS3pDLElBQUksQ0FBQzdCO1lBQ1ZBLE1BQU0zQiwySkFBT0EsQ0FBQzJCLEtBQUs7UUFDckI7UUFFQSxNQUFNVSxRQUFRLEVBQUU7UUFDaEIsSUFBSyxJQUFJb0MsSUFBSSxHQUFHQSxJQUFJd0IsS0FBS3ZCLE1BQU0sRUFBRUQsS0FBSyxFQUFHO1lBQ3ZDcEMsTUFBTW1CLElBQUksQ0FBQ3lDLEtBQUsvQixLQUFLLENBQUNPLEdBQUdBLElBQUk7UUFDL0I7UUFFQSxPQUFPO1lBQUVtQjtZQUFZQztZQUFVQztZQUFVRTtZQUFRQztZQUFNNUQ7UUFBTTtJQUMvRCxHQUFHO1FBQUM0QztLQUFhO0lBRWpCLHVCQUF1QjtJQUN2QixNQUFNaUIsY0FBY3hHLDhDQUFPQSxDQUFDLElBQzFCNEMsT0FBT1UsTUFBTSxDQUFDdEIsQ0FBQUE7WUFDWixNQUFNRSxhQUFhLElBQUlDLEtBQUtILE1BQU1JLEtBQUs7WUFDdkMsTUFBTUMsV0FBVyxJQUFJRixLQUFLSCxNQUFNTSxHQUFHO1lBQ25DLE9BQU9KLGNBQWMrRCxrQkFBa0JLLE1BQU0sSUFDdENqRSxZQUFZNEQsa0JBQWtCRyxRQUFRO1FBQy9DLElBQ0E7UUFBQ3hEO1FBQVFxRCxrQkFBa0JHLFFBQVE7UUFBRUgsa0JBQWtCSyxNQUFNO0tBQUM7SUFHaEUsTUFBTSxFQUFFekQsc0JBQXNCLEVBQUVFLGNBQWMsRUFBRSxHQUFHTCxlQUFldUQsa0JBQWtCdEQsS0FBSyxFQUFFNkQ7SUFFM0Ysb0NBQW9DO0lBQ3BDLE1BQU1DLG1CQUFtQixrQkFDdkIsOERBQUM3RTtZQUFJRSxXQUFVOzs4QkFDYiw4REFBQ0Y7b0JBQUlFLFdBQVU7OEJBQ1o7d0JBQUM7d0JBQVU7d0JBQVU7d0JBQVc7d0JBQWE7d0JBQVk7d0JBQVU7cUJBQVcsQ0FBQzRFLEdBQUcsQ0FBQ0MsQ0FBQUEsd0JBQ2xGLDhEQUFDL0U7NEJBQWtCRSxXQUFXckIsOENBQUVBLENBQzlCLHdDQUNBO3NDQUVDa0csUUFBUUMsU0FBUyxDQUFDLEdBQUc7MkJBSmREOzs7Ozs7Ozs7OzhCQVNkLDhEQUFDNUYsK0NBQVFBO29CQUNQOEYsT0FBTTtvQkFDTkMsU0FBUyxHQUFxQyxPQUFsQzdHLDJKQUFNQSxDQUFDc0YsY0FBYyxjQUFhO29CQUM5Q3dCLGtCQUFrQm5CO29CQUNsQm9CLFVBQVUsSUFBTXJCLGlCQUFpQko7Ozs7Ozs7Ozs7OztJQUt2QywwQkFBMEI7SUFDMUIsTUFBTTBCLHVCQUF1QixDQUFDaEYsS0FBV2lGO1FBQ3ZDLE1BQU01RixpQkFBaUJXLElBQUlrRixRQUFRLE9BQU81QixhQUFhNEIsUUFBUTtRQUMvRCxNQUFNQyxlQUFlNUcsMkpBQU9BLENBQUN5QjtRQUM3QixNQUFNb0YscUJBQXFCO1FBQzNCLE1BQU1DLGFBQWE7UUFFbkIsTUFBTTNDLFNBQVMxRSwySkFBTUEsQ0FBQ2dDLEtBQUs7UUFFM0IsNERBQTREO1FBQzVELE1BQU1zRixlQUFlZixZQUFZbEQsTUFBTSxDQUFDdEIsQ0FBQUEsUUFBU0QsZ0JBQWdCQyxPQUFPQztRQUN4RSxNQUFNdUYsb0JBQW9CRCxhQUFhdkMsTUFBTTtRQUU3QyxNQUFNaEIsZUFBZWtELFVBQVVqRCxJQUFJLENBQUMsQ0FBQ0MsR0FBR0MsSUFBTUQsRUFBRWUsR0FBRyxHQUFHZCxFQUFFYyxHQUFHO1FBQzNELE1BQU13QyxnQkFBZ0J6RCxhQUFhUSxLQUFLLENBQUMsR0FBRzZDO1FBQzVDLE1BQU1LLFVBQVVGLG9CQUFvQkg7UUFDcEMsTUFBTU0sb0JBQW9CQyxLQUFLQyxHQUFHLENBQUMsR0FBR0wsb0JBQW9CSDtRQUUxRCw2QkFBNkI7UUFDN0IsTUFBTVMsU0FBU0wsY0FBY00sTUFBTSxDQUFDLENBQUNGLEtBQUs3RixRQUFVNEYsS0FBS0MsR0FBRyxDQUFDQSxLQUFLN0YsTUFBTWlELEdBQUcsR0FBRyxDQUFDO1FBQy9FLE1BQU0rQyxrQkFBa0JOLFVBQ3BCLHFCQUFzQkosYUFBYyxLQUNwQyxDQUFDUSxTQUFTLEtBQUtSO1FBRW5CLHFCQUNFOzs4QkFDRSw4REFBQzFGO29CQUFJRSxXQUFVOztzQ0FDYiw4REFBQ21HOzRCQUFLbkcsV0FBV3JCLDhDQUFFQSxDQUNqQixzRkFDQTJHLGVBQWUsd0JBQXdCOUYsaUJBQWlCLG9DQUFvQztzQ0FFM0ZyQiwySkFBTUEsQ0FBQ2dDLEtBQUs7Ozs7Ozt3QkFFZDJELGVBQWV0RSxnQ0FDZCw4REFBQ1oseURBQU1BOzRCQUNMd0gsU0FBUTs0QkFDUnBHLFdBQVU7NEJBQ1ZULFNBQVMsQ0FBQzhHO2dDQUNSQSxFQUFFQyxlQUFlO2dDQUNqQkMsV0FBVyxJQUFNMUMsaUJBQWlCMUQsTUFBTTs0QkFDMUM7c0NBRUEsNEVBQUN0QixtR0FBUUE7Z0NBQUNtQixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFLMUIsOERBQUNGO29CQUFJRSxXQUFVO29CQUFXd0csT0FBTzt3QkFBRUMsUUFBUSxHQUFtQixPQUFoQlAsaUJBQWdCO29CQUFJOzt3QkFDL0RQLGNBQWNmLEdBQUcsQ0FBQzhCLENBQUFBO2dDQXFCRDFDO2lEQXBCaEIsOERBQUNsRTtnQ0FFQ0UsV0FBVTtnQ0FDVndHLE9BQU87b0NBQ0xHLEtBQUssR0FBdUIsT0FBcEJELEdBQUd2RCxHQUFHLEdBQUdxQyxZQUFXO29DQUM1Qm9CLE1BQU07b0NBQ05DLE9BQU9ILEdBQUd6RSxPQUFPLEdBQUcsSUFDaEIsUUFBK0IsT0FBdkJ5RSxHQUFHekUsT0FBTyxHQUFHLEtBQUksUUFBNEIsT0FBdEIsQ0FBQ3lFLEdBQUd6RSxPQUFPLEdBQUcsS0FBSyxJQUFHLFNBQ3JEO29DQUNKNkUsUUFBUSxLQUFLSixHQUFHdkQsR0FBRztnQ0FDckI7MENBRUEsNEVBQUNuRSxpRUFBaUJBO29DQUNoQmtCLE9BQU93RyxHQUFHeEcsS0FBSztvQ0FDZjZHLE1BQUs7b0NBQ0x4SCxTQUFTLENBQUM4Rzt3Q0FDUkEsRUFBRUMsZUFBZTt3Q0FDakIzQyxpQkFBaUIrQyxHQUFHeEcsS0FBSyxDQUFDUCxFQUFFO3dDQUM1Qm9FLGlCQUFpQjJDLEdBQUd4RyxLQUFLO29DQUMzQjtvQ0FDQThHLFlBQVloRCxDQUFBQSwyQkFBQUEsc0NBQUFBLDBCQUFBQSxlQUFnQmlELE9BQU8sY0FBdkJqRCw4Q0FBQUEsd0JBQXlCckUsRUFBRSxNQUFLK0csR0FBR3hHLEtBQUssQ0FBQ1AsRUFBRTs7Ozs7OytCQW5CcEQrRyxHQUFHeEcsS0FBSyxDQUFDUCxFQUFFOzs7Ozs7d0JBd0JuQmlHLHlCQUNDLDhEQUFDOUY7NEJBQ0NFLFdBQVU7NEJBQ1Z3RyxPQUFPO2dDQUNMVSxVQUFVO2dDQUNWUCxLQUFLLEdBQW1DLE9BQWhDcEIscUJBQXFCQyxZQUFXO2dDQUN4Q29CLE1BQU07NEJBQ1I7NEJBQ0FySCxTQUFTLENBQUM4RztnQ0FDUkEsRUFBRUMsZUFBZTtnQ0FDakIxQyxnQkFBZ0J6RDs0QkFDbEI7O2dDQUNEO2dDQUNJMEY7Z0NBQWtCOzs7Ozs7Ozs7Ozs7Ozs7SUFNakM7SUFFQSxtQkFBbUI7SUFDbkIscUJBQ0UsOERBQUMvRjtRQUFJRSxXQUFVOzswQkFDYiw4REFBQ0Y7Z0JBQUlFLFdBQVU7O2tDQUViLDhEQUFDRjt3QkFDQ3FILG9CQUFpQjt3QkFDakJuSCxXQUFVO2tDQUVUOzRCQUFDOzRCQUFVOzRCQUFVOzRCQUFXOzRCQUFhOzRCQUFZOzRCQUFVO3lCQUFXLENBQUM0RSxHQUFHLENBQUNDLENBQUFBLHdCQUNsRiw4REFBQy9FO2dDQUFrQkUsV0FBV3JCLDhDQUFFQSxDQUM5Qix3Q0FDQTswQ0FFQ2tHLFFBQVFDLFNBQVMsQ0FBQyxHQUFHOytCQUpkRDs7Ozs7Ozs7OztrQ0FVZCw4REFBQy9GLGtFQUFVQTt3QkFBQ2tCLFdBQVU7a0NBQ3BCLDRFQUFDRjs0QkFBSUUsV0FBVTtzQ0FDWm1FLGtCQUFrQnRELEtBQUssQ0FBQytELEdBQUcsQ0FBQyxDQUFDekQsTUFBTUMsWUFDbENELEtBQUt5RCxHQUFHLENBQUMsQ0FBQ3pFLEtBQUtpSDtvQ0FDYixNQUFNN0YsYUFBYVIsdUJBQXVCZ0MsR0FBRyxDQUFDM0IsY0FBYyxFQUFFO29DQUM5RCw2REFBNkQ7b0NBQzdELE1BQU1nRSxZQUFZN0QsV0FBV0MsTUFBTSxDQUFDa0YsQ0FBQUEsS0FDbENBLEdBQUdoRixhQUFhLEtBQUswRixZQUNwQlYsR0FBR2hGLGFBQWEsR0FBRzBGLFlBQVlWLEdBQUc5RSxXQUFXLElBQUl3RjtvQ0FFcEQsTUFBTTVILGlCQUFpQlcsSUFBSWtGLFFBQVEsT0FBTzVCLGFBQWE0QixRQUFRO29DQUUvRCxxQkFDRSw4REFBQ2pHO3dDQUVDQyxNQUFNYzt3Q0FDTlgsZ0JBQWdCQTt3Q0FDaEJELFNBQVMsSUFBTXFFLGdCQUFnQnpEO2tEQUU5QmdGLHFCQUFxQmhGLEtBQUtpRjt1Q0FMdEIsR0FBZ0JnQyxPQUFiaEcsV0FBVSxLQUFZLE9BQVRnRzs7Ozs7Z0NBUTNCOzs7Ozs7Ozs7Ozs7Ozs7OzswQkFNUiw4REFBQ2xJLCtEQUFnQkE7Z0JBQ2Z1RSxjQUFjQTtnQkFDZDNDLFFBQVFBO2dCQUNSNEMsZUFBZUE7Z0JBQ2ZDLGtCQUFrQkE7Z0JBQ2xCSSxrQkFBa0JBOzs7Ozs7Ozs7Ozs7QUFJMUIsRUFBRTtJQWpPV1A7O1FBV1N6RSw2REFBY0E7UUFxQ2lCNkI7OztNQWhEeEM0QztBQW1PYixTQUFTNkQsV0FBV25ILEtBQW9CO0lBQ3RDLE9BQU8sQ0FBQ3pCLDJKQUFTQSxDQUFDLElBQUk0QixLQUFLSCxNQUFNSSxLQUFLLEdBQUcsSUFBSUQsS0FBS0gsTUFBTU0sR0FBRztBQUM3RCxFQUNBLDBDQUEwQztDQUMxQyxvSEFBb0g7Q0FDcEgsb0NBQW9DO0NBQ3BDLG1EQUFtRDtDQUNuRCwwREFBMEQ7Q0FDMUQsNERBQTREO0NBQzVELGtEQUFrRDtDQUNsRCx1REFBdUQ7Q0FDdkQsMkRBQTJEO0NBQzNELHlDQUF5QztDQUN6Qyx5REFBeUQ7Q0FDekQsZ0RBQWdEO0NBR2hELDZCQUE2QjtDQUM3Qix3QkFBd0I7Q0FDeEIsNkJBQTZCO0NBQzdCLGtDQUFrQztDQUNsQyw0Q0FBNEM7Q0FDNUMsMkNBQTJDO0NBQzNDLDRDQUE0QztDQUM1QywwQkFBMEI7Q0FDMUIsc0RBQXNEO0NBQ3RELHlCQUF5QjtDQUN6QixJQUFJO0NBRUoscUJBQXFCO0NBQ3JCLFVBQVU7Q0FDVixjQUFjO0NBQ2QsYUFBYTtDQUNiLG1CQUFtQjtDQUNuQixPQUFPO0NBQ1AsZ0JBQWdCO0NBQ2hCLCtCQUErQjtDQUMvQix5QkFBeUI7Q0FDekIsNkJBQTZCO0NBQzdCLFVBQVU7Q0FDVixrREFBa0Q7Q0FDbEQsbURBQW1EO0NBQ25ELGNBQWM7Q0FDZCxvQkFBb0I7Q0FDcEIsd0JBQXdCO0NBQ3hCLFFBQVE7Q0FDUixRQUFRO0NBRVIsYUFBYTtDQUNiLFdBQVc7Q0FDWCx5QkFBeUI7Q0FDekIsMEJBQTBCO0NBQzFCLHVCQUF1QjtDQUN2QiwwSkFBMEo7Q0FDMUoseUJBQXlCO0NBQ3pCLDZDQUE2QztDQUM3QyxxREFBcUQ7Q0FDckQsaURBQWlEO0NBQ2pELFdBQVc7Q0FDWCxRQUFRO0NBQ1IsbUJBQW1CO0NBQ25CLGFBQWE7Q0FDYixPQUFPO0NBQ1AsS0FBSztDQUVMLGlFQUFpRTtDQUNqRSwwRUFBMEU7Q0FDMUUsOENBQThDO0NBQzlDLDBDQUEwQztDQUMxQyxvQ0FBb0M7Q0FDcEMsa0NBQWtDO0NBQ2xDLHNDQUFzQztDQUV0Qyx5REFBeUQ7Q0FDekQsS0FBSztDQUVMLDBGQUEwRjtDQUMxRix5RUFBeUU7Q0FDekUsMkJBQTJCO0NBQzNCLCtEQUErRDtDQUMvRCxvRkFBb0Y7Q0FFcEYsMkNBQTJDO0NBQzNDLG1DQUFtQztDQUNuQyxpQ0FBaUM7Q0FFakMsd0RBQXdEO0NBQ3hELG9EQUFvRDtDQUNwRCxvREFBb0Q7Q0FDcEQsZ0RBQWdEO0NBQ2hELGlFQUFpRTtDQUNqRSxZQUFZO0NBRVosMENBQTBDO0NBQzFDLHNDQUFzQztDQUN0QyxvREFBb0Q7Q0FDcEQsZ0RBQWdEO0NBRWhELDJEQUEyRDtDQUMzRCxtRkFBbUY7Q0FDbkYsK0VBQStFO0NBRS9FLG9EQUFvRDtDQUNwRCwrQkFBK0I7Q0FDL0IsNkJBQTZCO0NBRTdCLHNDQUFzQztDQUN0Qyx5Q0FBeUM7Q0FDekMsWUFBWTtDQUNaLG9DQUFvQztDQUNwQyxxQ0FBcUM7Q0FDckMsWUFBWTtDQUVaLHNEQUFzRDtDQUN0RCwrSEFBK0g7Q0FFL0gsZ0NBQWdDO0NBQ2hDLGtDQUFrQztDQUNsQyxxQkFBcUI7Q0FDckIsMENBQTBDO0NBQzFDLHNDQUFzQztDQUN0QyxvREFBb0Q7Q0FDcEQsZ0JBQWdCO0NBQ2hCLFlBQVk7Q0FDWixZQUFZO0NBRVosK0VBQStFO0NBQy9FLDZEQUE2RDtDQUM3RCxxREFBcUQ7Q0FDckQsc0RBQXNEO0NBQ3RELFlBQVk7Q0FDWix3Q0FBd0M7Q0FDeEMsWUFBWTtDQUVaLGdEQUFnRDtDQUNoRCxzQ0FBc0M7Q0FDdEMsa0NBQWtDO0NBRWxDLDRDQUE0QztDQUM1Qyx3RkFBd0Y7Q0FDeEYsK0ZBQStGO0NBQy9GLHVEQUF1RDtDQUN2RCxzREFBc0Q7Q0FDdEQsa0VBQWtFO0NBQ2xFLGdFQUFnRTtDQUNoRSxjQUFjO0NBRWQsMkJBQTJCO0NBQzNCLGlGQUFpRjtDQUNqRixvQkFBb0I7Q0FDcEIsWUFBWTtDQUVaLGdDQUFnQztDQUNoQyxnQ0FBZ0M7Q0FDaEMsa0RBQWtEO0NBQ2xELGlDQUFpQztDQUNqQywyREFBMkQ7Q0FDM0QsdUVBQXVFO0NBQ3ZFLG1FQUFtRTtDQUNuRSxlQUFlO0NBRWYsZ0NBQWdDO0NBQ2hDLG1DQUFtQztDQUNuQyx5REFBeUQ7Q0FDekQsK0JBQStCO0NBQy9CLHFCQUFxQjtDQUNyQixjQUFjO0NBQ2QsWUFBWTtDQUVaLDJCQUEyQjtDQUMzQixvQ0FBb0M7Q0FDcEMscUVBQXFFO0NBQ3JFLFlBQVk7Q0FFWixxREFBcUQ7Q0FDckQsd0NBQXdDO0NBQ3hDLHNEQUFzRDtDQUN0RCxrRUFBa0U7Q0FDbEUsMERBQTBEO0NBQzFELGNBQWM7Q0FDZCxZQUFZO0NBRVosMkRBQTJEO0NBQzNELFVBQVU7Q0FFVix5REFBeUQ7Q0FDekQseUJBQXlCO0NBQ3pCLEtBQUs7Q0FFTCx3REFBd0Q7Q0FDeEQsa0JBQWtCO0NBQ2xCLFlBQVk7Q0FDWixtQkFBbUI7Q0FDbkIsc0JBQXNCO0NBQ3RCLHFCQUFxQjtDQUNyQixzQkFBc0I7Q0FDdEIsaUJBQWlCO0NBQ2pCLHNCQUFzQjtDQUN0QixvQkFBb0I7Q0FDcEIsVUFBVTtDQUNWLDBDQUEwQztDQUUxQyx5Q0FBeUM7Q0FFekMsa0NBQWtDO0NBQ2xDLDhDQUE4QztDQUM5QyxxREFBcUQ7Q0FDckQsaURBQWlEO0NBQ2pELHFFQUFxRTtDQUNyRSwrREFBK0Q7Q0FFL0QsdUJBQXVCO0NBQ3ZCLDBCQUEwQjtDQUMxQiw4QkFBOEI7Q0FDOUIsd0JBQXdCO0NBQ3hCLCtCQUErQjtDQUMvQixRQUFRO0NBRVIsd0JBQXdCO0NBQ3hCLGlEQUFpRDtDQUNqRCwwQ0FBMEM7Q0FDMUMsUUFBUTtDQUVSLHNFQUFzRTtDQUN0RSx3QkFBd0I7Q0FFeEIsNEJBQTRCO0NBQzVCLHVDQUF1QztDQUN2QywrQkFBK0I7Q0FDL0Isa0RBQWtEO0NBQ2xELDhDQUE4QztDQUM5QywwREFBMEQ7Q0FDMUQsdURBQXVEO0NBQ3ZELFdBQVc7Q0FDWCxxRUFBcUU7Q0FDckUsT0FBTztDQUVQLDZHQUE2RztDQUU3Ryx5Q0FBeUM7Q0FDekMscUNBQXFDO0NBQ3JDLDJEQUEyRDtDQUMzRCxnRkFBZ0Y7Q0FDaEYsMkdBQTJHO0NBQzNHLDhDQUE4QztDQUM5QyxzREFBc0Q7Q0FDdEQsNkJBQTZCO0NBQzdCLGdCQUFnQjtDQUNoQix3Q0FBd0M7Q0FDeEMsbUJBQW1CO0NBQ25CLGNBQWM7Q0FDZCxlQUFlO0NBRWYsa0JBQWtCO0NBQ2xCLHVDQUF1QztDQUN2QywwR0FBMEc7Q0FDMUcseUNBQXlDO0NBQ3pDLDBEQUEwRDtDQUMxRCxXQUFXO0NBQ1gsYUFBYTtDQUNiLE9BQU87Q0FFUCwrQkFBK0I7Q0FDL0Isb0VBQW9FO0NBQ3BFLHlFQUF5RTtDQUN6RSx5Q0FBeUM7Q0FDekMsb0NBQW9DO0NBQ3BDLDZCQUE2QjtDQUU3QixnREFBZ0Q7Q0FFaEQsbUVBQW1FO0NBQ25FLHFGQUFxRjtDQUNyRixxREFBcUQ7Q0FFckQsb0VBQW9FO0NBQ3BFLHVFQUF1RTtDQUN2RSw4REFBOEQ7Q0FDOUQscUZBQXFGO0NBRXJGLG9DQUFvQztDQUNwQyx5RkFBeUY7Q0FDekYsc0NBQXNDO0NBQ3RDLGtEQUFrRDtDQUNsRCxxQ0FBcUM7Q0FFckMsZUFBZTtDQUNmLFdBQVc7Q0FDWCxtRUFBbUU7Q0FDbkUsaUNBQWlDO0NBQ2pDLG9HQUFvRztDQUNwRyw2SEFBNkg7Q0FDN0gsZ0JBQWdCO0NBQ2hCLGlDQUFpQztDQUNqQyxvQkFBb0I7Q0FDcEIsZ0RBQWdEO0NBQ2hELHNCQUFzQjtDQUN0QixnQ0FBZ0M7Q0FDaEMscUpBQXFKO0NBQ3JKLGtDQUFrQztDQUNsQyx1Q0FBdUM7Q0FDdkMseUNBQXlDO0NBQ3pDLG1CQUFtQjtDQUNuQixnQkFBZ0I7Q0FDaEIsNERBQTREO0NBQzVELHdCQUF3QjtDQUN4QixlQUFlO0NBQ2YsaUJBQWlCO0NBRWpCLGdGQUFnRjtDQUNoRix1Q0FBdUM7Q0FDdkMsbUJBQW1CO0NBQ25CLGtDQUFrQztDQUNsQyxxQ0FBcUM7Q0FDckMseUJBQXlCO0NBQ3pCLG1EQUFtRDtDQUNuRCwrQkFBK0I7Q0FDL0IseUNBQXlDO0NBQ3pDLGdGQUFnRjtDQUNoRiwwQ0FBMEM7Q0FDMUMsdUNBQXVDO0NBQ3ZDLG1CQUFtQjtDQUNuQixnQkFBZ0I7Q0FDaEIsbUNBQW1DO0NBQ25DLG1DQUFtQztDQUNuQywrQkFBK0I7Q0FDL0Isb0NBQW9DO0NBQ3BDLHlDQUF5QztDQUN6QyxtREFBbUQ7Q0FDbkQsZ0RBQWdEO0NBQ2hELHFCQUFxQjtDQUNyQiwyRUFBMkU7Q0FDM0UsbUJBQW1CO0NBQ25CLHFCQUFxQjtDQUNyQixnQkFBZ0I7Q0FFaEIsMEJBQTBCO0NBQzFCLG9CQUFvQjtDQUNwQiwyRkFBMkY7Q0FDM0YseUJBQXlCO0NBQ3pCLHdDQUF3QztDQUN4QywrREFBK0Q7Q0FDL0QsK0JBQStCO0NBQy9CLG1CQUFtQjtDQUNuQixrQ0FBa0M7Q0FDbEMsdUNBQXVDO0NBQ3ZDLHdDQUF3QztDQUN4QyxtQkFBbUI7Q0FDbkIsZ0JBQWdCO0NBQ2hCLDJDQUEyQztDQUMzQyxxQkFBcUI7Q0FDckIsZUFBZTtDQUNmLGlCQUFpQjtDQUNqQixZQUFZO0NBQ1osU0FBUztDQUNULE9BQU87Q0FFUCx3QkFBd0I7Q0FDeEIsYUFBYTtDQUNiLHVFQUF1RTtDQUN2RSx1REFBdUQ7Q0FDdkQsOEJBQThCO0NBQzlCLGdCQUFnQjtDQUNoQixvQ0FBb0M7Q0FDcEMsZ0dBQWdHO0NBQ2hHLFlBQVk7Q0FDWiw2R0FBNkc7Q0FDN0csZ0RBQWdEO0NBQ2hELHdEQUF3RDtDQUN4RCwrQkFBK0I7Q0FDL0Isa0JBQWtCO0NBQ2xCLDBDQUEwQztDQUMxQyxxQkFBcUI7Q0FDckIsZ0JBQWdCO0NBQ2hCLGlCQUFpQjtDQUVqQiw2QkFBNkI7Q0FDN0IsMENBQTBDO0NBQzFDLDJFQUEyRTtDQUMzRSxnRUFBZ0U7Q0FDaEUsOENBQThDO0NBQzlDLGtGQUFrRjtDQUNsRiw0RkFBNEY7Q0FDNUYscUZBQXFGO0NBRXJGLDJCQUEyQjtDQUMzQiw2QkFBNkI7Q0FDN0IsdURBQXVEO0NBQ3ZELGlDQUFpQztDQUNqQyxzREFBc0Q7Q0FDdEQsMkRBQTJEO0NBQzNELHNCQUFzQjtDQUN0Qiw2REFBNkQ7Q0FDN0QsK0JBQStCO0NBQy9CLHFCQUFxQjtDQUNyQixvQkFBb0I7Q0FDcEIsaUJBQWlCO0NBQ2pCLG1CQUFtQjtDQUNuQix3QkFBd0I7Q0FDeEIsZUFBZTtDQUVmLDBCQUEwQjtDQUMxQixzQ0FBc0M7Q0FDdEMsMEJBQTBCO0NBQzFCLHdDQUF3QztDQUN4Qyw4Q0FBOEM7Q0FDOUMsOENBQThDO0NBQzlDLFdBQVc7Q0FDWCxhQUFhO0NBQ2IsT0FBTztDQUNQLEtBQUs7Q0FFTCx1REFBdUQ7Q0FDdkQsbUVBQW1FO0NBQ25FLElBQUkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvd29ya3NwYWNlL21haW4vdmlld3MvY2FsZW5kYXIvY29tcG9uZW50cy9Nb250aFZpZXcudHN4P2I0NWUiXSwic291cmNlc0NvbnRlbnQiOlsiXG5cblxuaW1wb3J0IFJlYWN0LCB7IHVzZU1lbW8gfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBmb3JtYXQsIHN0YXJ0T2ZNb250aCwgZW5kT2ZNb250aCwgc3RhcnRPZldlZWssIGVuZE9mV2VlaywgYWRkRGF5cywgaXNTYW1lRGF5LCBpc1RvZGF5IH0gZnJvbSAnZGF0ZS1mbnMnO1xuaW1wb3J0IHsgY24gfSBmcm9tICdAL2xpYi91dGlscyc7XG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJztcbmltcG9ydCB7IFBsdXNJY29uIH0gZnJvbSAnQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lJztcbmltcG9ydCB7IFNjcm9sbEFyZWEgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvc2Nyb2xsLWFyZWEnO1xuaW1wb3J0IHsgQ2FsZW5kYXJFdmVudCB9IGZyb20gJ0AvdHlwaW5ncy9wYWdlJztcbmltcG9ydCB7IHVzZU1heWJlUmVjb3JkIH0gZnJvbSAnQC9wcm92aWRlcnMvcmVjb3JkJztcbmltcG9ydCB7IENhbGVuZGFyRXZlbnRJdGVtIH0gZnJvbSAnLi9DYWxlbmRhckV2ZW50SXRlbSc7XG5pbXBvcnQgeyBOb0V2ZW50cyB9IGZyb20gJy4vTm9FdmVudHMnO1xuaW1wb3J0IHsgQ2FsZW5kYXJTaWRlQ2FyZCB9IGZyb20gJy4vQ2FsZW5kYXJTaWRlQ2FyZCc7XG5pbXBvcnQgeyB1c2VEcm9wcGFibGUgfSBmcm9tICdAZG5kLWtpdC9jb3JlJztcblxuXG5pbnRlcmZhY2UgTW9udGhWaWV3UHJvcHMge1xuICBzZWxlY3RlZERhdGU6IERhdGU7XG4gIGV2ZW50czogQ2FsZW5kYXJFdmVudFtdO1xuICBzZWxlY3RlZEV2ZW50OiBzdHJpbmcgfCBudWxsO1xuICBzZXRTZWxlY3RlZEV2ZW50OiAoaWQ6IHN0cmluZykgPT4gdm9pZDtcbiAgc2V0U2VsZWN0ZWREYXRlOiAoZGF0ZTogRGF0ZSkgPT4gdm9pZDtcbiAgb3BlbkFkZEV2ZW50Rm9ybTogKGRhdGU6IERhdGUpID0+IHZvaWQ7XG4gIGNhbkVkaXREYXRhOiBib29sZWFuO1xuICBoYW5kbGVFdmVudENsaWNrOiAoZXZlbnQ6IENhbGVuZGFyRXZlbnQpID0+IHZvaWQ7XG4gIGFjdGl2ZURyYWdEYXRhOiBhbnk7XG59XG5cbmNvbnN0IERheUNlbGwgPSAoe1xuICBkYXRlLFxuICBjaGlsZHJlbixcbiAgb25DbGljayxcbiAgaXNDdXJyZW50TW9udGhcbn06IHtcbiAgZGF0ZTogRGF0ZTtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbiAgb25DbGljazogKCkgPT4gdm9pZDtcbiAgaXNDdXJyZW50TW9udGg6IGJvb2xlYW47XG59KSA9PiB7XG4gIGNvbnN0IHsgc2V0Tm9kZVJlZiwgaXNPdmVyIH0gPSB1c2VEcm9wcGFibGUoe1xuICAgIGlkOiBgZGF5Y2VsbC0ke2Zvcm1hdChkYXRlLCAneXl5eS1NTS1kZCcpfWAsXG4gICAgZGF0YToge1xuICAgICAgZGF0ZTogZGF0ZSxcbiAgICAgIHR5cGU6ICdkYXljZWxsJ1xuICAgIH1cbiAgfSk7XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2XG4gICAgICByZWY9e3NldE5vZGVSZWZ9XG4gICAgICBvbkNsaWNrPXtvbkNsaWNrfVxuICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgXCJib3JkZXItYiBib3JkZXItciBib3JkZXItbmV1dHJhbC0zMDAgcmVsYXRpdmUgY3Vyc29yLXBvaW50ZXIgdHJhbnNpdGlvbi1jb2xvcnMgZ3JvdXAgZmxleCBmbGV4LWNvbCBwLTIgbWluLWgtWzEyMHB4XSBsZzpwLTMgbGc6bWluLWgtWzE0MHB4XVwiLFxuICAgICAgICBpc0N1cnJlbnRNb250aFxuICAgICAgICAgID8gXCJiZy13aGl0ZSBob3ZlcjpiZy1uZXV0cmFsLTUwXCJcbiAgICAgICAgICA6IFwiYmctbmV1dHJhbC0xMDAgaG92ZXI6YmctbmV1dHJhbC0yMDBcIixcbiAgICAgICAgaXNPdmVyICYmIFwiYmctYmx1ZS01MCBib3JkZXItYmx1ZS0yMDBcIlxuICAgICAgKX1cbiAgICA+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG4vLyBIZWxwZXIgZnVuY3Rpb24gdG8gY2hlY2sgaWYgYW4gZXZlbnQgYWZmZWN0cyBhIHNwZWNpZmljIGRheVxuY29uc3QgZXZlbnRBZmZlY3RzRGF5ID0gKGV2ZW50OiBDYWxlbmRhckV2ZW50LCBkYXk6IERhdGUpOiBib29sZWFuID0+IHtcbiAgY29uc3QgZXZlbnRTdGFydCA9IG5ldyBEYXRlKGV2ZW50LnN0YXJ0KTtcbiAgY29uc3QgZXZlbnRFbmQgPSBuZXcgRGF0ZShldmVudC5lbmQpO1xuICBjb25zdCBkYXlTdGFydCA9IG5ldyBEYXRlKGRheSk7XG4gIGNvbnN0IGRheUVuZCA9IG5ldyBEYXRlKGRheSk7XG4gIGRheUVuZC5zZXRIb3VycygyMywgNTksIDU5LCA5OTkpO1xuICBcbiAgcmV0dXJuIGV2ZW50U3RhcnQgPD0gZGF5RW5kICYmIGV2ZW50RW5kID49IGRheVN0YXJ0O1xufTtcblxuLy8gTmV3IGhlbHBlciBmdW5jdGlvbiB0byBwcm9jZXNzIGV2ZW50cyBmb3IgdGhlIGVudGlyZSBtb250aCB3aXRoIHByb3BlciBzbG90IHRyYWNraW5nXG5jb25zdCB1c2VNb250aEV2ZW50cyA9ICh3ZWVrczogRGF0ZVtdW10sIGV2ZW50czogQ2FsZW5kYXJFdmVudFtdKSA9PiB7XG4gIHJldHVybiB1c2VNZW1vKCgpID0+IHtcbiAgICBjb25zdCBwb3NpdGlvbmVkRXZlbnRzQnlXZWVrID0gbmV3IE1hcDxudW1iZXIsIGFueVtdPigpO1xuICAgIGNvbnN0IHNsb3RVc2FnZUJ5RGF5ID0gbmV3IE1hcDxzdHJpbmcsIG51bWJlcj4oKTsgLy8gVHJhY2sgc2xvdCB1c2FnZSBwZXIgZGF5XG5cbiAgICB3ZWVrcy5mb3JFYWNoKCh3ZWVrLCB3ZWVrSW5kZXgpID0+IHtcbiAgICAgIGNvbnN0IHdlZWtTdGFydCA9IHdlZWtbMF07XG4gICAgICBjb25zdCB3ZWVrRW5kID0gd2Vla1s2XTtcblxuICAgICAgLy8gR2V0IGFsbCBldmVudHMgdGhhdCBpbnRlcnNlY3Qgd2l0aCB0aGlzIHdlZWtcbiAgICAgIGNvbnN0IHdlZWtFdmVudHMgPSBldmVudHMuZmlsdGVyKGV2ZW50ID0+IHtcbiAgICAgICAgY29uc3QgZXZlbnRTdGFydCA9IG5ldyBEYXRlKGV2ZW50LnN0YXJ0KTtcbiAgICAgICAgY29uc3QgZXZlbnRFbmQgPSBuZXcgRGF0ZShldmVudC5lbmQpO1xuICAgICAgICByZXR1cm4gZXZlbnRTdGFydCA8PSB3ZWVrRW5kICYmIGV2ZW50RW5kID49IHdlZWtTdGFydDtcbiAgICAgIH0pO1xuXG4gICAgICBjb25zdCBzcGFubmluZ0V2ZW50czogYW55W10gPSBbXTtcbiAgICAgIHdlZWtFdmVudHMuZm9yRWFjaChldmVudCA9PiB7XG4gICAgICAgIGNvbnN0IGV2ZW50U3RhcnQgPSBuZXcgRGF0ZShldmVudC5zdGFydCk7XG4gICAgICAgIGNvbnN0IGV2ZW50RW5kID0gbmV3IERhdGUoZXZlbnQuZW5kKTtcbiAgICAgICAgXG4gICAgICAgIC8vIEZpbmQgd2hpY2ggZGF5cyB0aGlzIGV2ZW50IHNwYW5zIGluIHRoaXMgd2Vla1xuICAgICAgICBjb25zdCBzdGFydERheUluZGV4ID0gd2Vlay5maW5kSW5kZXgoZGF5ID0+IGlzU2FtZURheShkYXksIGV2ZW50U3RhcnQpKTtcbiAgICAgICAgY29uc3QgZW5kRGF5SW5kZXggPSB3ZWVrLmZpbmRJbmRleChkYXkgPT4gaXNTYW1lRGF5KGRheSwgZXZlbnRFbmQpKTtcblxuICAgICAgICAvLyBDYWxjdWxhdGUgYWN0dWFsIHNwYW4gd2l0aGluIHRoaXMgd2Vla1xuICAgICAgICBsZXQgYWN0dWFsU3RhcnQgPSAwO1xuICAgICAgICBsZXQgYWN0dWFsRW5kID0gNjtcbiAgICAgICAgXG4gICAgICAgIC8vIEZvciBldmVudHMgdGhhdCBzdGFydCBpbiB0aGlzIHdlZWtcbiAgICAgICAgaWYgKHN0YXJ0RGF5SW5kZXggIT09IC0xKSB7XG4gICAgICAgICAgYWN0dWFsU3RhcnQgPSBzdGFydERheUluZGV4O1xuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICAvLyBGb3IgZXZlbnRzIHRoYXQgZW5kIGluIHRoaXMgd2Vla1xuICAgICAgICBpZiAoZW5kRGF5SW5kZXggIT09IC0xKSB7XG4gICAgICAgICAgYWN0dWFsRW5kID0gZW5kRGF5SW5kZXg7XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIC8vIEZvciBldmVudHMgdGhhdCBzcGFuIHRocm91Z2ggdGhpcyB3ZWVrIGVudGlyZWx5XG4gICAgICAgIGlmIChzdGFydERheUluZGV4ID09PSAtMSAmJiBlbmREYXlJbmRleCA9PT0gLTEgJiYgZXZlbnRTdGFydCA8IHdlZWtTdGFydCAmJiBldmVudEVuZCA+IHdlZWtFbmQpIHtcbiAgICAgICAgICBhY3R1YWxTdGFydCA9IDA7XG4gICAgICAgICAgYWN0dWFsRW5kID0gNjtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIENoZWNrIGlmIGV2ZW50IGludGVyc2VjdHMgd2l0aCB0aGlzIHdlZWtcbiAgICAgICAgY29uc3QgZXZlbnRTcGFuc1dlZWsgPSBzdGFydERheUluZGV4ICE9PSAtMSB8fCBlbmREYXlJbmRleCAhPT0gLTEgfHwgKGV2ZW50U3RhcnQgPCB3ZWVrU3RhcnQgJiYgZXZlbnRFbmQgPiB3ZWVrRW5kKTtcblxuICAgICAgICBpZiAoZXZlbnRTcGFuc1dlZWspIHtcbiAgICAgICAgICBzcGFubmluZ0V2ZW50cy5wdXNoKHtcbiAgICAgICAgICAgIGV2ZW50LFxuICAgICAgICAgICAgc3RhcnREYXlJbmRleDogYWN0dWFsU3RhcnQsXG4gICAgICAgICAgICBlbmREYXlJbmRleDogYWN0dWFsRW5kLFxuICAgICAgICAgICAgY29sU3BhbjogYWN0dWFsRW5kIC0gYWN0dWFsU3RhcnQgKyAxLFxuICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICB9KTtcbiAgICAgIFxuICAgICAgLy8gU29ydCBldmVudHMgYnkgc3RhcnQgZGF5LCB0aGVuIGJ5IHNwYW4gbGVuZ3RoIChsb25nZXIgZXZlbnRzIGZpcnN0KVxuICAgICAgY29uc3Qgc29ydGVkRXZlbnRzID0gc3Bhbm5pbmdFdmVudHMuc29ydCgoYSwgYikgPT4ge1xuICAgICAgICBpZiAoYS5zdGFydERheUluZGV4ICE9PSBiLnN0YXJ0RGF5SW5kZXgpIHtcbiAgICAgICAgICByZXR1cm4gYS5zdGFydERheUluZGV4IC0gYi5zdGFydERheUluZGV4O1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBiLmNvbFNwYW4gLSBhLmNvbFNwYW47XG4gICAgICB9KTtcblxuICAgICAgLy8gUG9zaXRpb24gZXZlbnRzIGFuZCB0cmFjayBzbG90IHVzYWdlXG4gICAgICBjb25zdCBwb3NpdGlvbmVkOiBhbnlbXSA9IFtdO1xuICAgICAgY29uc3Qgcm93czogYW55W11bXSA9IFtdO1xuXG4gICAgICBzb3J0ZWRFdmVudHMuZm9yRWFjaChldmVudERhdGEgPT4ge1xuICAgICAgICAvLyBDaGVjayBpZiB0aGlzIGV2ZW50IGNhbiBiZSBwbGFjZWQgKGFsbCBkYXlzIGl0IHNwYW5zIGhhdmUgYXZhaWxhYmxlIHNsb3RzKVxuICAgICAgICBjb25zdCBhZmZlY3RlZERheXMgPSB3ZWVrLnNsaWNlKGV2ZW50RGF0YS5zdGFydERheUluZGV4LCBldmVudERhdGEuZW5kRGF5SW5kZXggKyAxKTtcbiAgICAgICAgY29uc3QgY2FuUGxhY2UgPSBhZmZlY3RlZERheXMuZXZlcnkoZGF5ID0+IHtcbiAgICAgICAgICBjb25zdCBkYXlLZXkgPSBmb3JtYXQoZGF5LCAneXl5eS1NTS1kZCcpO1xuICAgICAgICAgIGNvbnN0IGN1cnJlbnRVc2FnZSA9IHNsb3RVc2FnZUJ5RGF5LmdldChkYXlLZXkpIHx8IDA7XG4gICAgICAgICAgcmV0dXJuIGN1cnJlbnRVc2FnZSA8IDQ7IC8vIE1heGltdW0gNCBzbG90cyBwZXIgZGF5XG4gICAgICAgIH0pO1xuXG4gICAgICAgIGlmICghY2FuUGxhY2UpIHtcbiAgICAgICAgICAvLyBFdmVudCBjYW5ub3QgYmUgcGxhY2VkLCBza2lwIGl0IChpdCB3aWxsIGJlIGluIHRoZSBcIittb3JlXCIgY291bnQpXG4gICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gRmluZCBhdmFpbGFibGUgcm93XG4gICAgICAgIGxldCBhc3NpZ25lZCA9IGZhbHNlO1xuICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHJvd3MubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgICBjb25zdCByb3cgPSByb3dzW2ldO1xuICAgICAgICAgIGNvbnN0IGhhc0NvbmZsaWN0ID0gcm93LnNvbWUoZXhpc3RpbmdFdmVudCA9PiBcbiAgICAgICAgICAgIGV2ZW50RGF0YS5zdGFydERheUluZGV4IDw9IGV4aXN0aW5nRXZlbnQuZW5kRGF5SW5kZXggJiYgXG4gICAgICAgICAgICBldmVudERhdGEuZW5kRGF5SW5kZXggPj0gZXhpc3RpbmdFdmVudC5zdGFydERheUluZGV4XG4gICAgICAgICAgKTtcbiAgICAgICAgICBcbiAgICAgICAgICBpZiAoIWhhc0NvbmZsaWN0KSB7XG4gICAgICAgICAgICByb3cucHVzaChldmVudERhdGEpO1xuICAgICAgICAgICAgcG9zaXRpb25lZC5wdXNoKHsgLi4uZXZlbnREYXRhLCByb3c6IGkgfSk7XG4gICAgICAgICAgICBhc3NpZ25lZCA9IHRydWU7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICBpZiAoIWFzc2lnbmVkKSB7XG4gICAgICAgICAgcm93cy5wdXNoKFtldmVudERhdGFdKTtcbiAgICAgICAgICBwb3NpdGlvbmVkLnB1c2goeyAuLi5ldmVudERhdGEsIHJvdzogcm93cy5sZW5ndGggLSAxIH0pO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gVXBkYXRlIHNsb3QgdXNhZ2UgZm9yIGFsbCBhZmZlY3RlZCBkYXlzXG4gICAgICAgIGFmZmVjdGVkRGF5cy5mb3JFYWNoKGRheSA9PiB7XG4gICAgICAgICAgY29uc3QgZGF5S2V5ID0gZm9ybWF0KGRheSwgJ3l5eXktTU0tZGQnKTtcbiAgICAgICAgICBjb25zdCBjdXJyZW50VXNhZ2UgPSBzbG90VXNhZ2VCeURheS5nZXQoZGF5S2V5KSB8fCAwO1xuICAgICAgICAgIHNsb3RVc2FnZUJ5RGF5LnNldChkYXlLZXksIGN1cnJlbnRVc2FnZSArIDEpO1xuICAgICAgICB9KTtcbiAgICAgIH0pO1xuXG4gICAgICBwb3NpdGlvbmVkRXZlbnRzQnlXZWVrLnNldCh3ZWVrSW5kZXgsIHBvc2l0aW9uZWQpO1xuICAgIH0pO1xuXG4gICAgcmV0dXJuIHsgcG9zaXRpb25lZEV2ZW50c0J5V2Vlaywgc2xvdFVzYWdlQnlEYXkgfTtcbiAgfSwgW3dlZWtzLCBldmVudHNdKTtcbn07XG5cbmV4cG9ydCBjb25zdCBNb250aFZpZXc6IFJlYWN0LkZDPE1vbnRoVmlld1Byb3BzPiA9ICh7XG4gIHNlbGVjdGVkRGF0ZSxcbiAgZXZlbnRzLFxuICBzZWxlY3RlZEV2ZW50LFxuICBzZXRTZWxlY3RlZEV2ZW50LFxuICBzZXRTZWxlY3RlZERhdGUsXG4gIG9wZW5BZGRFdmVudEZvcm0sXG4gIGNhbkVkaXREYXRhLFxuICBoYW5kbGVFdmVudENsaWNrLFxuICBhY3RpdmVEcmFnRGF0YSxcbn0pID0+IHtcbiAgY29uc3QgbWF5YmVSZWNvcmQgPSB1c2VNYXliZVJlY29yZCgpO1xuXG4gIGNvbnN0IGlzSW5SZWNvcmRUYWIgPSAhIW1heWJlUmVjb3JkO1xuXG4gIC8vIE1lbW9pemUgbW9udGggY2FsY3VsYXRpb25zXG4gIGNvbnN0IG1vbnRoQ2FsY3VsYXRpb25zID0gdXNlTWVtbygoKSA9PiB7XG4gICAgY29uc3QgbW9udGhTdGFydCA9IHN0YXJ0T2ZNb250aChzZWxlY3RlZERhdGUpO1xuICAgIGNvbnN0IG1vbnRoRW5kID0gZW5kT2ZNb250aChzZWxlY3RlZERhdGUpO1xuICAgIGNvbnN0IHN0YXJ0RGF5ID0gc3RhcnRPZldlZWsobW9udGhTdGFydCwgeyB3ZWVrU3RhcnRzT246IDAgfSk7XG4gICAgY29uc3QgZW5kRGF5ID0gZW5kT2ZXZWVrKG1vbnRoRW5kLCB7IHdlZWtTdGFydHNPbjogMCB9KTtcblxuICAgIGNvbnN0IGRheXMgPSBbXTtcbiAgICBsZXQgZGF5ID0gc3RhcnREYXk7XG4gICAgd2hpbGUgKGRheSA8PSBlbmREYXkpIHtcbiAgICAgIGRheXMucHVzaChkYXkpO1xuICAgICAgZGF5ID0gYWRkRGF5cyhkYXksIDEpO1xuICAgIH1cblxuICAgIGNvbnN0IHdlZWtzID0gW107XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBkYXlzLmxlbmd0aDsgaSArPSA3KSB7XG4gICAgICB3ZWVrcy5wdXNoKGRheXMuc2xpY2UoaSwgaSArIDcpKTtcbiAgICB9XG5cbiAgICByZXR1cm4geyBtb250aFN0YXJ0LCBtb250aEVuZCwgc3RhcnREYXksIGVuZERheSwgZGF5cywgd2Vla3MgfTtcbiAgfSwgW3NlbGVjdGVkRGF0ZV0pO1xuXG4gIC8vIE1lbW9pemUgbW9udGggZXZlbnRzXG4gIGNvbnN0IG1vbnRoRXZlbnRzID0gdXNlTWVtbygoKSA9PiBcbiAgICBldmVudHMuZmlsdGVyKGV2ZW50ID0+IHtcbiAgICAgIGNvbnN0IGV2ZW50U3RhcnQgPSBuZXcgRGF0ZShldmVudC5zdGFydCk7XG4gICAgICBjb25zdCBldmVudEVuZCA9IG5ldyBEYXRlKGV2ZW50LmVuZCk7XG4gICAgICByZXR1cm4gZXZlbnRTdGFydCA8PSBtb250aENhbGN1bGF0aW9ucy5lbmREYXkgJiYgXG4gICAgICAgICAgICAgZXZlbnRFbmQgPj0gbW9udGhDYWxjdWxhdGlvbnMuc3RhcnREYXk7XG4gICAgfSksIFxuICAgIFtldmVudHMsIG1vbnRoQ2FsY3VsYXRpb25zLnN0YXJ0RGF5LCBtb250aENhbGN1bGF0aW9ucy5lbmREYXldXG4gICk7XG5cbiAgY29uc3QgeyBwb3NpdGlvbmVkRXZlbnRzQnlXZWVrLCBzbG90VXNhZ2VCeURheSB9ID0gdXNlTW9udGhFdmVudHMobW9udGhDYWxjdWxhdGlvbnMud2Vla3MsIG1vbnRoRXZlbnRzKTtcblxuICAvLyBSZW5kZXIgZW1wdHkgc3RhdGUgd2hlbiBubyBldmVudHNcbiAgY29uc3QgcmVuZGVyRW1wdHlTdGF0ZSA9ICgpID0+IChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaC1mdWxsIGJnLWJhY2tncm91bmRcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtNyBib3JkZXItYiBib3JkZXItbmV1dHJhbC0zMDAgYmctd2hpdGVcIj5cbiAgICAgICAge1snU3VuZGF5JywgJ01vbmRheScsICdUdWVzZGF5JywgJ1dlZG5lc2RheScsICdUaHVyc2RheScsICdGcmlkYXknLCAnU2F0dXJkYXknXS5tYXAoZGF5TmFtZSA9PiAoXG4gICAgICAgICAgPGRpdiBrZXk9e2RheU5hbWV9IGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICBcInRleHQtY2VudGVyIGZvbnQtc2VtaWJvbGQgdGV4dC1ibGFja1wiLFxuICAgICAgICAgICAgXCJweS0yIHRleHQteHNcIlxuICAgICAgICAgICl9PlxuICAgICAgICAgICAge2RheU5hbWUuc3Vic3RyaW5nKDAsIDMpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApKX1cbiAgICAgIDwvZGl2PlxuXG4gICAgICA8Tm9FdmVudHNcbiAgICAgICAgdGl0bGU9XCJObyBldmVudHMgdGhpcyBtb250aFwiXG4gICAgICAgIG1lc3NhZ2U9e2Ake2Zvcm1hdChzZWxlY3RlZERhdGUsICdNTU1NIHl5eXknKX0gaXMgY29tcGxldGVseSBmcmVlLiBTdGFydCBwbGFubmluZyB5b3VyIG1vbnRoIWB9XG4gICAgICAgIHNob3dDcmVhdGVCdXR0b249e2NhbkVkaXREYXRhfVxuICAgICAgICBvbkNyZWF0ZT17KCkgPT4gb3BlbkFkZEV2ZW50Rm9ybShzZWxlY3RlZERhdGUpfVxuICAgICAgLz5cbiAgICA8L2Rpdj5cbiAgKTtcblxuICAvLyBSZW5kZXIgZGF5IGNlbGwgY29udGVudFxuICBjb25zdCByZW5kZXJEYXlDZWxsQ29udGVudCA9IChkYXk6IERhdGUsIGRheUV2ZW50czogYW55W10pID0+IHtcbiAgICBjb25zdCBpc0N1cnJlbnRNb250aCA9IGRheS5nZXRNb250aCgpID09PSBzZWxlY3RlZERhdGUuZ2V0TW9udGgoKTtcbiAgICBjb25zdCBpc0N1cnJlbnREYXkgPSBpc1RvZGF5KGRheSk7XG4gICAgY29uc3QgTUFYX1ZJU0lCTEVfRVZFTlRTID0gNDtcbiAgICBjb25zdCBST1dfSEVJR0hUID0gMjg7XG5cbiAgICBjb25zdCBkYXlLZXkgPSBmb3JtYXQoZGF5LCAneXl5eS1NTS1kZCcpO1xuICAgIFxuICAgIC8vIEdldCBhbGwgZXZlbnRzIHRoYXQgYWZmZWN0IHRoaXMgZGF5IChmb3IgdGhlICttb3JlIGNvdW50KVxuICAgIGNvbnN0IGFsbERheUV2ZW50cyA9IG1vbnRoRXZlbnRzLmZpbHRlcihldmVudCA9PiBldmVudEFmZmVjdHNEYXkoZXZlbnQsIGRheSkpO1xuICAgIGNvbnN0IHRvdGFsRXZlbnRzRm9yRGF5ID0gYWxsRGF5RXZlbnRzLmxlbmd0aDtcbiAgICBcbiAgICBjb25zdCBzb3J0ZWRFdmVudHMgPSBkYXlFdmVudHMuc29ydCgoYSwgYikgPT4gYS5yb3cgLSBiLnJvdyk7XG4gICAgY29uc3QgdmlzaWJsZUV2ZW50cyA9IHNvcnRlZEV2ZW50cy5zbGljZSgwLCBNQVhfVklTSUJMRV9FVkVOVFMpO1xuICAgIGNvbnN0IGhhc01vcmUgPSB0b3RhbEV2ZW50c0ZvckRheSA+IE1BWF9WSVNJQkxFX0VWRU5UUztcbiAgICBjb25zdCBoaWRkZW5FdmVudHNDb3VudCA9IE1hdGgubWF4KDAsIHRvdGFsRXZlbnRzRm9yRGF5IC0gTUFYX1ZJU0lCTEVfRVZFTlRTKTtcblxuICAgIC8vIENhbGN1bGF0ZSBjb250YWluZXIgaGVpZ2h0XG4gICAgY29uc3QgbWF4Um93ID0gdmlzaWJsZUV2ZW50cy5yZWR1Y2UoKG1heCwgZXZlbnQpID0+IE1hdGgubWF4KG1heCwgZXZlbnQucm93KSwgLTEpO1xuICAgIGNvbnN0IGNvbnRhaW5lckhlaWdodCA9IGhhc01vcmVcbiAgICAgID8gKE1BWF9WSVNJQkxFX0VWRU5UUyAqIFJPV19IRUlHSFQpICsgMjAgXG4gICAgICA6IChtYXhSb3cgKyAxKSAqIFJPV19IRUlHSFQ7XG5cbiAgICByZXR1cm4gKFxuICAgICAgPD5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItMlwiPlxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICBcImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciByb3VuZGVkLWZ1bGwgZm9udC1zZW1pYm9sZCB0ZXh0LXhzIHctNiBoLTZcIixcbiAgICAgICAgICAgIGlzQ3VycmVudERheSA/IFwiYmctYmxhY2sgdGV4dC13aGl0ZVwiIDogaXNDdXJyZW50TW9udGggPyBcInRleHQtYmxhY2sgaG92ZXI6YmctbmV1dHJhbC0xMDBcIiA6IFwidGV4dC1uZXV0cmFsLTQwMFwiXG4gICAgICAgICAgKX0+XG4gICAgICAgICAgICB7Zm9ybWF0KGRheSwgJ2QnKX1cbiAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAge2NhbkVkaXREYXRhICYmIGlzQ3VycmVudE1vbnRoICYmIChcbiAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicm91bmRlZC1mdWxsIGgtNiB3LTYgcC0xIG9wYWNpdHktMCBncm91cC1ob3ZlcjpvcGFjaXR5LTEwMCB0cmFuc2l0aW9uLW9wYWNpdHkgaG92ZXI6YmctbmV1dHJhbC0xMDAgaGlkZGVuIGxnOmlubGluZS1mbGV4XCJcbiAgICAgICAgICAgICAgb25DbGljaz17KGUpID0+IHtcbiAgICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xuICAgICAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gb3BlbkFkZEV2ZW50Rm9ybShkYXkpLCAxNTApO1xuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8UGx1c0ljb24gY2xhc3NOYW1lPVwiaC0zIHctMyB0ZXh0LWJsYWNrXCIgLz5cbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIiBzdHlsZT17eyBoZWlnaHQ6IGAke2NvbnRhaW5lckhlaWdodH1weGAgfX0+XG4gICAgICAgICAge3Zpc2libGVFdmVudHMubWFwKHBlID0+IChcbiAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAga2V5PXtwZS5ldmVudC5pZH1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGVcIlxuICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgIHRvcDogYCR7cGUucm93ICogUk9XX0hFSUdIVH1weGAsXG4gICAgICAgICAgICAgICAgbGVmdDogJzJweCcsXG4gICAgICAgICAgICAgICAgd2lkdGg6IHBlLmNvbFNwYW4gPiAxIFxuICAgICAgICAgICAgICAgICAgPyBgY2FsYygke3BlLmNvbFNwYW4gKiAxMDB9JSArICR7KHBlLmNvbFNwYW4gLSAxKSAqIDE5fXB4KWBcbiAgICAgICAgICAgICAgICAgIDogJ2NhbGMoMTAwJSAtIDRweCknLFxuICAgICAgICAgICAgICAgIHpJbmRleDogMTAgKyBwZS5yb3csXG4gICAgICAgICAgICAgIH19XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxDYWxlbmRhckV2ZW50SXRlbVxuICAgICAgICAgICAgICAgIGV2ZW50PXtwZS5ldmVudH1cbiAgICAgICAgICAgICAgICB2aWV3PVwibW9udGhcIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xuICAgICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRFdmVudChwZS5ldmVudC5pZCk7XG4gICAgICAgICAgICAgICAgICBoYW5kbGVFdmVudENsaWNrKHBlLmV2ZW50KTtcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgIGlzRHJhZ2dpbmc9e2FjdGl2ZURyYWdEYXRhPy5wYXlsb2FkPy5pZCA9PT0gcGUuZXZlbnQuaWR9XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApKX1cblxuICAgICAgICAgIHtoYXNNb3JlICYmIChcbiAgICAgICAgICAgIDxkaXYgXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtYmxhY2sgaG92ZXI6dGV4dC1ibGFjayBmb250LW1lZGl1bSB0ZXh0LXhzIGN1cnNvci1wb2ludGVyXCJcbiAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICBwb3NpdGlvbjogJ2Fic29sdXRlJyxcbiAgICAgICAgICAgICAgICB0b3A6IGAke01BWF9WSVNJQkxFX0VWRU5UUyAqIFJPV19IRUlHSFR9cHhgLFxuICAgICAgICAgICAgICAgIGxlZnQ6ICcycHgnLFxuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKCk7XG4gICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWREYXRlKGRheSk7XG4gICAgICAgICAgICAgIH19XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgICsge2hpZGRlbkV2ZW50c0NvdW50fSBtb3JlXG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvPlxuICAgICk7XG4gIH07XG5cbiAgLy8gUmVuZGVyIG1haW4gdmlld1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiaC1mdWxsIGJnLWJhY2tncm91bmQgZmxleCBmbGV4LWNvbCBsZzpmbGV4LXJvd1wiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgZmxleCBmbGV4LWNvbCBtaW4taC0wXCI+XG4gICAgICAgIHsvKiBEYXkgSGVhZGVycyAqL31cbiAgICAgICAgPGRpdiBcbiAgICAgICAgICBkYXRhLWRheS1oZWFkZXJzPVwidHJ1ZVwiXG4gICAgICAgICAgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtNyBib3JkZXItYiBib3JkZXItbmV1dHJhbC0zMDAgc3RpY2t5IHRvcC0wIHotMTAgYmctd2hpdGVcIlxuICAgICAgICA+XG4gICAgICAgICAge1snU3VuZGF5JywgJ01vbmRheScsICdUdWVzZGF5JywgJ1dlZG5lc2RheScsICdUaHVyc2RheScsICdGcmlkYXknLCAnU2F0dXJkYXknXS5tYXAoZGF5TmFtZSA9PiAoXG4gICAgICAgICAgICA8ZGl2IGtleT17ZGF5TmFtZX0gY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgXCJ0ZXh0LWNlbnRlciBmb250LXNlbWlib2xkIHRleHQtYmxhY2tcIixcbiAgICAgICAgICAgICAgXCJweS0yIHRleHQteHNcIlxuICAgICAgICAgICAgKX0+XG4gICAgICAgICAgICAgIHtkYXlOYW1lLnN1YnN0cmluZygwLCAzKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICkpfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogTW9udGggR3JpZCAqL31cbiAgICAgICAgPFNjcm9sbEFyZWEgY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy03IGJvcmRlci1uZXV0cmFsLTMwMCBib3JkZXItYlwiPlxuICAgICAgICAgICAge21vbnRoQ2FsY3VsYXRpb25zLndlZWtzLm1hcCgod2Vlaywgd2Vla0luZGV4KSA9PlxuICAgICAgICAgICAgICB3ZWVrLm1hcCgoZGF5LCBkYXlJbmRleCkgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IHdlZWtFdmVudHMgPSBwb3NpdGlvbmVkRXZlbnRzQnlXZWVrLmdldCh3ZWVrSW5kZXgpIHx8IFtdO1xuICAgICAgICAgICAgICAgIC8vIEdldCBldmVudHMgdGhhdCBzdGFydCBvbiB0aGlzIGRheSBPUiBzcGFuIHRocm91Z2ggdGhpcyBkYXlcbiAgICAgICAgICAgICAgICBjb25zdCBkYXlFdmVudHMgPSB3ZWVrRXZlbnRzLmZpbHRlcihwZSA9PiBcbiAgICAgICAgICAgICAgICAgIHBlLnN0YXJ0RGF5SW5kZXggPT09IGRheUluZGV4IHx8IFxuICAgICAgICAgICAgICAgICAgKHBlLnN0YXJ0RGF5SW5kZXggPCBkYXlJbmRleCAmJiBwZS5lbmREYXlJbmRleCA+PSBkYXlJbmRleClcbiAgICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgICAgIGNvbnN0IGlzQ3VycmVudE1vbnRoID0gZGF5LmdldE1vbnRoKCkgPT09IHNlbGVjdGVkRGF0ZS5nZXRNb250aCgpO1xuXG4gICAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICAgIDxEYXlDZWxsXG4gICAgICAgICAgICAgICAgICAgIGtleT17YCR7d2Vla0luZGV4fS0ke2RheUluZGV4fWB9XG4gICAgICAgICAgICAgICAgICAgIGRhdGU9e2RheX1cbiAgICAgICAgICAgICAgICAgICAgaXNDdXJyZW50TW9udGg9e2lzQ3VycmVudE1vbnRofVxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTZWxlY3RlZERhdGUoZGF5KX1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAge3JlbmRlckRheUNlbGxDb250ZW50KGRheSwgZGF5RXZlbnRzKX1cbiAgICAgICAgICAgICAgICAgIDwvRGF5Q2VsbD5cbiAgICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgICB9KSxcbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvU2Nyb2xsQXJlYT5cbiAgICAgIDwvZGl2PlxuXG4gICAgICA8Q2FsZW5kYXJTaWRlQ2FyZFxuICAgICAgICBzZWxlY3RlZERhdGU9e3NlbGVjdGVkRGF0ZX1cbiAgICAgICAgZXZlbnRzPXtldmVudHN9XG4gICAgICAgIHNlbGVjdGVkRXZlbnQ9e3NlbGVjdGVkRXZlbnR9XG4gICAgICAgIHNldFNlbGVjdGVkRXZlbnQ9e3NldFNlbGVjdGVkRXZlbnR9XG4gICAgICAgIGhhbmRsZUV2ZW50Q2xpY2s9e2hhbmRsZUV2ZW50Q2xpY2t9XG4gICAgICAvPlxuICAgIDwvZGl2PlxuICApO1xufTtcblxuZnVuY3Rpb24gaXNNdWx0aURheShldmVudDogQ2FsZW5kYXJFdmVudCk6IGJvb2xlYW4ge1xuICByZXR1cm4gIWlzU2FtZURheShuZXcgRGF0ZShldmVudC5zdGFydCksIG5ldyBEYXRlKGV2ZW50LmVuZCkpO1xufVxuLy8gaW1wb3J0IFJlYWN0LCB7IHVzZU1lbW8gfSBmcm9tICdyZWFjdCc7XG4vLyBpbXBvcnQgeyBmb3JtYXQsIHN0YXJ0T2ZNb250aCwgZW5kT2ZNb250aCwgc3RhcnRPZldlZWssIGVuZE9mV2VlaywgYWRkRGF5cywgaXNTYW1lRGF5LCBpc1RvZGF5IH0gZnJvbSAnZGF0ZS1mbnMnO1xuLy8gaW1wb3J0IHsgY24gfSBmcm9tICdAL2xpYi91dGlscyc7XG4vLyBpbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJztcbi8vIGltcG9ydCB7IFBsdXNJY29uIH0gZnJvbSAnQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lJztcbi8vIGltcG9ydCB7IFNjcm9sbEFyZWEgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvc2Nyb2xsLWFyZWEnO1xuLy8gaW1wb3J0IHsgQ2FsZW5kYXJFdmVudCB9IGZyb20gJ0AvdHlwaW5ncy9wYWdlJztcbi8vIGltcG9ydCB7IHVzZU1heWJlUmVjb3JkIH0gZnJvbSAnQC9wcm92aWRlcnMvcmVjb3JkJztcbi8vIGltcG9ydCB7IENhbGVuZGFyRXZlbnRJdGVtIH0gZnJvbSAnLi9DYWxlbmRhckV2ZW50SXRlbSc7XG4vLyBpbXBvcnQgeyBOb0V2ZW50cyB9IGZyb20gJy4vTm9FdmVudHMnO1xuLy8gaW1wb3J0IHsgQ2FsZW5kYXJTaWRlQ2FyZCB9IGZyb20gJy4vQ2FsZW5kYXJTaWRlQ2FyZCc7XG4vLyBpbXBvcnQgeyB1c2VEcm9wcGFibGUgfSBmcm9tICdAZG5kLWtpdC9jb3JlJztcblxuXG4vLyBpbnRlcmZhY2UgTW9udGhWaWV3UHJvcHMge1xuLy8gICBzZWxlY3RlZERhdGU6IERhdGU7XG4vLyAgIGV2ZW50czogQ2FsZW5kYXJFdmVudFtdO1xuLy8gICBzZWxlY3RlZEV2ZW50OiBzdHJpbmcgfCBudWxsO1xuLy8gICBzZXRTZWxlY3RlZEV2ZW50OiAoaWQ6IHN0cmluZykgPT4gdm9pZDtcbi8vICAgc2V0U2VsZWN0ZWREYXRlOiAoZGF0ZTogRGF0ZSkgPT4gdm9pZDtcbi8vICAgb3BlbkFkZEV2ZW50Rm9ybTogKGRhdGU6IERhdGUpID0+IHZvaWQ7XG4vLyAgIGNhbkVkaXREYXRhOiBib29sZWFuO1xuLy8gICBoYW5kbGVFdmVudENsaWNrOiAoZXZlbnQ6IENhbGVuZGFyRXZlbnQpID0+IHZvaWQ7XG4vLyAgIGFjdGl2ZURyYWdEYXRhOiBhbnk7XG4vLyB9XG5cbi8vIGNvbnN0IERheUNlbGwgPSAoe1xuLy8gICBkYXRlLFxuLy8gICBjaGlsZHJlbixcbi8vICAgb25DbGljayxcbi8vICAgaXNDdXJyZW50TW9udGhcbi8vIH06IHtcbi8vICAgZGF0ZTogRGF0ZTtcbi8vICAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbi8vICAgb25DbGljazogKCkgPT4gdm9pZDtcbi8vICAgaXNDdXJyZW50TW9udGg6IGJvb2xlYW47XG4vLyB9KSA9PiB7XG4vLyAgIGNvbnN0IHsgc2V0Tm9kZVJlZiwgaXNPdmVyIH0gPSB1c2VEcm9wcGFibGUoe1xuLy8gICAgIGlkOiBgZGF5Y2VsbC0ke2Zvcm1hdChkYXRlLCAneXl5eS1NTS1kZCcpfWAsXG4vLyAgICAgZGF0YToge1xuLy8gICAgICAgZGF0ZTogZGF0ZSxcbi8vICAgICAgIHR5cGU6ICdkYXljZWxsJ1xuLy8gICAgIH1cbi8vICAgfSk7XG5cbi8vICAgcmV0dXJuIChcbi8vICAgICA8ZGl2XG4vLyAgICAgICByZWY9e3NldE5vZGVSZWZ9XG4vLyAgICAgICBvbkNsaWNrPXtvbkNsaWNrfVxuLy8gICAgICAgY2xhc3NOYW1lPXtjbihcbi8vICAgICAgICAgXCJib3JkZXItYiBib3JkZXItciBib3JkZXItbmV1dHJhbC0zMDAgcmVsYXRpdmUgY3Vyc29yLXBvaW50ZXIgdHJhbnNpdGlvbi1jb2xvcnMgZ3JvdXAgZmxleCBmbGV4LWNvbCBwLTIgbWluLWgtWzEyMHB4XSBsZzpwLTMgbGc6bWluLWgtWzE0MHB4XVwiLFxuLy8gICAgICAgICBpc0N1cnJlbnRNb250aFxuLy8gICAgICAgICAgID8gXCJiZy13aGl0ZSBob3ZlcjpiZy1uZXV0cmFsLTUwXCJcbi8vICAgICAgICAgICA6IFwiYmctbmV1dHJhbC0xMDAgaG92ZXI6YmctbmV1dHJhbC0yMDBcIixcbi8vICAgICAgICAgaXNPdmVyICYmIFwiYmctYmx1ZS01MCBib3JkZXItYmx1ZS0yMDBcIlxuLy8gICAgICAgKX1cbi8vICAgICA+XG4vLyAgICAgICB7Y2hpbGRyZW59XG4vLyAgICAgPC9kaXY+XG4vLyAgICk7XG4vLyB9O1xuXG4vLyAvLyBIZWxwZXIgZnVuY3Rpb24gdG8gY2hlY2sgaWYgYW4gZXZlbnQgYWZmZWN0cyBhIHNwZWNpZmljIGRheVxuLy8gY29uc3QgZXZlbnRBZmZlY3RzRGF5ID0gKGV2ZW50OiBDYWxlbmRhckV2ZW50LCBkYXk6IERhdGUpOiBib29sZWFuID0+IHtcbi8vICAgY29uc3QgZXZlbnRTdGFydCA9IG5ldyBEYXRlKGV2ZW50LnN0YXJ0KTtcbi8vICAgY29uc3QgZXZlbnRFbmQgPSBuZXcgRGF0ZShldmVudC5lbmQpO1xuLy8gICBjb25zdCBkYXlTdGFydCA9IG5ldyBEYXRlKGRheSk7XG4vLyAgIGNvbnN0IGRheUVuZCA9IG5ldyBEYXRlKGRheSk7XG4vLyAgIGRheUVuZC5zZXRIb3VycygyMywgNTksIDU5LCA5OTkpO1xuICBcbi8vICAgcmV0dXJuIGV2ZW50U3RhcnQgPD0gZGF5RW5kICYmIGV2ZW50RW5kID49IGRheVN0YXJ0O1xuLy8gfTtcblxuLy8gLy8gTmV3IGhlbHBlciBmdW5jdGlvbiB0byBwcm9jZXNzIGV2ZW50cyBmb3IgdGhlIGVudGlyZSBtb250aCB3aXRoIHByb3BlciBzbG90IHRyYWNraW5nXG4vLyBjb25zdCB1c2VNb250aEV2ZW50cyA9ICh3ZWVrczogRGF0ZVtdW10sIGV2ZW50czogQ2FsZW5kYXJFdmVudFtdKSA9PiB7XG4vLyAgIHJldHVybiB1c2VNZW1vKCgpID0+IHtcbi8vICAgICBjb25zdCBwb3NpdGlvbmVkRXZlbnRzQnlXZWVrID0gbmV3IE1hcDxudW1iZXIsIGFueVtdPigpO1xuLy8gICAgIGNvbnN0IHNsb3RVc2FnZUJ5RGF5ID0gbmV3IE1hcDxzdHJpbmcsIG51bWJlcj4oKTsgLy8gVHJhY2sgc2xvdCB1c2FnZSBwZXIgZGF5XG5cbi8vICAgICB3ZWVrcy5mb3JFYWNoKCh3ZWVrLCB3ZWVrSW5kZXgpID0+IHtcbi8vICAgICAgIGNvbnN0IHdlZWtTdGFydCA9IHdlZWtbMF07XG4vLyAgICAgICBjb25zdCB3ZWVrRW5kID0gd2Vla1s2XTtcblxuLy8gICAgICAgLy8gR2V0IGFsbCBldmVudHMgdGhhdCBpbnRlcnNlY3Qgd2l0aCB0aGlzIHdlZWtcbi8vICAgICAgIGNvbnN0IHdlZWtFdmVudHMgPSBldmVudHMuZmlsdGVyKGV2ZW50ID0+IHtcbi8vICAgICAgICAgY29uc3QgZXZlbnRTdGFydCA9IG5ldyBEYXRlKGV2ZW50LnN0YXJ0KTtcbi8vICAgICAgICAgY29uc3QgZXZlbnRFbmQgPSBuZXcgRGF0ZShldmVudC5lbmQpO1xuLy8gICAgICAgICByZXR1cm4gZXZlbnRTdGFydCA8PSB3ZWVrRW5kICYmIGV2ZW50RW5kID49IHdlZWtTdGFydDtcbi8vICAgICAgIH0pO1xuXG4vLyAgICAgICBjb25zdCBzcGFubmluZ0V2ZW50czogYW55W10gPSBbXTtcbi8vICAgICAgIHdlZWtFdmVudHMuZm9yRWFjaChldmVudCA9PiB7XG4vLyAgICAgICAgIGNvbnN0IGV2ZW50U3RhcnQgPSBuZXcgRGF0ZShldmVudC5zdGFydCk7XG4vLyAgICAgICAgIGNvbnN0IGV2ZW50RW5kID0gbmV3IERhdGUoZXZlbnQuZW5kKTtcbiAgICAgICAgXG4vLyAgICAgICAgIC8vIEZpbmQgd2hpY2ggZGF5cyB0aGlzIGV2ZW50IHNwYW5zIGluIHRoaXMgd2Vla1xuLy8gICAgICAgICBjb25zdCBzdGFydERheUluZGV4ID0gd2Vlay5maW5kSW5kZXgoZGF5ID0+IGlzU2FtZURheShkYXksIGV2ZW50U3RhcnQpKTtcbi8vICAgICAgICAgY29uc3QgZW5kRGF5SW5kZXggPSB3ZWVrLmZpbmRJbmRleChkYXkgPT4gaXNTYW1lRGF5KGRheSwgZXZlbnRFbmQpKTtcblxuLy8gICAgICAgICAvLyBDYWxjdWxhdGUgYWN0dWFsIHNwYW4gd2l0aGluIHRoaXMgd2Vla1xuLy8gICAgICAgICBsZXQgYWN0dWFsU3RhcnQgPSAwO1xuLy8gICAgICAgICBsZXQgYWN0dWFsRW5kID0gNjtcbiAgICAgICAgXG4vLyAgICAgICAgIGlmIChzdGFydERheUluZGV4ICE9PSAtMSkge1xuLy8gICAgICAgICAgIGFjdHVhbFN0YXJ0ID0gc3RhcnREYXlJbmRleDtcbi8vICAgICAgICAgfVxuLy8gICAgICAgICBpZiAoZW5kRGF5SW5kZXggIT09IC0xKSB7XG4vLyAgICAgICAgICAgYWN0dWFsRW5kID0gZW5kRGF5SW5kZXg7XG4vLyAgICAgICAgIH1cblxuLy8gICAgICAgICAvLyBDaGVjayBpZiBldmVudCBpbnRlcnNlY3RzIHdpdGggdGhpcyB3ZWVrXG4vLyAgICAgICAgIGNvbnN0IGV2ZW50U3BhbnNXZWVrID0gc3RhcnREYXlJbmRleCAhPT0gLTEgfHwgZW5kRGF5SW5kZXggIT09IC0xIHx8IChldmVudFN0YXJ0IDwgd2Vla1N0YXJ0ICYmIGV2ZW50RW5kID4gd2Vla0VuZCk7XG5cbi8vICAgICAgICAgaWYgKGV2ZW50U3BhbnNXZWVrKSB7XG4vLyAgICAgICAgICAgc3Bhbm5pbmdFdmVudHMucHVzaCh7XG4vLyAgICAgICAgICAgICBldmVudCxcbi8vICAgICAgICAgICAgIHN0YXJ0RGF5SW5kZXg6IGFjdHVhbFN0YXJ0LFxuLy8gICAgICAgICAgICAgZW5kRGF5SW5kZXg6IGFjdHVhbEVuZCxcbi8vICAgICAgICAgICAgIGNvbFNwYW46IGFjdHVhbEVuZCAtIGFjdHVhbFN0YXJ0ICsgMSxcbi8vICAgICAgICAgICB9KTtcbi8vICAgICAgICAgfVxuLy8gICAgICAgfSk7XG4gICAgICBcbi8vICAgICAgIC8vIFNvcnQgZXZlbnRzIGJ5IHN0YXJ0IGRheSwgdGhlbiBieSBzcGFuIGxlbmd0aCAobG9uZ2VyIGV2ZW50cyBmaXJzdClcbi8vICAgICAgIGNvbnN0IHNvcnRlZEV2ZW50cyA9IHNwYW5uaW5nRXZlbnRzLnNvcnQoKGEsIGIpID0+IHtcbi8vICAgICAgICAgaWYgKGEuc3RhcnREYXlJbmRleCAhPT0gYi5zdGFydERheUluZGV4KSB7XG4vLyAgICAgICAgICAgcmV0dXJuIGEuc3RhcnREYXlJbmRleCAtIGIuc3RhcnREYXlJbmRleDtcbi8vICAgICAgICAgfVxuLy8gICAgICAgICByZXR1cm4gYi5jb2xTcGFuIC0gYS5jb2xTcGFuO1xuLy8gICAgICAgfSk7XG5cbi8vICAgICAgIC8vIFBvc2l0aW9uIGV2ZW50cyBhbmQgdHJhY2sgc2xvdCB1c2FnZVxuLy8gICAgICAgY29uc3QgcG9zaXRpb25lZDogYW55W10gPSBbXTtcbi8vICAgICAgIGNvbnN0IHJvd3M6IGFueVtdW10gPSBbXTtcblxuLy8gICAgICAgc29ydGVkRXZlbnRzLmZvckVhY2goZXZlbnREYXRhID0+IHtcbi8vICAgICAgICAgLy8gQ2hlY2sgaWYgdGhpcyBldmVudCBjYW4gYmUgcGxhY2VkIChhbGwgZGF5cyBpdCBzcGFucyBoYXZlIGF2YWlsYWJsZSBzbG90cylcbi8vICAgICAgICAgY29uc3QgYWZmZWN0ZWREYXlzID0gd2Vlay5zbGljZShldmVudERhdGEuc3RhcnREYXlJbmRleCwgZXZlbnREYXRhLmVuZERheUluZGV4ICsgMSk7XG4vLyAgICAgICAgIGNvbnN0IGNhblBsYWNlID0gYWZmZWN0ZWREYXlzLmV2ZXJ5KGRheSA9PiB7XG4vLyAgICAgICAgICAgY29uc3QgZGF5S2V5ID0gZm9ybWF0KGRheSwgJ3l5eXktTU0tZGQnKTtcbi8vICAgICAgICAgICBjb25zdCBjdXJyZW50VXNhZ2UgPSBzbG90VXNhZ2VCeURheS5nZXQoZGF5S2V5KSB8fCAwO1xuLy8gICAgICAgICAgIHJldHVybiBjdXJyZW50VXNhZ2UgPCA0OyAvLyBNYXhpbXVtIDQgc2xvdHMgcGVyIGRheVxuLy8gICAgICAgICB9KTtcblxuLy8gICAgICAgICBpZiAoIWNhblBsYWNlKSB7XG4vLyAgICAgICAgICAgLy8gRXZlbnQgY2Fubm90IGJlIHBsYWNlZCwgc2tpcCBpdCAoaXQgd2lsbCBiZSBpbiB0aGUgXCIrbW9yZVwiIGNvdW50KVxuLy8gICAgICAgICAgIHJldHVybjtcbi8vICAgICAgICAgfVxuXG4vLyAgICAgICAgIC8vIEZpbmQgYXZhaWxhYmxlIHJvd1xuLy8gICAgICAgICBsZXQgYXNzaWduZWQgPSBmYWxzZTtcbi8vICAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCByb3dzLmxlbmd0aDsgaSsrKSB7XG4vLyAgICAgICAgICAgY29uc3Qgcm93ID0gcm93c1tpXTtcbi8vICAgICAgICAgICBjb25zdCBoYXNDb25mbGljdCA9IHJvdy5zb21lKGV4aXN0aW5nRXZlbnQgPT4gXG4vLyAgICAgICAgICAgICBldmVudERhdGEuc3RhcnREYXlJbmRleCA8PSBleGlzdGluZ0V2ZW50LmVuZERheUluZGV4ICYmIFxuLy8gICAgICAgICAgICAgZXZlbnREYXRhLmVuZERheUluZGV4ID49IGV4aXN0aW5nRXZlbnQuc3RhcnREYXlJbmRleFxuLy8gICAgICAgICAgICk7XG4gICAgICAgICAgXG4vLyAgICAgICAgICAgaWYgKCFoYXNDb25mbGljdCkge1xuLy8gICAgICAgICAgICAgcm93LnB1c2goZXZlbnREYXRhKTtcbi8vICAgICAgICAgICAgIHBvc2l0aW9uZWQucHVzaCh7IC4uLmV2ZW50RGF0YSwgcm93OiBpIH0pO1xuLy8gICAgICAgICAgICAgYXNzaWduZWQgPSB0cnVlO1xuLy8gICAgICAgICAgICAgYnJlYWs7XG4vLyAgICAgICAgICAgfVxuLy8gICAgICAgICB9XG5cbi8vICAgICAgICAgaWYgKCFhc3NpZ25lZCkge1xuLy8gICAgICAgICAgIHJvd3MucHVzaChbZXZlbnREYXRhXSk7XG4vLyAgICAgICAgICAgcG9zaXRpb25lZC5wdXNoKHsgLi4uZXZlbnREYXRhLCByb3c6IHJvd3MubGVuZ3RoIC0gMSB9KTtcbi8vICAgICAgICAgfVxuXG4vLyAgICAgICAgIC8vIFVwZGF0ZSBzbG90IHVzYWdlIGZvciBhbGwgYWZmZWN0ZWQgZGF5c1xuLy8gICAgICAgICBhZmZlY3RlZERheXMuZm9yRWFjaChkYXkgPT4ge1xuLy8gICAgICAgICAgIGNvbnN0IGRheUtleSA9IGZvcm1hdChkYXksICd5eXl5LU1NLWRkJyk7XG4vLyAgICAgICAgICAgY29uc3QgY3VycmVudFVzYWdlID0gc2xvdFVzYWdlQnlEYXkuZ2V0KGRheUtleSkgfHwgMDtcbi8vICAgICAgICAgICBzbG90VXNhZ2VCeURheS5zZXQoZGF5S2V5LCBjdXJyZW50VXNhZ2UgKyAxKTtcbi8vICAgICAgICAgfSk7XG4vLyAgICAgICB9KTtcblxuLy8gICAgICAgcG9zaXRpb25lZEV2ZW50c0J5V2Vlay5zZXQod2Vla0luZGV4LCBwb3NpdGlvbmVkKTtcbi8vICAgICB9KTtcblxuLy8gICAgIHJldHVybiB7IHBvc2l0aW9uZWRFdmVudHNCeVdlZWssIHNsb3RVc2FnZUJ5RGF5IH07XG4vLyAgIH0sIFt3ZWVrcywgZXZlbnRzXSk7XG4vLyB9O1xuXG4vLyBleHBvcnQgY29uc3QgTW9udGhWaWV3OiBSZWFjdC5GQzxNb250aFZpZXdQcm9wcz4gPSAoe1xuLy8gICBzZWxlY3RlZERhdGUsXG4vLyAgIGV2ZW50cyxcbi8vICAgc2VsZWN0ZWRFdmVudCxcbi8vICAgc2V0U2VsZWN0ZWRFdmVudCxcbi8vICAgc2V0U2VsZWN0ZWREYXRlLFxuLy8gICBvcGVuQWRkRXZlbnRGb3JtLFxuLy8gICBjYW5FZGl0RGF0YSxcbi8vICAgaGFuZGxlRXZlbnRDbGljayxcbi8vICAgYWN0aXZlRHJhZ0RhdGEsXG4vLyB9KSA9PiB7XG4vLyAgIGNvbnN0IG1heWJlUmVjb3JkID0gdXNlTWF5YmVSZWNvcmQoKTtcblxuLy8gICBjb25zdCBpc0luUmVjb3JkVGFiID0gISFtYXliZVJlY29yZDtcblxuLy8gICAvLyBNZW1vaXplIG1vbnRoIGNhbGN1bGF0aW9uc1xuLy8gICBjb25zdCBtb250aENhbGN1bGF0aW9ucyA9IHVzZU1lbW8oKCkgPT4ge1xuLy8gICAgIGNvbnN0IG1vbnRoU3RhcnQgPSBzdGFydE9mTW9udGgoc2VsZWN0ZWREYXRlKTtcbi8vICAgICBjb25zdCBtb250aEVuZCA9IGVuZE9mTW9udGgoc2VsZWN0ZWREYXRlKTtcbi8vICAgICBjb25zdCBzdGFydERheSA9IHN0YXJ0T2ZXZWVrKG1vbnRoU3RhcnQsIHsgd2Vla1N0YXJ0c09uOiAwIH0pO1xuLy8gICAgIGNvbnN0IGVuZERheSA9IGVuZE9mV2Vlayhtb250aEVuZCwgeyB3ZWVrU3RhcnRzT246IDAgfSk7XG5cbi8vICAgICBjb25zdCBkYXlzID0gW107XG4vLyAgICAgbGV0IGRheSA9IHN0YXJ0RGF5O1xuLy8gICAgIHdoaWxlIChkYXkgPD0gZW5kRGF5KSB7XG4vLyAgICAgICBkYXlzLnB1c2goZGF5KTtcbi8vICAgICAgIGRheSA9IGFkZERheXMoZGF5LCAxKTtcbi8vICAgICB9XG5cbi8vICAgICBjb25zdCB3ZWVrcyA9IFtdO1xuLy8gICAgIGZvciAobGV0IGkgPSAwOyBpIDwgZGF5cy5sZW5ndGg7IGkgKz0gNykge1xuLy8gICAgICAgd2Vla3MucHVzaChkYXlzLnNsaWNlKGksIGkgKyA3KSk7XG4vLyAgICAgfVxuXG4vLyAgICAgcmV0dXJuIHsgbW9udGhTdGFydCwgbW9udGhFbmQsIHN0YXJ0RGF5LCBlbmREYXksIGRheXMsIHdlZWtzIH07XG4vLyAgIH0sIFtzZWxlY3RlZERhdGVdKTtcblxuLy8gICAvLyBNZW1vaXplIG1vbnRoIGV2ZW50c1xuLy8gICBjb25zdCBtb250aEV2ZW50cyA9IHVzZU1lbW8oKCkgPT4gXG4vLyAgICAgZXZlbnRzLmZpbHRlcihldmVudCA9PiB7XG4vLyAgICAgICBjb25zdCBldmVudFN0YXJ0ID0gbmV3IERhdGUoZXZlbnQuc3RhcnQpO1xuLy8gICAgICAgY29uc3QgZXZlbnRFbmQgPSBuZXcgRGF0ZShldmVudC5lbmQpO1xuLy8gICAgICAgcmV0dXJuIGV2ZW50U3RhcnQgPD0gbW9udGhDYWxjdWxhdGlvbnMuZW5kRGF5ICYmIFxuLy8gICAgICAgICAgICAgIGV2ZW50RW5kID49IG1vbnRoQ2FsY3VsYXRpb25zLnN0YXJ0RGF5O1xuLy8gICAgIH0pLCBcbi8vICAgICBbZXZlbnRzLCBtb250aENhbGN1bGF0aW9ucy5zdGFydERheSwgbW9udGhDYWxjdWxhdGlvbnMuZW5kRGF5XVxuLy8gICApO1xuXG4vLyAgIGNvbnN0IHsgcG9zaXRpb25lZEV2ZW50c0J5V2Vlaywgc2xvdFVzYWdlQnlEYXkgfSA9IHVzZU1vbnRoRXZlbnRzKG1vbnRoQ2FsY3VsYXRpb25zLndlZWtzLCBtb250aEV2ZW50cyk7XG5cbi8vICAgLy8gUmVuZGVyIGVtcHR5IHN0YXRlIHdoZW4gbm8gZXZlbnRzXG4vLyAgIGNvbnN0IHJlbmRlckVtcHR5U3RhdGUgPSAoKSA9PiAoXG4vLyAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGgtZnVsbCBiZy1iYWNrZ3JvdW5kXCI+XG4vLyAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTcgYm9yZGVyLWIgYm9yZGVyLW5ldXRyYWwtMzAwIGJnLXdoaXRlXCI+XG4vLyAgICAgICAgIHtbJ1N1bmRheScsICdNb25kYXknLCAnVHVlc2RheScsICdXZWRuZXNkYXknLCAnVGh1cnNkYXknLCAnRnJpZGF5JywgJ1NhdHVyZGF5J10ubWFwKGRheU5hbWUgPT4gKFxuLy8gICAgICAgICAgIDxkaXYga2V5PXtkYXlOYW1lfSBjbGFzc05hbWU9e2NuKFxuLy8gICAgICAgICAgICAgXCJ0ZXh0LWNlbnRlciBmb250LXNlbWlib2xkIHRleHQtYmxhY2tcIixcbi8vICAgICAgICAgICAgIFwicHktMiB0ZXh0LXhzXCJcbi8vICAgICAgICAgICApfT5cbi8vICAgICAgICAgICAgIHtkYXlOYW1lLnN1YnN0cmluZygwLCAzKX1cbi8vICAgICAgICAgICA8L2Rpdj5cbi8vICAgICAgICAgKSl9XG4vLyAgICAgICA8L2Rpdj5cblxuLy8gICAgICAgPE5vRXZlbnRzXG4vLyAgICAgICAgIHRpdGxlPVwiTm8gZXZlbnRzIHRoaXMgbW9udGhcIlxuLy8gICAgICAgICBtZXNzYWdlPXtgJHtmb3JtYXQoc2VsZWN0ZWREYXRlLCAnTU1NTSB5eXl5Jyl9IGlzIGNvbXBsZXRlbHkgZnJlZS4gU3RhcnQgcGxhbm5pbmcgeW91ciBtb250aCFgfVxuLy8gICAgICAgICBzaG93Q3JlYXRlQnV0dG9uPXtjYW5FZGl0RGF0YX1cbi8vICAgICAgICAgb25DcmVhdGU9eygpID0+IG9wZW5BZGRFdmVudEZvcm0oc2VsZWN0ZWREYXRlKX1cbi8vICAgICAgIC8+XG4vLyAgICAgPC9kaXY+XG4vLyAgICk7XG5cbi8vICAgLy8gUmVuZGVyIGRheSBjZWxsIGNvbnRlbnRcbi8vICAgY29uc3QgcmVuZGVyRGF5Q2VsbENvbnRlbnQgPSAoZGF5OiBEYXRlLCBkYXlFdmVudHM6IGFueVtdKSA9PiB7XG4vLyAgICAgY29uc3QgaXNDdXJyZW50TW9udGggPSBkYXkuZ2V0TW9udGgoKSA9PT0gc2VsZWN0ZWREYXRlLmdldE1vbnRoKCk7XG4vLyAgICAgY29uc3QgaXNDdXJyZW50RGF5ID0gaXNUb2RheShkYXkpO1xuLy8gICAgIGNvbnN0IE1BWF9WSVNJQkxFX0VWRU5UUyA9IDQ7XG4vLyAgICAgY29uc3QgUk9XX0hFSUdIVCA9IDI4O1xuXG4vLyAgICAgY29uc3QgZGF5S2V5ID0gZm9ybWF0KGRheSwgJ3l5eXktTU0tZGQnKTtcbiAgICBcbi8vICAgICAvLyBHZXQgYWxsIGV2ZW50cyB0aGF0IGFmZmVjdCB0aGlzIGRheSAoZm9yIHRoZSArbW9yZSBjb3VudClcbi8vICAgICBjb25zdCBhbGxEYXlFdmVudHMgPSBtb250aEV2ZW50cy5maWx0ZXIoZXZlbnQgPT4gZXZlbnRBZmZlY3RzRGF5KGV2ZW50LCBkYXkpKTtcbi8vICAgICBjb25zdCB0b3RhbEV2ZW50c0ZvckRheSA9IGFsbERheUV2ZW50cy5sZW5ndGg7XG4gICAgXG4vLyAgICAgY29uc3Qgc29ydGVkRXZlbnRzID0gZGF5RXZlbnRzLnNvcnQoKGEsIGIpID0+IGEucm93IC0gYi5yb3cpO1xuLy8gICAgIGNvbnN0IHZpc2libGVFdmVudHMgPSBzb3J0ZWRFdmVudHMuc2xpY2UoMCwgTUFYX1ZJU0lCTEVfRVZFTlRTKTtcbi8vICAgICBjb25zdCBoYXNNb3JlID0gdG90YWxFdmVudHNGb3JEYXkgPiBNQVhfVklTSUJMRV9FVkVOVFM7XG4vLyAgICAgY29uc3QgaGlkZGVuRXZlbnRzQ291bnQgPSBNYXRoLm1heCgwLCB0b3RhbEV2ZW50c0ZvckRheSAtIE1BWF9WSVNJQkxFX0VWRU5UUyk7XG5cbi8vICAgICAvLyBDYWxjdWxhdGUgY29udGFpbmVyIGhlaWdodFxuLy8gICAgIGNvbnN0IG1heFJvdyA9IHZpc2libGVFdmVudHMucmVkdWNlKChtYXgsIGV2ZW50KSA9PiBNYXRoLm1heChtYXgsIGV2ZW50LnJvdyksIC0xKTtcbi8vICAgICBjb25zdCBjb250YWluZXJIZWlnaHQgPSBoYXNNb3JlXG4vLyAgICAgICA/IChNQVhfVklTSUJMRV9FVkVOVFMgKiBST1dfSEVJR0hUKSArIDIwIFxuLy8gICAgICAgOiAobWF4Um93ICsgMSkgKiBST1dfSEVJR0hUO1xuXG4vLyAgICAgcmV0dXJuIChcbi8vICAgICAgIDw+XG4vLyAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTJcIj5cbi8vICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2NuKFxuLy8gICAgICAgICAgICAgXCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcm91bmRlZC1mdWxsIGZvbnQtc2VtaWJvbGQgdGV4dC14cyB3LTYgaC02XCIsXG4vLyAgICAgICAgICAgICBpc0N1cnJlbnREYXkgPyBcImJnLWJsYWNrIHRleHQtd2hpdGVcIiA6IGlzQ3VycmVudE1vbnRoID8gXCJ0ZXh0LWJsYWNrIGhvdmVyOmJnLW5ldXRyYWwtMTAwXCIgOiBcInRleHQtbmV1dHJhbC00MDBcIlxuLy8gICAgICAgICAgICl9PlxuLy8gICAgICAgICAgICAge2Zvcm1hdChkYXksICdkJyl9XG4vLyAgICAgICAgICAgPC9zcGFuPlxuLy8gICAgICAgICAgIHtjYW5FZGl0RGF0YSAmJiBpc0N1cnJlbnRNb250aCAmJiAoXG4vLyAgICAgICAgICAgICA8QnV0dG9uXG4vLyAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4vLyAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJvdW5kZWQtZnVsbCBoLTYgdy02IHAtMSBvcGFjaXR5LTAgZ3JvdXAtaG92ZXI6b3BhY2l0eS0xMDAgdHJhbnNpdGlvbi1vcGFjaXR5IGhvdmVyOmJnLW5ldXRyYWwtMTAwIGhpZGRlbiBsZzppbmxpbmUtZmxleFwiXG4vLyAgICAgICAgICAgICAgIG9uQ2xpY2s9eyhlKSA9PiB7XG4vLyAgICAgICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKTtcbi8vICAgICAgICAgICAgICAgICBvcGVuQWRkRXZlbnRGb3JtKGRheSk7XG4vLyAgICAgICAgICAgICAgIH19XG4vLyAgICAgICAgICAgICA+XG4vLyAgICAgICAgICAgICAgIDxQbHVzSWNvbiBjbGFzc05hbWU9XCJoLTMgdy0zIHRleHQtYmxhY2tcIiAvPlxuLy8gICAgICAgICAgICAgPC9CdXR0b24+XG4vLyAgICAgICAgICAgKX1cbi8vICAgICAgICAgPC9kaXY+XG5cbi8vICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiIHN0eWxlPXt7IGhlaWdodDogYCR7Y29udGFpbmVySGVpZ2h0fXB4YCB9fT5cbi8vICAgICAgICAgICB7dmlzaWJsZUV2ZW50cy5tYXAocGUgPT4gKFxuLy8gICAgICAgICAgICAgPGRpdlxuLy8gICAgICAgICAgICAgICBrZXk9e3BlLmV2ZW50LmlkfVxuLy8gICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZVwiXG4vLyAgICAgICAgICAgICAgIHN0eWxlPXt7XG4vLyAgICAgICAgICAgICAgICAgdG9wOiBgJHtwZS5yb3cgKiBST1dfSEVJR0hUfXB4YCxcbi8vICAgICAgICAgICAgICAgICBsZWZ0OiAnMnB4Jyxcbi8vICAgICAgICAgICAgICAgICB3aWR0aDogcGUuY29sU3BhbiA+IDEgXG4vLyAgICAgICAgICAgICAgICAgICA/IGBjYWxjKCR7cGUuY29sU3BhbiAqIDEwMH0lICsgJHsocGUuY29sU3BhbiAtIDEpICogMTl9cHgpYFxuLy8gICAgICAgICAgICAgICAgICAgOiAnY2FsYygxMDAlIC0gNHB4KScsXG4vLyAgICAgICAgICAgICAgICAgekluZGV4OiAxMCArIHBlLnJvdyxcbi8vICAgICAgICAgICAgICAgfX1cbi8vICAgICAgICAgICAgID5cbi8vICAgICAgICAgICAgICAgPENhbGVuZGFyRXZlbnRJdGVtXG4vLyAgICAgICAgICAgICAgICAgZXZlbnQ9e3BlLmV2ZW50fVxuLy8gICAgICAgICAgICAgICAgIHZpZXc9XCJtb250aFwiXG4vLyAgICAgICAgICAgICAgICAgb25DbGljaz17KGUpID0+IHtcbi8vICAgICAgICAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKCk7XG4vLyAgICAgICAgICAgICAgICAgICBzZXRTZWxlY3RlZEV2ZW50KHBlLmV2ZW50LmlkKTtcbi8vICAgICAgICAgICAgICAgICAgIGhhbmRsZUV2ZW50Q2xpY2socGUuZXZlbnQpO1xuLy8gICAgICAgICAgICAgICAgIH19XG4vLyAgICAgICAgICAgICAgICAgaXNEcmFnZ2luZz17YWN0aXZlRHJhZ0RhdGE/LnBheWxvYWQ/LmlkID09PSBwZS5ldmVudC5pZH1cbi8vICAgICAgICAgICAgICAgLz5cbi8vICAgICAgICAgICAgIDwvZGl2PlxuLy8gICAgICAgICAgICkpfVxuXG4vLyAgICAgICAgICAge2hhc01vcmUgJiYgKFxuLy8gICAgICAgICAgICAgPGRpdiBcbi8vICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ibGFjayBob3Zlcjp0ZXh0LWJsYWNrIGZvbnQtbWVkaXVtIHRleHQteHMgY3Vyc29yLXBvaW50ZXJcIlxuLy8gICAgICAgICAgICAgICBzdHlsZT17e1xuLy8gICAgICAgICAgICAgICAgIHBvc2l0aW9uOiAnYWJzb2x1dGUnLFxuLy8gICAgICAgICAgICAgICAgIHRvcDogYCR7TUFYX1ZJU0lCTEVfRVZFTlRTICogUk9XX0hFSUdIVH1weGAsXG4vLyAgICAgICAgICAgICAgICAgbGVmdDogJzJweCcsXG4vLyAgICAgICAgICAgICAgIH19XG4vLyAgICAgICAgICAgICAgIG9uQ2xpY2s9eyhlKSA9PiB7XG4vLyAgICAgICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKTtcbi8vICAgICAgICAgICAgICAgICBzZXRTZWxlY3RlZERhdGUoZGF5KTtcbi8vICAgICAgICAgICAgICAgfX1cbi8vICAgICAgICAgICAgID5cbi8vICAgICAgICAgICAgICAgKyB7aGlkZGVuRXZlbnRzQ291bnR9IG1vcmVcbi8vICAgICAgICAgICAgIDwvZGl2PlxuLy8gICAgICAgICAgICl9XG4vLyAgICAgICAgIDwvZGl2PlxuLy8gICAgICAgPC8+XG4vLyAgICAgKTtcbi8vICAgfTtcblxuLy8gICAvLyBSZW5kZXIgbWFpbiB2aWV3XG4vLyAgIHJldHVybiAoXG4vLyAgICAgPGRpdiBjbGFzc05hbWU9XCJoLWZ1bGwgYmctYmFja2dyb3VuZCBmbGV4IGZsZXgtY29sIGxnOmZsZXgtcm93XCI+XG4vLyAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBmbGV4IGZsZXgtY29sIG1pbi1oLTBcIj5cbi8vICAgICAgICAgey8qIERheSBIZWFkZXJzICovfVxuLy8gICAgICAgICA8ZGl2IFxuLy8gICAgICAgICAgIGRhdGEtZGF5LWhlYWRlcnM9XCJ0cnVlXCJcbi8vICAgICAgICAgICBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy03IGJvcmRlci1iIGJvcmRlci1uZXV0cmFsLTMwMCBzdGlja3kgdG9wLTAgei0xMCBiZy13aGl0ZVwiXG4vLyAgICAgICAgID5cbi8vICAgICAgICAgICB7WydTdW5kYXknLCAnTW9uZGF5JywgJ1R1ZXNkYXknLCAnV2VkbmVzZGF5JywgJ1RodXJzZGF5JywgJ0ZyaWRheScsICdTYXR1cmRheSddLm1hcChkYXlOYW1lID0+IChcbi8vICAgICAgICAgICAgIDxkaXYga2V5PXtkYXlOYW1lfSBjbGFzc05hbWU9e2NuKFxuLy8gICAgICAgICAgICAgICBcInRleHQtY2VudGVyIGZvbnQtc2VtaWJvbGQgdGV4dC1ibGFja1wiLFxuLy8gICAgICAgICAgICAgICBcInB5LTIgdGV4dC14c1wiXG4vLyAgICAgICAgICAgICApfT5cbi8vICAgICAgICAgICAgICAge2RheU5hbWUuc3Vic3RyaW5nKDAsIDMpfVxuLy8gICAgICAgICAgICAgPC9kaXY+XG4vLyAgICAgICAgICAgKSl9XG4vLyAgICAgICAgIDwvZGl2PlxuXG4vLyAgICAgICAgIHsvKiBNb250aCBHcmlkICovfVxuLy8gICAgICAgICA8U2Nyb2xsQXJlYSBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbi8vICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTcgYm9yZGVyLW5ldXRyYWwtMzAwIGJvcmRlci1iXCI+XG4vLyAgICAgICAgICAgICB7bW9udGhDYWxjdWxhdGlvbnMud2Vla3MubWFwKCh3ZWVrLCB3ZWVrSW5kZXgpID0+XG4vLyAgICAgICAgICAgICAgIHdlZWsubWFwKChkYXksIGRheUluZGV4KSA9PiB7XG4vLyAgICAgICAgICAgICAgICAgY29uc3Qgd2Vla0V2ZW50cyA9IHBvc2l0aW9uZWRFdmVudHNCeVdlZWsuZ2V0KHdlZWtJbmRleCkgfHwgW107XG4vLyAgICAgICAgICAgICAgICAgY29uc3QgZGF5RXZlbnRzID0gd2Vla0V2ZW50cy5maWx0ZXIocGUgPT4gcGUuc3RhcnREYXlJbmRleCA9PT0gZGF5SW5kZXgpO1xuLy8gICAgICAgICAgICAgICAgIGNvbnN0IGlzQ3VycmVudE1vbnRoID0gZGF5LmdldE1vbnRoKCkgPT09IHNlbGVjdGVkRGF0ZS5nZXRNb250aCgpO1xuXG4vLyAgICAgICAgICAgICAgICAgcmV0dXJuIChcbi8vICAgICAgICAgICAgICAgICAgIDxEYXlDZWxsXG4vLyAgICAgICAgICAgICAgICAgICAgIGtleT17YCR7d2Vla0luZGV4fS0ke2RheUluZGV4fWB9XG4vLyAgICAgICAgICAgICAgICAgICAgIGRhdGU9e2RheX1cbi8vICAgICAgICAgICAgICAgICAgICAgaXNDdXJyZW50TW9udGg9e2lzQ3VycmVudE1vbnRofVxuLy8gICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTZWxlY3RlZERhdGUoZGF5KX1cbi8vICAgICAgICAgICAgICAgICAgID5cbi8vICAgICAgICAgICAgICAgICAgICAge3JlbmRlckRheUNlbGxDb250ZW50KGRheSwgZGF5RXZlbnRzKX1cbi8vICAgICAgICAgICAgICAgICAgIDwvRGF5Q2VsbD5cbi8vICAgICAgICAgICAgICAgICApO1xuLy8gICAgICAgICAgICAgICB9KSxcbi8vICAgICAgICAgICAgICl9XG4vLyAgICAgICAgICAgPC9kaXY+XG4vLyAgICAgICAgIDwvU2Nyb2xsQXJlYT5cbi8vICAgICAgIDwvZGl2PlxuXG4vLyAgICAgICA8Q2FsZW5kYXJTaWRlQ2FyZFxuLy8gICAgICAgICBzZWxlY3RlZERhdGU9e3NlbGVjdGVkRGF0ZX1cbi8vICAgICAgICAgZXZlbnRzPXtldmVudHN9XG4vLyAgICAgICAgIHNlbGVjdGVkRXZlbnQ9e3NlbGVjdGVkRXZlbnR9XG4vLyAgICAgICAgIHNldFNlbGVjdGVkRXZlbnQ9e3NldFNlbGVjdGVkRXZlbnR9XG4vLyAgICAgICAgIGhhbmRsZUV2ZW50Q2xpY2s9e2hhbmRsZUV2ZW50Q2xpY2t9XG4vLyAgICAgICAvPlxuLy8gICAgIDwvZGl2PlxuLy8gICApO1xuLy8gfTtcblxuLy8gZnVuY3Rpb24gaXNNdWx0aURheShldmVudDogQ2FsZW5kYXJFdmVudCk6IGJvb2xlYW4ge1xuLy8gICByZXR1cm4gIWlzU2FtZURheShuZXcgRGF0ZShldmVudC5zdGFydCksIG5ldyBEYXRlKGV2ZW50LmVuZCkpO1xuLy8gfSJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZU1lbW8iLCJmb3JtYXQiLCJzdGFydE9mTW9udGgiLCJlbmRPZk1vbnRoIiwic3RhcnRPZldlZWsiLCJlbmRPZldlZWsiLCJhZGREYXlzIiwiaXNTYW1lRGF5IiwiaXNUb2RheSIsImNuIiwiQnV0dG9uIiwiUGx1c0ljb24iLCJTY3JvbGxBcmVhIiwidXNlTWF5YmVSZWNvcmQiLCJDYWxlbmRhckV2ZW50SXRlbSIsIk5vRXZlbnRzIiwiQ2FsZW5kYXJTaWRlQ2FyZCIsInVzZURyb3BwYWJsZSIsIkRheUNlbGwiLCJkYXRlIiwiY2hpbGRyZW4iLCJvbkNsaWNrIiwiaXNDdXJyZW50TW9udGgiLCJzZXROb2RlUmVmIiwiaXNPdmVyIiwiaWQiLCJkYXRhIiwidHlwZSIsImRpdiIsInJlZiIsImNsYXNzTmFtZSIsImV2ZW50QWZmZWN0c0RheSIsImV2ZW50IiwiZGF5IiwiZXZlbnRTdGFydCIsIkRhdGUiLCJzdGFydCIsImV2ZW50RW5kIiwiZW5kIiwiZGF5U3RhcnQiLCJkYXlFbmQiLCJzZXRIb3VycyIsInVzZU1vbnRoRXZlbnRzIiwid2Vla3MiLCJldmVudHMiLCJwb3NpdGlvbmVkRXZlbnRzQnlXZWVrIiwiTWFwIiwic2xvdFVzYWdlQnlEYXkiLCJmb3JFYWNoIiwid2VlayIsIndlZWtJbmRleCIsIndlZWtTdGFydCIsIndlZWtFbmQiLCJ3ZWVrRXZlbnRzIiwiZmlsdGVyIiwic3Bhbm5pbmdFdmVudHMiLCJzdGFydERheUluZGV4IiwiZmluZEluZGV4IiwiZW5kRGF5SW5kZXgiLCJhY3R1YWxTdGFydCIsImFjdHVhbEVuZCIsImV2ZW50U3BhbnNXZWVrIiwicHVzaCIsImNvbFNwYW4iLCJzb3J0ZWRFdmVudHMiLCJzb3J0IiwiYSIsImIiLCJwb3NpdGlvbmVkIiwicm93cyIsImV2ZW50RGF0YSIsImFmZmVjdGVkRGF5cyIsInNsaWNlIiwiY2FuUGxhY2UiLCJldmVyeSIsImRheUtleSIsImN1cnJlbnRVc2FnZSIsImdldCIsImFzc2lnbmVkIiwiaSIsImxlbmd0aCIsInJvdyIsImhhc0NvbmZsaWN0Iiwic29tZSIsImV4aXN0aW5nRXZlbnQiLCJzZXQiLCJNb250aFZpZXciLCJzZWxlY3RlZERhdGUiLCJzZWxlY3RlZEV2ZW50Iiwic2V0U2VsZWN0ZWRFdmVudCIsInNldFNlbGVjdGVkRGF0ZSIsIm9wZW5BZGRFdmVudEZvcm0iLCJjYW5FZGl0RGF0YSIsImhhbmRsZUV2ZW50Q2xpY2siLCJhY3RpdmVEcmFnRGF0YSIsIm1heWJlUmVjb3JkIiwiaXNJblJlY29yZFRhYiIsIm1vbnRoQ2FsY3VsYXRpb25zIiwibW9udGhTdGFydCIsIm1vbnRoRW5kIiwic3RhcnREYXkiLCJ3ZWVrU3RhcnRzT24iLCJlbmREYXkiLCJkYXlzIiwibW9udGhFdmVudHMiLCJyZW5kZXJFbXB0eVN0YXRlIiwibWFwIiwiZGF5TmFtZSIsInN1YnN0cmluZyIsInRpdGxlIiwibWVzc2FnZSIsInNob3dDcmVhdGVCdXR0b24iLCJvbkNyZWF0ZSIsInJlbmRlckRheUNlbGxDb250ZW50IiwiZGF5RXZlbnRzIiwiZ2V0TW9udGgiLCJpc0N1cnJlbnREYXkiLCJNQVhfVklTSUJMRV9FVkVOVFMiLCJST1dfSEVJR0hUIiwiYWxsRGF5RXZlbnRzIiwidG90YWxFdmVudHNGb3JEYXkiLCJ2aXNpYmxlRXZlbnRzIiwiaGFzTW9yZSIsImhpZGRlbkV2ZW50c0NvdW50IiwiTWF0aCIsIm1heCIsIm1heFJvdyIsInJlZHVjZSIsImNvbnRhaW5lckhlaWdodCIsInNwYW4iLCJ2YXJpYW50IiwiZSIsInN0b3BQcm9wYWdhdGlvbiIsInNldFRpbWVvdXQiLCJzdHlsZSIsImhlaWdodCIsInBlIiwidG9wIiwibGVmdCIsIndpZHRoIiwiekluZGV4IiwidmlldyIsImlzRHJhZ2dpbmciLCJwYXlsb2FkIiwicG9zaXRpb24iLCJkYXRhLWRheS1oZWFkZXJzIiwiZGF5SW5kZXgiLCJpc011bHRpRGF5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/index.tsx":
/*!****************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/index.tsx ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CalendarView: function() { return /* binding */ CalendarView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! opendb-app-db-utils/lib/typings/db */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/typings/db.js\");\n/* harmony import */ var _components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/custom-ui/loader */ \"(app-pages-browser)/./src/components/custom-ui/loader.tsx\");\n/* harmony import */ var _providers_page__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/page */ \"(app-pages-browser)/./src/providers/page.tsx\");\n/* harmony import */ var _providers_shared__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/providers/shared */ \"(app-pages-browser)/./src/providers/shared.tsx\");\n/* harmony import */ var _providers_template__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/providers/template */ \"(app-pages-browser)/./src/providers/template.tsx\");\n/* harmony import */ var _providers_views__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/providers/views */ \"(app-pages-browser)/./src/providers/views.tsx\");\n/* harmony import */ var _components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/workspace/main/views/table */ \"(app-pages-browser)/./src/components/workspace/main/views/table/index.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/icons/FontAwesomeRegular */ \"(app-pages-browser)/./src/components/icons/FontAwesomeRegular.tsx\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/subWeeks/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/subMonths/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addWeeks/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addMonths/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _providers_screenSize__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/providers/screenSize */ \"(app-pages-browser)/./src/providers/screenSize.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _providers_stackedPeek__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/providers/stackedPeek */ \"(app-pages-browser)/./src/providers/stackedPeek.tsx\");\n/* harmony import */ var _components_DayView__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./components/DayView */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/DayView.tsx\");\n/* harmony import */ var _components_WeekView__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./components/WeekView */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/WeekView.tsx\");\n/* harmony import */ var _components_MonthView__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./components/MonthView */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx\");\n/* harmony import */ var _components_CalendarEventItem__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./components/CalendarEventItem */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx\");\n/* harmony import */ var _components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/workspace/main/views/form/components/element/linked */ \"(app-pages-browser)/./src/components/workspace/main/views/form/components/element/linked.tsx\");\n/* harmony import */ var _components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/custom-ui/customSelect */ \"(app-pages-browser)/./src/components/custom-ui/customSelect.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n/* harmony import */ var _components_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./components/CalendarEventSegment */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx\");\n/* harmony import */ var _barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=differenceInDays,differenceInMilliseconds!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/differenceInMilliseconds/index.js\");\n/* harmony import */ var _barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=differenceInDays,differenceInMilliseconds!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/differenceInDays/index.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Custom modifier to restrict dragging to the calendar main content area only\nconst restrictToCalendarContainer = (param)=>{\n    let { transform, draggingNodeRect, windowRect } = param;\n    if (!draggingNodeRect || !windowRect) {\n        return transform;\n    }\n    // Find the calendar main content container (the div that contains the views)\n    const calendarContainer = document.querySelector('[data-calendar-content=\"true\"]');\n    if (!calendarContainer) {\n        return transform;\n    }\n    const containerRect = calendarContainer.getBoundingClientRect();\n    // For month view, we need to account for the side card\n    // The side card is positioned on the right side of the calendar\n    const sideCard = document.querySelector('[data-side-card=\"true\"]');\n    let maxX = containerRect.right - draggingNodeRect.width;\n    if (sideCard) {\n        const sideCardRect = sideCard.getBoundingClientRect();\n        // Restrict dragging to not go past the side card\n        maxX = Math.min(maxX, sideCardRect.left - draggingNodeRect.width);\n    }\n    // Find header areas to prevent dragging over them\n    const timeLabels = document.querySelector('[data-time-labels=\"true\"]');\n    const dayHeaders = document.querySelector('[data-day-headers=\"true\"]');\n    const allDayRow = document.querySelector('[data-all-day-row=\"true\"]');\n    // Calculate the boundaries relative to the window\n    let minX = containerRect.left;\n    let minY = containerRect.top;\n    const maxY = containerRect.bottom - draggingNodeRect.height;\n    // Prevent dragging over time labels (left side in day/week view)\n    if (timeLabels) {\n        const timeLabelsRect = timeLabels.getBoundingClientRect();\n        minX = Math.max(minX, timeLabelsRect.right);\n    }\n    // Prevent dragging over day headers (top of calendar)\n    if (dayHeaders) {\n        const dayHeadersRect = dayHeaders.getBoundingClientRect();\n        minY = Math.max(minY, dayHeadersRect.bottom);\n    }\n    // Prevent dragging over all-day row (if present)\n    if (allDayRow) {\n        const allDayRowRect = allDayRow.getBoundingClientRect();\n        minY = Math.max(minY, allDayRowRect.bottom);\n    }\n    // Get current pointer position\n    const currentX = transform.x + draggingNodeRect.left;\n    const currentY = transform.y + draggingNodeRect.top;\n    // Constrain the position\n    const constrainedX = Math.min(Math.max(currentX, minX), maxX);\n    const constrainedY = Math.min(Math.max(currentY, minY), maxY);\n    return {\n        ...transform,\n        x: constrainedX - draggingNodeRect.left,\n        y: constrainedY - draggingNodeRect.top\n    };\n};\n// Custom hook to track previous value\nconst usePrevious = (value)=>{\n    _s();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        ref.current = value;\n    });\n    return ref.current;\n};\n_s(usePrevious, \"8uVE59eA/r6b92xF80p7sH8rXLk=\");\nconst CalendarView = (props)=>{\n    _s1();\n    const { databaseStore, members, workspace } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace)();\n    const { definition } = props;\n    const { accessLevel } = (0,_providers_page__WEBPACK_IMPORTED_MODULE_5__.usePage)();\n    const { isMobile } = (0,_providers_screenSize__WEBPACK_IMPORTED_MODULE_16__.useScreenSize)();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_17__.useMaybeRecord)();\n    const maybeShared = (0,_providers_shared__WEBPACK_IMPORTED_MODULE_6__.useMaybeShared)();\n    const maybeTemplate = (0,_providers_template__WEBPACK_IMPORTED_MODULE_7__.useMaybeTemplate)();\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [viewType, setViewType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"week\");\n    const [selectedEvent, setSelectedEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showSideCalendar, setShowSideCalendar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!isMobile);\n    const [activeDragData, setActiveDragData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const savedScrollTop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const pointerCoordinates = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        x: 0,\n        y: 0\n    });\n    const isInRecordTab = !!maybeRecord;\n    definition.filter = definition.filter || {\n        conditions: [],\n        match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Match.All\n    };\n    definition.sorts = definition.sorts || [];\n    const databaseId = definition.databaseId;\n    const database = databaseStore[definition.databaseId];\n    const isPublishedView = !!maybeShared;\n    const editable = !definition.lockContent && !isPublishedView && !!accessLevel;\n    let canEditStructure = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    let canEditData = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    const { createRecords, updateRecordValues, setPeekRecordId, peekRecordId, refreshDatabase, deleteRecords } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViews)();\n    const { sorts, filter, search } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewFiltering)();\n    const { selectedIds, setSelectedIds } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewSelection)();\n    const prevPeekRecordId = usePrevious(peekRecordId);\n    const { openRecord } = (0,_providers_stackedPeek__WEBPACK_IMPORTED_MODULE_18__.useStackedPeek)();\n    const sensors = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensors)((0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.PointerSensor, {\n        activationConstraint: {\n            distance: 8\n        }\n    }), (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.TouchSensor, {\n        activationConstraint: {\n            delay: 150,\n            tolerance: 5\n        }\n    }));\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const containerId = viewType === \"day\" ? \"day-view-container\" : \"week-view-container\";\n        const container = document.getElementById(containerId);\n        if (container) {\n            requestAnimationFrame(()=>{\n                container.scrollTop = savedScrollTop.current;\n            });\n        }\n    }, [\n        selectedEvent,\n        viewType\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setShowSideCalendar(!isMobile);\n    }, [\n        isMobile\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only refresh if the peek view was open and is now closed.\n        if (prevPeekRecordId && !peekRecordId) {\n            console.log(\"Peek view was closed, refreshing calendar data\");\n            refreshDatabase(definition.databaseId);\n        }\n    }, [\n        peekRecordId,\n        prevPeekRecordId,\n        definition.databaseId,\n        refreshDatabase\n    ]);\n    if (!database) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_4__.PageLoader, {\n        size: \"full\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n        lineNumber: 211,\n        columnNumber: 25\n    }, undefined);\n    const getEvents = ()=>{\n        var _workspace_workspaceMember, _database_database;\n        if (!database) return [];\n        const { rows } = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__.filterAndSortRecords)(database, members, databaseStore, definition.filter || {\n            match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Match.All,\n            conditions: []\n        }, filter, sorts.length ? sorts : definition.sorts || [], (workspace === null || workspace === void 0 ? void 0 : (_workspace_workspaceMember = workspace.workspaceMember) === null || _workspace_workspaceMember === void 0 ? void 0 : _workspace_workspaceMember.userId) || \"\", (database === null || database === void 0 ? void 0 : (_database_database = database.database) === null || _database_database === void 0 ? void 0 : _database_database.id) || \"\");\n        const filteredRows = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__.searchFilteredRecords)(search || \"\", rows);\n        const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getDatabaseTitleCol)(database.database);\n        return filteredRows.map((row)=>{\n            const startValue = row.processedRecord.processedRecordValues[definition.eventStartColumnId];\n            let startDate;\n            if (startValue && typeof startValue === \"string\") {\n                startDate = new Date(startValue);\n            } else {\n                startDate = new Date();\n            }\n            let endDate;\n            if (definition.eventEndColumnId) {\n                const endValue = row.processedRecord.processedRecordValues[definition.eventEndColumnId];\n                if (endValue && typeof endValue === \"string\") {\n                    endDate = new Date(endValue);\n                } else {\n                    endDate = new Date(startDate.getTime() + (definition.defaultDuration || 30) * 60000);\n                }\n            } else {\n                endDate = new Date(startDate.getTime() + (definition.defaultDuration || 30) * 60000);\n            }\n            const title = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getRecordTitle)(row.record, titleColOpts.titleColId, titleColOpts.defaultTitle, titleColOpts.isContacts);\n            return {\n                id: row.id,\n                title,\n                start: startDate,\n                end: endDate,\n                record: row.record,\n                processedRecord: row.processedRecord\n            };\n        });\n    };\n    const getFilteredEvents = ()=>{\n        const baseEvents = getEvents();\n        if (!searchTerm.trim()) {\n            return baseEvents;\n        }\n        return baseEvents.filter((event)=>{\n            const searchLower = searchTerm.toLowerCase();\n            return event.title.toLowerCase().includes(searchLower);\n        });\n    };\n    const onDragStart = (event)=>{\n        if (event.active.data.current) {\n            var _event_active_rect_current_translated, _event_active_rect_current, _event_active_rect_current_translated1, _event_active_rect_current_translated2;\n            const initialPointerY = pointerCoordinates.current.y;\n            var _event_active_rect_current_translated_top;\n            const initialEventTop = (_event_active_rect_current_translated_top = (_event_active_rect_current = event.active.rect.current) === null || _event_active_rect_current === void 0 ? void 0 : (_event_active_rect_current_translated = _event_active_rect_current.translated) === null || _event_active_rect_current_translated === void 0 ? void 0 : _event_active_rect_current_translated.top) !== null && _event_active_rect_current_translated_top !== void 0 ? _event_active_rect_current_translated_top : 0;\n            const grabOffsetY = initialPointerY - initialEventTop;\n            // Get the exact dimensions from the DOM element\n            // Handle both event and segment types\n            const { payload, type } = event.active.data.current;\n            const eventId = type === \"segment\" ? payload.originalEvent.id : payload.id;\n            const draggedElement = document.getElementById(\"event-\".concat(eventId));\n            const width = draggedElement ? draggedElement.offsetWidth : (_event_active_rect_current_translated1 = event.active.rect.current.translated) === null || _event_active_rect_current_translated1 === void 0 ? void 0 : _event_active_rect_current_translated1.width;\n            const height = draggedElement ? draggedElement.offsetHeight : (_event_active_rect_current_translated2 = event.active.rect.current.translated) === null || _event_active_rect_current_translated2 === void 0 ? void 0 : _event_active_rect_current_translated2.height;\n            setActiveDragData({\n                ...event.active.data.current,\n                grabOffsetY,\n                width,\n                height\n            });\n            document.addEventListener(\"mousemove\", handleMouseMove);\n            document.addEventListener(\"touchmove\", handleTouchMove);\n        }\n    };\n    const onDragEnd = async (param)=>{\n        let { active, over } = param;\n        document.removeEventListener(\"mousemove\", handleMouseMove);\n        document.removeEventListener(\"touchmove\", handleTouchMove);\n        setActiveDragData(null);\n        if (!over || !active || !canEditData || active.id === over.id) {\n            return;\n        }\n        const activeData = active.data.current;\n        const overData = over.data.current;\n        if (!activeData || !overData) {\n            return;\n        }\n        const { payload, type } = activeData;\n        const eventToUpdate = type === \"segment\" ? payload.originalEvent : payload;\n        const originalStart = new Date(eventToUpdate.start);\n        const originalEnd = new Date(eventToUpdate.end);\n        const duration = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_27__[\"default\"])(originalEnd, originalStart);\n        let newStart;\n        if (overData.type.startsWith(\"allday\")) {\n            const dayDifference = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_28__[\"default\"])(overData.date, (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_29__[\"default\"])(originalStart));\n            newStart = (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(originalStart, dayDifference);\n            newStart.setHours(0, 0, 0, 0);\n        } else if (overData.type === \"timeslot-minute\") {\n            // Handle precise minute-based drops\n            newStart = new Date(overData.date);\n            newStart.setHours(overData.hour, overData.minute, 0, 0);\n        } else if (overData.type === \"daycell\") {\n            const dayDifference = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_28__[\"default\"])(overData.date, (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_29__[\"default\"])(originalStart));\n            newStart = (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(originalStart, dayDifference);\n        } else {\n            return;\n        }\n        const newEnd = new Date(newStart.getTime() + duration);\n        const recordId = eventToUpdate.record.id;\n        const newValues = {\n            [definition.eventStartColumnId]: newStart.toISOString(),\n            ...definition.eventEndColumnId && {\n                [definition.eventEndColumnId]: newEnd.toISOString()\n            }\n        };\n        try {\n            await updateRecordValues(definition.databaseId, [\n                recordId\n            ], newValues);\n            sonner__WEBPACK_IMPORTED_MODULE_14__.toast.success('Event \"'.concat(eventToUpdate.title, '\" updated.'));\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Failed to update event.\");\n            console.error(\"Error updating event:\", error);\n        }\n    };\n    const handleMouseMove = (event)=>{\n        pointerCoordinates.current = {\n            x: event.clientX,\n            y: event.clientY\n        };\n    };\n    const handleTouchMove = (event)=>{\n        const touch = event.touches[0];\n        pointerCoordinates.current = {\n            x: touch.clientX,\n            y: touch.clientY\n        };\n    };\n    const events = getFilteredEvents();\n    const goToToday = ()=>setSelectedDate(new Date());\n    const goToPrevious = ()=>{\n        switch(viewType){\n            case \"day\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(prevDate, -1));\n                break;\n            case \"week\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_31__[\"default\"])(prevDate, 1));\n                break;\n            case \"month\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_32__[\"default\"])(prevDate, 1));\n                break;\n        }\n    };\n    const goToNext = ()=>{\n        switch(viewType){\n            case \"day\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(prevDate, 1));\n                break;\n            case \"week\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_33__[\"default\"])(prevDate, 1));\n                break;\n            case \"month\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_34__[\"default\"])(prevDate, 1));\n                break;\n        }\n    };\n    const handleRequestCreateEvent = function(date) {\n        let useSystemTime = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (useSystemTime) {\n            const now = new Date();\n            const newDate = new Date(date);\n            newDate.setHours(now.getHours(), now.getMinutes(), now.getSeconds(), now.getMilliseconds());\n            handleCreateEvent(newDate);\n        } else {\n            handleCreateEvent(date);\n        }\n    };\n    const handleCreateEvent = async function() {\n        let date = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : new Date();\n        if (!canEditData) return;\n        const startTime = new Date(date);\n        const endTime = new Date(startTime.getTime() + (definition.defaultDuration || 30) * 60000);\n        const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getDatabaseTitleCol)(database.database);\n        try {\n            const recordValues = {\n                [definition.eventStartColumnId]: startTime.toISOString(),\n                ...definition.eventEndColumnId && {\n                    [definition.eventEndColumnId]: endTime.toISOString()\n                }\n            };\n            if (titleColOpts.titleColId) {\n                recordValues[titleColOpts.titleColId] = \"New Event\";\n            }\n            const result = await createRecords(definition.databaseId, [\n                recordValues\n            ]);\n            if (result && result.records && result.records.length > 0) {\n                const newRecordId = result.records[0].id;\n                if (newRecordId) {\n                    await refreshDatabase(definition.databaseId);\n                    setPeekRecordId(newRecordId);\n                    sonner__WEBPACK_IMPORTED_MODULE_14__.toast.success(\"New event created\");\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Error accessing the new event\");\n                }\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Failed to create event properly\");\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Failed to create event\");\n        }\n    };\n    const handleEventClick = (event)=>{\n        if (event && event.id) {\n            const containerId = viewType === \"day\" ? \"day-view-container\" : \"week-view-container\";\n            const container = document.getElementById(containerId);\n            if (container) {\n                savedScrollTop.current = container.scrollTop;\n            }\n            openRecord(event.id, event.record.databaseId);\n            setSelectedEvent(event.id);\n        }\n    };\n    const getHeaderDateDisplay = ()=>{\n        switch(viewType){\n            case \"day\":\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(selectedDate, \"MMMM d, yyyy\");\n            case \"week\":\n                return \"\".concat((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(selectedDate, -selectedDate.getDay()), \"MMM d\"), \" - \").concat((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(selectedDate, 6 - selectedDate.getDay()), \"MMM d, yyyy\"));\n            case \"month\":\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(selectedDate, \"MMMM yyyy\");\n            default:\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(selectedDate, \"MMMM d, yyyy\");\n        }\n    };\n    const handleEventDelete = async (event)=>{\n        if (!canEditData) return;\n        try {\n            await deleteRecords(database.database.id, [\n                event.record.id\n            ]);\n        } catch (error) {\n            console.error(\"Error deleting event:\", error);\n        }\n    };\n    const viewTypeOptions = [\n        {\n            id: \"day\",\n            value: \"day\",\n            title: \"Day\",\n            data: \"day\"\n        },\n        {\n            id: \"week\",\n            value: \"week\",\n            title: \"Week\",\n            data: \"week\"\n        },\n        {\n            id: \"month\",\n            value: \"month\",\n            title: \"Month\",\n            data: \"month\"\n        }\n    ];\n    const selectedViewOption = viewType === \"day\" ? [\n        \"day\"\n    ] : viewType === \"week\" ? [\n        \"week\"\n    ] : [\n        \"month\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"border-b border-neutral-300 bg-white\", isInRecordTab && \"py-1\"),\n                children: [\n                    isMobile ? /* Mobile Header Layout */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"p-2\", isInRecordTab && \"py-1\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xs font-semibold text-black truncate flex-1 mr-2\",\n                                        children: getHeaderDateDisplay()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: ()=>setShowSideCalendar(!showSideCalendar),\n                                        className: \"rounded-full h-8 px-3 text-xs text-black hover:bg-gray-50\",\n                                        children: \"Calendar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 509,\n                                columnNumber: 12\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToToday,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs text-black hover:bg-gray-50\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                children: \"Today\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        variant: \"ghost\",\n                                                        onClick: goToPrevious,\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleLeftIcon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 20\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 18\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        variant: \"ghost\",\n                                                        onClick: goToNext,\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleRightIcon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 20\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 18\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 16\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__.CustomSelect, {\n                                                options: viewTypeOptions,\n                                                selectedIds: selectedViewOption,\n                                                onChange: (selected)=>{\n                                                    if (selected.length > 0) {\n                                                        setViewType(selected[0]);\n                                                    }\n                                                },\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"px-3 text-xs border-neutral-300 rounded-full bg-white hover:bg-gray-50 text-black w-20\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                placeholder: \"View\",\n                                                hideSearch: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                onClick: ()=>handleRequestCreateEvent(selectedDate, true),\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs border border-neutral-300 bg-white hover:bg-gray-50 text-black\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.PlusIcon, {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 584,\n                                                    columnNumber: 20\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 577,\n                                                columnNumber: 18\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 558,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 522,\n                                columnNumber: 12\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 508,\n                        columnNumber: 10\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"flex items-center justify-between px-4\", isInRecordTab ? \"py-1\" : \"py-2\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: goToToday,\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs text-black hover:bg-gray-50\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                        children: \"Today\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 596,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToPrevious,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleLeftIcon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 615,\n                                                    columnNumber: 16\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 14\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToNext,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleRightIcon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 625,\n                                                    columnNumber: 16\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 617,\n                                                columnNumber: 14\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 606,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xs font-semibold text-black\",\n                                        children: getHeaderDateDisplay()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 629,\n                                        columnNumber: 12\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 595,\n                                columnNumber: 10\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                                placeholder: \"Search events...\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"w-48 pr-8 h-8 text-xs border-neutral-300 bg-transparent shadow-none\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 637,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.MagnifyingGlassIcon, {\n                                                className: \"h-3 w-3 absolute right-2 top-1/2 transform -translate-y-1/2 text-neutral-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 643,\n                                                columnNumber: 16\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 636,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__.CustomSelect, {\n                                        options: viewTypeOptions,\n                                        selectedIds: selectedViewOption,\n                                        onChange: (selected)=>{\n                                            if (selected.length > 0) {\n                                                setViewType(selected[0]);\n                                            }\n                                        },\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"px-3 text-xs border-neutral-300 rounded-full bg-white hover:bg-gray-50 text-black\", isInRecordTab ? \"h-6 w-20\" : \"h-8 w-28\"),\n                                        placeholder: \"View\",\n                                        hideSearch: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 647,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        onClick: ()=>handleRequestCreateEvent(selectedDate, true),\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs border border-neutral-300 bg-white hover:bg-gray-50 text-black gap-1\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.PlusIcon, {\n                                                className: \"h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 671,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Add Event\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 672,\n                                                columnNumber: 35\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 664,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 634,\n                                columnNumber: 10\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 591,\n                        columnNumber: 8\n                    }, undefined),\n                    isMobile && !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-2 pb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                    placeholder: \"Search events...\",\n                                    value: searchTerm,\n                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                    className: \"w-full pr-8 h-8 text-xs border-neutral-300 bg-transparent shadow-none\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 682,\n                                    columnNumber: 12\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.MagnifyingGlassIcon, {\n                                    className: \"h-3 w-3 absolute right-2 top-1/2 transform -translate-y-1/2 text-neutral-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 688,\n                                    columnNumber: 12\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                            lineNumber: 681,\n                            columnNumber: 10\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 680,\n                        columnNumber: 8\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                lineNumber: 502,\n                columnNumber: 6\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex min-h-0\",\n                children: [\n                    showSideCalendar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"flex-none bg-white\", isMobile ? \"w-full absolute z-50 backdrop-blur-sm h-full shadow-lg\" : \"w-fit border-r border-neutral-300\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center p-2 border-b border-neutral-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xs font-semibold text-black\",\n                                        children: \"Calendar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 702,\n                                        columnNumber: 10\n                                    }, undefined),\n                                    isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: ()=>setShowSideCalendar(false),\n                                        className: \"rounded-full h-8 w-8 p-1 text-black hover:bg-neutral-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Close\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 709,\n                                                columnNumber: 14\n                                            }, undefined),\n                                            \"\\xd7\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 704,\n                                        columnNumber: 12\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 701,\n                                columnNumber: 8\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                mode: \"single\",\n                                selected: selectedDate,\n                                onSelect: (date)=>{\n                                    if (date) {\n                                        setSelectedDate(date);\n                                        if (isMobile) {\n                                            setShowSideCalendar(false);\n                                        }\n                                    }\n                                },\n                                className: \"rounded-md border-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 714,\n                                columnNumber: 8\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 697,\n                        columnNumber: 6\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.DndContext, {\n                        sensors: sensors,\n                        onDragStart: onDragStart,\n                        onDragEnd: onDragEnd,\n                        modifiers: [\n                            restrictToCalendarContainer\n                        ],\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                \"data-calendar-content\": \"true\",\n                                children: [\n                                    viewType === \"day\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DayView__WEBPACK_IMPORTED_MODULE_19__.DayView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        openAddEventForm: handleRequestCreateEvent,\n                                        canEditData: canEditData,\n                                        savedScrollTop: savedScrollTop,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 738,\n                                        columnNumber: 7\n                                    }, undefined),\n                                    viewType === \"week\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_WeekView__WEBPACK_IMPORTED_MODULE_20__.WeekView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        setSelectedDate: setSelectedDate,\n                                        openAddEventForm: handleRequestCreateEvent,\n                                        canEditData: canEditData,\n                                        savedScrollTop: savedScrollTop,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 751,\n                                        columnNumber: 7\n                                    }, undefined),\n                                    viewType === \"month\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MonthView__WEBPACK_IMPORTED_MODULE_21__.MonthView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        setSelectedDate: setSelectedDate,\n                                        openAddEventForm: (date)=>handleRequestCreateEvent(date, true),\n                                        canEditData: canEditData,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 765,\n                                        columnNumber: 7\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 736,\n                                columnNumber: 6\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.DragOverlay, {\n                                dropAnimation: null,\n                                children: activeDragData && activeDragData.type === \"segment\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_26__.CalendarEventSegment, {\n                                    segment: activeDragData.payload,\n                                    view: viewType === \"day\" ? \"day\" : \"week\",\n                                    onClick: ()=>{},\n                                    style: {\n                                        width: activeDragData.width,\n                                        height: activeDragData.height\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 781,\n                                    columnNumber: 11\n                                }, undefined) : activeDragData && activeDragData.type === \"event\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CalendarEventItem__WEBPACK_IMPORTED_MODULE_22__.CalendarEventItem, {\n                                    event: activeDragData.payload,\n                                    view: viewType,\n                                    onClick: ()=>{},\n                                    style: {\n                                        width: activeDragData.width,\n                                        height: activeDragData.height\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 791,\n                                    columnNumber: 11\n                                }, undefined) : null\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 779,\n                                columnNumber: 6\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 730,\n                        columnNumber: 6\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                lineNumber: 695,\n                columnNumber: 4\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n        lineNumber: 501,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(CalendarView, \"gC+3ORgbOSOWj17lR2zBDE1vWrs=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace,\n        _providers_page__WEBPACK_IMPORTED_MODULE_5__.usePage,\n        _providers_screenSize__WEBPACK_IMPORTED_MODULE_16__.useScreenSize,\n        _providers_record__WEBPACK_IMPORTED_MODULE_17__.useMaybeRecord,\n        _providers_shared__WEBPACK_IMPORTED_MODULE_6__.useMaybeShared,\n        _providers_template__WEBPACK_IMPORTED_MODULE_7__.useMaybeTemplate,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViews,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewFiltering,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewSelection,\n        usePrevious,\n        _providers_stackedPeek__WEBPACK_IMPORTED_MODULE_18__.useStackedPeek,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensors\n    ];\n});\n_c = CalendarView;\nvar _c;\n$RefreshReg$(_c, \"CalendarView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3dvcmtzcGFjZS9tYWluL3ZpZXdzL2NhbGVuZGFyL2luZGV4LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXdFO0FBQ25CO0FBQ3lCO0FBQ25CO0FBQ2hCO0FBSVM7QUFDSTtBQUN5QjtBQUNxQjtBQUNsRDtBQUNKO0FBRXFFO0FBQ3lDO0FBQ2hIO0FBQ2Y7QUFDRTtBQUVzQjtBQUNIO0FBQ0s7QUFHVjtBQUNFO0FBQ0U7QUFDZ0I7QUFDb0Q7QUFJcEQ7QUFjNUM7QUFHa0Q7QUFDSDtBQUV0RSw4RUFBOEU7QUFDOUUsTUFBTWtELDhCQUE4QjtRQUFDLEVBQUVDLFNBQVMsRUFBRUMsZ0JBQWdCLEVBQUVDLFVBQVUsRUFBTztJQUNuRixJQUFJLENBQUNELG9CQUFvQixDQUFDQyxZQUFZO1FBQ3BDLE9BQU9GO0lBQ1Q7SUFFQSw2RUFBNkU7SUFDN0UsTUFBTUcsb0JBQW9CQyxTQUFTQyxhQUFhLENBQUM7SUFDakQsSUFBSSxDQUFDRixtQkFBbUI7UUFDdEIsT0FBT0g7SUFDVDtJQUVBLE1BQU1NLGdCQUFnQkgsa0JBQWtCSSxxQkFBcUI7SUFFN0QsdURBQXVEO0lBQ3ZELGdFQUFnRTtJQUNoRSxNQUFNQyxXQUFXSixTQUFTQyxhQUFhLENBQUM7SUFDeEMsSUFBSUksT0FBT0gsY0FBY0ksS0FBSyxHQUFHVCxpQkFBaUJVLEtBQUs7SUFFdkQsSUFBSUgsVUFBVTtRQUNaLE1BQU1JLGVBQWVKLFNBQVNELHFCQUFxQjtRQUNuRCxpREFBaUQ7UUFDakRFLE9BQU9JLEtBQUtDLEdBQUcsQ0FBQ0wsTUFBTUcsYUFBYUcsSUFBSSxHQUFHZCxpQkFBaUJVLEtBQUs7SUFDbEU7SUFFQSxrREFBa0Q7SUFDbEQsTUFBTUssYUFBYVosU0FBU0MsYUFBYSxDQUFDO0lBQzFDLE1BQU1ZLGFBQWFiLFNBQVNDLGFBQWEsQ0FBQztJQUMxQyxNQUFNYSxZQUFZZCxTQUFTQyxhQUFhLENBQUM7SUFFekMsa0RBQWtEO0lBQ2xELElBQUljLE9BQU9iLGNBQWNTLElBQUk7SUFDN0IsSUFBSUssT0FBT2QsY0FBY2UsR0FBRztJQUM1QixNQUFNQyxPQUFPaEIsY0FBY2lCLE1BQU0sR0FBR3RCLGlCQUFpQnVCLE1BQU07SUFFM0QsaUVBQWlFO0lBQ2pFLElBQUlSLFlBQVk7UUFDZCxNQUFNUyxpQkFBaUJULFdBQVdULHFCQUFxQjtRQUN2RFksT0FBT04sS0FBS2EsR0FBRyxDQUFDUCxNQUFNTSxlQUFlZixLQUFLO0lBQzVDO0lBRUEsc0RBQXNEO0lBQ3RELElBQUlPLFlBQVk7UUFDZCxNQUFNVSxpQkFBaUJWLFdBQVdWLHFCQUFxQjtRQUN2RGEsT0FBT1AsS0FBS2EsR0FBRyxDQUFDTixNQUFNTyxlQUFlSixNQUFNO0lBQzdDO0lBRUEsaURBQWlEO0lBQ2pELElBQUlMLFdBQVc7UUFDYixNQUFNVSxnQkFBZ0JWLFVBQVVYLHFCQUFxQjtRQUNyRGEsT0FBT1AsS0FBS2EsR0FBRyxDQUFDTixNQUFNUSxjQUFjTCxNQUFNO0lBQzVDO0lBRUEsK0JBQStCO0lBQy9CLE1BQU1NLFdBQVc3QixVQUFVOEIsQ0FBQyxHQUFHN0IsaUJBQWlCYyxJQUFJO0lBQ3BELE1BQU1nQixXQUFXL0IsVUFBVWdDLENBQUMsR0FBRy9CLGlCQUFpQm9CLEdBQUc7SUFFbkQseUJBQXlCO0lBQ3pCLE1BQU1ZLGVBQWVwQixLQUFLQyxHQUFHLENBQUNELEtBQUthLEdBQUcsQ0FBQ0csVUFBVVYsT0FBT1Y7SUFDeEQsTUFBTXlCLGVBQWVyQixLQUFLQyxHQUFHLENBQUNELEtBQUthLEdBQUcsQ0FBQ0ssVUFBVVgsT0FBT0U7SUFFeEQsT0FBTztRQUNMLEdBQUd0QixTQUFTO1FBQ1o4QixHQUFHRyxlQUFlaEMsaUJBQWlCYyxJQUFJO1FBQ3ZDaUIsR0FBR0UsZUFBZWpDLGlCQUFpQm9CLEdBQUc7SUFDeEM7QUFDRjtBQUlBLHNDQUFzQztBQUN0QyxNQUFNYyxjQUFjLENBQUtDOztJQUN2QixNQUFNQyxNQUFNdEYsNkNBQU1BO0lBQ2xCQyxnREFBU0EsQ0FBQztRQUNScUYsSUFBSUMsT0FBTyxHQUFHRjtJQUNoQjtJQUNBLE9BQU9DLElBQUlDLE9BQU87QUFDcEI7R0FOTUg7QUFRQyxNQUFNSSxlQUFlLENBQUNDOztJQUMzQixNQUFNLEVBQUVDLGFBQWEsRUFBRUMsT0FBTyxFQUFFQyxTQUFTLEVBQUUsR0FBRzFGLGtFQUFZQTtJQUMxRCxNQUFNLEVBQUUyRixVQUFVLEVBQUUsR0FBR0o7SUFDdkIsTUFBTSxFQUFFSyxXQUFXLEVBQUUsR0FBR3pGLHdEQUFPQTtJQUMvQixNQUFNLEVBQUUwRixRQUFRLEVBQUUsR0FBR2xFLHFFQUFhQTtJQUNsQyxNQUFNbUUsY0FBY2xFLGtFQUFjQTtJQUVsQyxNQUFNbUUsY0FBYzNGLGlFQUFjQTtJQUNsQyxNQUFNNEYsZ0JBQWdCM0YscUVBQWdCQTtJQUV0QyxNQUFNLENBQUM0RixjQUFjQyxnQkFBZ0IsR0FBR3JHLCtDQUFRQSxDQUFPLElBQUlzRztJQUMzRCxNQUFNLENBQUNDLFVBQVVDLFlBQVksR0FBR3hHLCtDQUFRQSxDQUFtQjtJQUMzRCxNQUFNLENBQUN5RyxlQUFlQyxpQkFBaUIsR0FBRzFHLCtDQUFRQSxDQUFnQjtJQUNsRSxNQUFNLENBQUMyRyxZQUFZQyxjQUFjLEdBQUc1RywrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUM2RyxrQkFBa0JDLG9CQUFvQixHQUFHOUcsK0NBQVFBLENBQUMsQ0FBQ2dHO0lBQzFELE1BQU0sQ0FBQ2UsZ0JBQWdCQyxrQkFBa0IsR0FBR2hILCtDQUFRQSxDQUFhO0lBQ2pFLE1BQU1pSCxpQkFBaUJoSCw2Q0FBTUEsQ0FBQztJQUM5QixNQUFNaUgscUJBQXFCakgsNkNBQU1BLENBQUM7UUFBRStFLEdBQUc7UUFBR0UsR0FBRztJQUFFO0lBRS9DLE1BQU1pQyxnQkFBZ0IsQ0FBQyxDQUFDbEI7SUFFeEJILFdBQVdzQixNQUFNLEdBQUd0QixXQUFXc0IsTUFBTSxJQUFJO1FBQUVDLFlBQVksRUFBRTtRQUFFQyxPQUFPbEgscUVBQUtBLENBQUNtSCxHQUFHO0lBQUM7SUFDNUV6QixXQUFXMEIsS0FBSyxHQUFHMUIsV0FBVzBCLEtBQUssSUFBSSxFQUFFO0lBRXpDLE1BQU1DLGFBQWEzQixXQUFXMkIsVUFBVTtJQUN4QyxNQUFNQyxXQUFXL0IsYUFBYSxDQUFDRyxXQUFXMkIsVUFBVSxDQUFDO0lBRXJELE1BQU1FLGtCQUFrQixDQUFDLENBQUN6QjtJQUMxQixNQUFNMEIsV0FBVyxDQUFDOUIsV0FBVytCLFdBQVcsSUFBSSxDQUFDRixtQkFBbUIsQ0FBQyxDQUFDNUI7SUFFbEUsSUFBSStCLG1CQUFtQixDQUFDLENBQUUsRUFBQzNCLGlCQUFpQixDQUFDRCxlQUFlLENBQUN5QixtQkFBbUIsQ0FBQzdCLFdBQVcrQixXQUFXLElBQUk5QixlQUFlNkIsUUFBTztJQUNqSSxJQUFJRyxjQUFjLENBQUMsQ0FBRSxFQUFDNUIsaUJBQWlCLENBQUNELGVBQWUsQ0FBQ3lCLG1CQUFtQixDQUFDN0IsV0FBVytCLFdBQVcsSUFBSTlCLGVBQWU2QixRQUFPO0lBRXhILE1BQU0sRUFBRUksYUFBYSxFQUFFQyxrQkFBa0IsRUFBRUMsZUFBZSxFQUFFQyxZQUFZLEVBQUVDLGVBQWUsRUFBRUMsYUFBYSxFQUFFLEdBQUc1SCwwREFBUUE7SUFDdkgsTUFBTSxFQUFFK0csS0FBSyxFQUFFSixNQUFNLEVBQUVrQixNQUFNLEVBQUUsR0FBRzVILGtFQUFnQkE7SUFDbEQsTUFBTSxFQUFFNkgsV0FBVyxFQUFFQyxjQUFjLEVBQUUsR0FBRzdILGtFQUFnQkE7SUFDMUQsTUFBTThILG1CQUFtQnBELFlBQVk4QztJQUNyQyxNQUFNLEVBQUVPLFVBQVUsRUFBRSxHQUFHMUcsdUVBQWNBO0lBRXJDLE1BQU0yRyxVQUFVOUYsMERBQVVBLENBQ3hCRCx5REFBU0EsQ0FBQ0YseURBQWFBLEVBQUU7UUFDdkJrRyxzQkFBc0I7WUFDcEJDLFVBQVU7UUFDWjtJQUNGLElBQ0FqRyx5REFBU0EsQ0FBQ0QsdURBQVdBLEVBQUU7UUFDckJpRyxzQkFBc0I7WUFDcEJFLE9BQU87WUFDUEMsV0FBVztRQUNiO0lBQ0Y7SUFHRjdJLGdEQUFTQSxDQUFDO1FBQ1IsTUFBTThJLGNBQWN6QyxhQUFhLFFBQVEsdUJBQXVCO1FBQ2hFLE1BQU0wQyxZQUFZM0YsU0FBUzRGLGNBQWMsQ0FBQ0Y7UUFFMUMsSUFBSUMsV0FBVztZQUNiRSxzQkFBc0I7Z0JBQ3BCRixVQUFVRyxTQUFTLEdBQUduQyxlQUFlekIsT0FBTztZQUM5QztRQUNGO0lBQ0YsR0FBRztRQUFDaUI7UUFBZUY7S0FBUztJQUU1QnJHLGdEQUFTQSxDQUFDO1FBQ1I0RyxvQkFBb0IsQ0FBQ2Q7SUFDdkIsR0FBRztRQUFDQTtLQUFTO0lBR2I5RixnREFBU0EsQ0FBQztRQUNSLDREQUE0RDtRQUM1RCxJQUFJdUksb0JBQW9CLENBQUNOLGNBQWM7WUFDckNrQixRQUFRQyxHQUFHLENBQUM7WUFDWmxCLGdCQUFnQnRDLFdBQVcyQixVQUFVO1FBQ3ZDO0lBQ0YsR0FBRztRQUFDVTtRQUFjTTtRQUFrQjNDLFdBQVcyQixVQUFVO1FBQUVXO0tBQWdCO0lBRTNFLElBQUksQ0FBQ1YsVUFBVSxxQkFBTyw4REFBQ3JILG9FQUFVQTtRQUFDa0osTUFBSzs7Ozs7O0lBRXZDLE1BQU1DLFlBQVk7WUFVZDNELDRCQUNBNkI7UUFWRixJQUFJLENBQUNBLFVBQVUsT0FBTyxFQUFFO1FBRXhCLE1BQU0sRUFBRStCLElBQUksRUFBRSxHQUFHN0ksNEZBQW9CQSxDQUNuQzhHLFVBQ0E5QixTQUNBRCxlQUNBRyxXQUFXc0IsTUFBTSxJQUFJO1lBQUVFLE9BQU9sSCxxRUFBS0EsQ0FBQ21ILEdBQUc7WUFBRUYsWUFBWSxFQUFFO1FBQUMsR0FDeERELFFBQ0FJLE1BQU1rQyxNQUFNLEdBQUdsQyxRQUFTMUIsV0FBVzBCLEtBQUssSUFBSSxFQUFFLEVBQzlDM0IsQ0FBQUEsc0JBQUFBLGlDQUFBQSw2QkFBQUEsVUFBVzhELGVBQWUsY0FBMUI5RCxpREFBQUEsMkJBQTRCK0QsTUFBTSxLQUFJLElBQ3RDbEMsQ0FBQUEscUJBQUFBLGdDQUFBQSxxQkFBQUEsU0FBVUEsUUFBUSxjQUFsQkEseUNBQUFBLG1CQUFvQm1DLEVBQUUsS0FBSTtRQUc1QixNQUFNQyxlQUFlakosNkZBQXFCQSxDQUFDeUgsVUFBVSxJQUFJbUI7UUFDekQsTUFBTU0sZUFBZTFILHFIQUFtQkEsQ0FBQ3FGLFNBQVNBLFFBQVE7UUFFMUQsT0FBT29DLGFBQWFFLEdBQUcsQ0FBQ0MsQ0FBQUE7WUFDdEIsTUFBTUMsYUFBYUQsSUFBSUUsZUFBZSxDQUFDQyxxQkFBcUIsQ0FBQ3RFLFdBQVd1RSxrQkFBa0IsQ0FBQztZQUMzRixJQUFJQztZQUVKLElBQUlKLGNBQWMsT0FBT0EsZUFBZSxVQUFVO2dCQUNoREksWUFBWSxJQUFJaEUsS0FBSzREO1lBQ3ZCLE9BQU87Z0JBQ0xJLFlBQVksSUFBSWhFO1lBQ2xCO1lBRUEsSUFBSWlFO1lBQ0osSUFBSXpFLFdBQVcwRSxnQkFBZ0IsRUFBRTtnQkFDL0IsTUFBTUMsV0FBV1IsSUFBSUUsZUFBZSxDQUFDQyxxQkFBcUIsQ0FBQ3RFLFdBQVcwRSxnQkFBZ0IsQ0FBQztnQkFDdkYsSUFBSUMsWUFBWSxPQUFPQSxhQUFhLFVBQVU7b0JBQzVDRixVQUFVLElBQUlqRSxLQUFLbUU7Z0JBQ3JCLE9BQU87b0JBQ0xGLFVBQVUsSUFBSWpFLEtBQUtnRSxVQUFVSSxPQUFPLEtBQUssQ0FBQzVFLFdBQVc2RSxlQUFlLElBQUksRUFBQyxJQUFLO2dCQUNoRjtZQUNGLE9BQU87Z0JBQ0xKLFVBQVUsSUFBSWpFLEtBQUtnRSxVQUFVSSxPQUFPLEtBQUssQ0FBQzVFLFdBQVc2RSxlQUFlLElBQUksRUFBQyxJQUFLO1lBQ2hGO1lBRUEsTUFBTUMsUUFBUXRJLGdIQUFjQSxDQUMxQjJILElBQUlZLE1BQU0sRUFDVmQsYUFBYWUsVUFBVSxFQUN2QmYsYUFBYWdCLFlBQVksRUFDekJoQixhQUFhaUIsVUFBVTtZQUd6QixPQUFPO2dCQUNMbkIsSUFBSUksSUFBSUosRUFBRTtnQkFDVmU7Z0JBQ0FLLE9BQU9YO2dCQUNQWSxLQUFLWDtnQkFDTE0sUUFBUVosSUFBSVksTUFBTTtnQkFDbEJWLGlCQUFpQkYsSUFBSUUsZUFBZTtZQUN0QztRQUNGO0lBQ0Y7SUFFQSxNQUFNZ0Isb0JBQW9CO1FBQ3hCLE1BQU1DLGFBQWE1QjtRQUVuQixJQUFJLENBQUM3QyxXQUFXMEUsSUFBSSxJQUFJO1lBQ3RCLE9BQU9EO1FBQ1Q7UUFFQSxPQUFPQSxXQUFXaEUsTUFBTSxDQUFDa0UsQ0FBQUE7WUFDdkIsTUFBTUMsY0FBYzVFLFdBQVc2RSxXQUFXO1lBQzFDLE9BQU9GLE1BQU1WLEtBQUssQ0FBQ1ksV0FBVyxHQUFHQyxRQUFRLENBQUNGO1FBQzVDO0lBQ0Y7SUFFQSxNQUFNRyxjQUFjLENBQUNKO1FBQ25CLElBQUlBLE1BQU1LLE1BQU0sQ0FBQ0MsSUFBSSxDQUFDcEcsT0FBTyxFQUFFO2dCQUVMOEYsdUNBQUFBLDRCQVFvQ0Esd0NBQ0VBO1lBVjlELE1BQU1PLGtCQUFrQjNFLG1CQUFtQjFCLE9BQU8sQ0FBQ04sQ0FBQztnQkFDNUJvRztZQUF4QixNQUFNUSxrQkFBa0JSLENBQUFBLDZDQUFBQSw2QkFBQUEsTUFBTUssTUFBTSxDQUFDSSxJQUFJLENBQUN2RyxPQUFPLGNBQXpCOEYsa0RBQUFBLHdDQUFBQSwyQkFBMkJVLFVBQVUsY0FBckNWLDREQUFBQSxzQ0FBdUMvRyxHQUFHLGNBQTFDK0csdURBQUFBLDRDQUE4QztZQUN0RSxNQUFNVyxjQUFjSixrQkFBa0JDO1lBRXRDLGdEQUFnRDtZQUNoRCxzQ0FBc0M7WUFDdEMsTUFBTSxFQUFFSSxPQUFPLEVBQUVDLElBQUksRUFBRSxHQUFHYixNQUFNSyxNQUFNLENBQUNDLElBQUksQ0FBQ3BHLE9BQU87WUFDbkQsTUFBTTRHLFVBQVVELFNBQVMsWUFBWUQsUUFBUUcsYUFBYSxDQUFDeEMsRUFBRSxHQUFHcUMsUUFBUXJDLEVBQUU7WUFDMUUsTUFBTXlDLGlCQUFpQmhKLFNBQVM0RixjQUFjLENBQUMsU0FBaUIsT0FBUmtEO1lBQ3hELE1BQU12SSxRQUFReUksaUJBQWlCQSxlQUFlQyxXQUFXLElBQUdqQix5Q0FBQUEsTUFBTUssTUFBTSxDQUFDSSxJQUFJLENBQUN2RyxPQUFPLENBQUN3RyxVQUFVLGNBQXBDViw2REFBQUEsdUNBQXNDekgsS0FBSztZQUN2RyxNQUFNYSxTQUFTNEgsaUJBQWlCQSxlQUFlRSxZQUFZLElBQUdsQix5Q0FBQUEsTUFBTUssTUFBTSxDQUFDSSxJQUFJLENBQUN2RyxPQUFPLENBQUN3RyxVQUFVLGNBQXBDViw2REFBQUEsdUNBQXNDNUcsTUFBTTtZQUUxR3NDLGtCQUFrQjtnQkFDaEIsR0FBR3NFLE1BQU1LLE1BQU0sQ0FBQ0MsSUFBSSxDQUFDcEcsT0FBTztnQkFDNUJ5RztnQkFDQXBJO2dCQUNBYTtZQUNGO1lBRUFwQixTQUFTbUosZ0JBQWdCLENBQUMsYUFBYUM7WUFDdkNwSixTQUFTbUosZ0JBQWdCLENBQUMsYUFBYUU7UUFDekM7SUFDRjtJQUVBLE1BQU1DLFlBQVk7WUFBTyxFQUFFakIsTUFBTSxFQUFFa0IsSUFBSSxFQUF5QztRQUM5RXZKLFNBQVN3SixtQkFBbUIsQ0FBQyxhQUFhSjtRQUMxQ3BKLFNBQVN3SixtQkFBbUIsQ0FBQyxhQUFhSDtRQUMxQzNGLGtCQUFrQjtRQUVsQixJQUFJLENBQUM2RixRQUFRLENBQUNsQixVQUFVLENBQUM1RCxlQUFlNEQsT0FBTzlCLEVBQUUsS0FBS2dELEtBQUtoRCxFQUFFLEVBQUU7WUFDN0Q7UUFDRjtRQUVBLE1BQU1rRCxhQUFhcEIsT0FBT0MsSUFBSSxDQUFDcEcsT0FBTztRQUN0QyxNQUFNd0gsV0FBV0gsS0FBS2pCLElBQUksQ0FBQ3BHLE9BQU87UUFFbEMsSUFBSSxDQUFDdUgsY0FBYyxDQUFDQyxVQUFVO1lBQzVCO1FBQ0Y7UUFFQSxNQUFNLEVBQUVkLE9BQU8sRUFBRUMsSUFBSSxFQUFFLEdBQUdZO1FBQzFCLE1BQU1FLGdCQUErQmQsU0FBUyxZQUFZRCxRQUFRRyxhQUFhLEdBQUdIO1FBQ2xGLE1BQU1nQixnQkFBZ0IsSUFBSTVHLEtBQUsyRyxjQUFjaEMsS0FBSztRQUNsRCxNQUFNa0MsY0FBYyxJQUFJN0csS0FBSzJHLGNBQWMvQixHQUFHO1FBQzlDLE1BQU1rQyxXQUFXcEssc0hBQXdCQSxDQUFDbUssYUFBYUQ7UUFFdkQsSUFBSUc7UUFFSixJQUFJTCxTQUFTYixJQUFJLENBQUNtQixVQUFVLENBQUMsV0FBVztZQUN0QyxNQUFNQyxnQkFBZ0J4SyxzSEFBZ0JBLENBQUNpSyxTQUFTUSxJQUFJLEVBQUU5TCw0SUFBVUEsQ0FBQ3dMO1lBQ2pFRyxXQUFXaE0sNElBQU9BLENBQUM2TCxlQUFlSztZQUNsQ0YsU0FBU0ksUUFBUSxDQUFDLEdBQUcsR0FBRyxHQUFHO1FBQzdCLE9BQU8sSUFBSVQsU0FBU2IsSUFBSSxLQUFLLG1CQUFtQjtZQUM5QyxvQ0FBb0M7WUFDcENrQixXQUFXLElBQUkvRyxLQUFLMEcsU0FBU1EsSUFBSTtZQUNqQ0gsU0FBU0ksUUFBUSxDQUFDVCxTQUFTVSxJQUFJLEVBQUVWLFNBQVNXLE1BQU0sRUFBRSxHQUFHO1FBQ3ZELE9BQU8sSUFBSVgsU0FBU2IsSUFBSSxLQUFLLFdBQVc7WUFDdEMsTUFBTW9CLGdCQUFnQnhLLHNIQUFnQkEsQ0FBQ2lLLFNBQVNRLElBQUksRUFBRTlMLDRJQUFVQSxDQUFDd0w7WUFDakVHLFdBQVdoTSw0SUFBT0EsQ0FBQzZMLGVBQWVLO1FBQ3BDLE9BQU87WUFDTDtRQUNGO1FBRUEsTUFBTUssU0FBUyxJQUFJdEgsS0FBSytHLFNBQVMzQyxPQUFPLEtBQUswQztRQUU3QyxNQUFNUyxXQUFXWixjQUFjcEMsTUFBTSxDQUFDaEIsRUFBRTtRQUN4QyxNQUFNaUUsWUFBWTtZQUNoQixDQUFDaEksV0FBV3VFLGtCQUFrQixDQUFDLEVBQUVnRCxTQUFTVSxXQUFXO1lBQ3JELEdBQUlqSSxXQUFXMEUsZ0JBQWdCLElBQUk7Z0JBQUUsQ0FBQzFFLFdBQVcwRSxnQkFBZ0IsQ0FBQyxFQUFFb0QsT0FBT0csV0FBVztZQUFHLENBQUM7UUFDNUY7UUFFQSxJQUFJO1lBQ0YsTUFBTTlGLG1CQUFtQm5DLFdBQVcyQixVQUFVLEVBQUU7Z0JBQUNvRzthQUFTLEVBQUVDO1lBQzVEbE0sMENBQUtBLENBQUNvTSxPQUFPLENBQUMsVUFBOEIsT0FBcEJmLGNBQWNyQyxLQUFLLEVBQUM7UUFDOUMsRUFBRSxPQUFPcUQsT0FBTztZQUNkck0sMENBQUtBLENBQUNxTSxLQUFLLENBQUM7WUFDWjVFLFFBQVE0RSxLQUFLLENBQUMseUJBQXlCQTtRQUN6QztJQUNGO0lBRUEsTUFBTXZCLGtCQUFrQixDQUFDcEI7UUFDdkJwRSxtQkFBbUIxQixPQUFPLEdBQUc7WUFBRVIsR0FBR3NHLE1BQU00QyxPQUFPO1lBQUVoSixHQUFHb0csTUFBTTZDLE9BQU87UUFBQztJQUNwRTtJQUNBLE1BQU14QixrQkFBa0IsQ0FBQ3JCO1FBQ3JCLE1BQU04QyxRQUFROUMsTUFBTStDLE9BQU8sQ0FBQyxFQUFFO1FBQzlCbkgsbUJBQW1CMUIsT0FBTyxHQUFHO1lBQUVSLEdBQUdvSixNQUFNRixPQUFPO1lBQUVoSixHQUFHa0osTUFBTUQsT0FBTztRQUFDO0lBQ3RFO0lBS0EsTUFBTUcsU0FBU25EO0lBRWYsTUFBTW9ELFlBQVksSUFBTWxJLGdCQUFnQixJQUFJQztJQUU1QyxNQUFNa0ksZUFBZTtRQUNuQixPQUFRakk7WUFDTixLQUFLO2dCQUNIRixnQkFBZ0JvSSxDQUFBQSxXQUFZcE4sNElBQU9BLENBQUNvTixVQUFVLENBQUM7Z0JBQy9DO1lBQ0YsS0FBSztnQkFDSHBJLGdCQUFnQm9JLENBQUFBLFdBQVloTiw0SUFBUUEsQ0FBQ2dOLFVBQVU7Z0JBQy9DO1lBQ0YsS0FBSztnQkFDSHBJLGdCQUFnQm9JLENBQUFBLFdBQVlsTiw0SUFBU0EsQ0FBQ2tOLFVBQVU7Z0JBQ2hEO1FBQ0o7SUFDRjtJQUVBLE1BQU1DLFdBQVc7UUFDZixPQUFRbkk7WUFDTixLQUFLO2dCQUNIRixnQkFBZ0JvSSxDQUFBQSxXQUFZcE4sNElBQU9BLENBQUNvTixVQUFVO2dCQUM5QztZQUNGLEtBQUs7Z0JBQ0hwSSxnQkFBZ0JvSSxDQUFBQSxXQUFZak4sNElBQVFBLENBQUNpTixVQUFVO2dCQUMvQztZQUNGLEtBQUs7Z0JBQ0hwSSxnQkFBZ0JvSSxDQUFBQSxXQUFZbk4sNElBQVNBLENBQUNtTixVQUFVO2dCQUNoRDtRQUNKO0lBQ0Y7SUFFQSxNQUFNRSwyQkFBMkIsU0FBQ25CO1lBQVlvQixpRkFBeUI7UUFDckUsSUFBSUEsZUFBZTtZQUNqQixNQUFNQyxNQUFNLElBQUl2STtZQUNoQixNQUFNd0ksVUFBVSxJQUFJeEksS0FBS2tIO1lBQ3pCc0IsUUFBUXJCLFFBQVEsQ0FBQ29CLElBQUlFLFFBQVEsSUFBSUYsSUFBSUcsVUFBVSxJQUFJSCxJQUFJSSxVQUFVLElBQUlKLElBQUlLLGVBQWU7WUFDeEZDLGtCQUFrQkw7UUFDcEIsT0FBTztZQUNMSyxrQkFBa0IzQjtRQUNwQjtJQUNGO0lBRUEsTUFBTTJCLG9CQUFvQjtZQUFPM0Isd0VBQWEsSUFBSWxIO1FBQ2hELElBQUksQ0FBQ3lCLGFBQWE7UUFFbEIsTUFBTXFILFlBQVksSUFBSTlJLEtBQUtrSDtRQUMzQixNQUFNNkIsVUFBVSxJQUFJL0ksS0FBSzhJLFVBQVUxRSxPQUFPLEtBQUssQ0FBQzVFLFdBQVc2RSxlQUFlLElBQUksRUFBQyxJQUFLO1FBQ3BGLE1BQU1aLGVBQWUxSCxxSEFBbUJBLENBQUNxRixTQUFTQSxRQUFRO1FBRTFELElBQUk7WUFDRixNQUFNNEgsZUFBb0I7Z0JBQ3hCLENBQUN4SixXQUFXdUUsa0JBQWtCLENBQUMsRUFBRStFLFVBQVVyQixXQUFXO2dCQUN0RCxHQUFJakksV0FBVzBFLGdCQUFnQixJQUFJO29CQUFFLENBQUMxRSxXQUFXMEUsZ0JBQWdCLENBQUMsRUFBRTZFLFFBQVF0QixXQUFXO2dCQUFHLENBQUM7WUFDN0Y7WUFFQSxJQUFJaEUsYUFBYWUsVUFBVSxFQUFFO2dCQUMzQndFLFlBQVksQ0FBQ3ZGLGFBQWFlLFVBQVUsQ0FBQyxHQUFHO1lBQzFDO1lBRUEsTUFBTXlFLFNBQVMsTUFBTXZILGNBQWNsQyxXQUFXMkIsVUFBVSxFQUFFO2dCQUFDNkg7YUFBYTtZQUV4RSxJQUFJQyxVQUFVQSxPQUFPQyxPQUFPLElBQUlELE9BQU9DLE9BQU8sQ0FBQzlGLE1BQU0sR0FBRyxHQUFHO2dCQUN6RCxNQUFNK0YsY0FBY0YsT0FBT0MsT0FBTyxDQUFDLEVBQUUsQ0FBQzNGLEVBQUU7Z0JBRXhDLElBQUk0RixhQUFhO29CQUNmLE1BQU1ySCxnQkFBZ0J0QyxXQUFXMkIsVUFBVTtvQkFDM0NTLGdCQUFnQnVIO29CQUNoQjdOLDBDQUFLQSxDQUFDb00sT0FBTyxDQUFDO2dCQUNoQixPQUFPO29CQUNMcE0sMENBQUtBLENBQUNxTSxLQUFLLENBQUM7Z0JBQ2Q7WUFDRixPQUFPO2dCQUNMck0sMENBQUtBLENBQUNxTSxLQUFLLENBQUM7WUFDZDtRQUNGLEVBQUUsT0FBT0EsT0FBTztZQUNkck0sMENBQUtBLENBQUNxTSxLQUFLLENBQUM7UUFDZDtJQUNGO0lBRUEsTUFBTXlCLG1CQUFtQixDQUFDcEU7UUFDeEIsSUFBSUEsU0FBU0EsTUFBTXpCLEVBQUUsRUFBRTtZQUNyQixNQUFNYixjQUFjekMsYUFBYSxRQUFRLHVCQUF1QjtZQUNoRSxNQUFNMEMsWUFBWTNGLFNBQVM0RixjQUFjLENBQUNGO1lBQzFDLElBQUlDLFdBQVc7Z0JBQ2JoQyxlQUFlekIsT0FBTyxHQUFHeUQsVUFBVUcsU0FBUztZQUM5QztZQUVBVixXQUFXNEMsTUFBTXpCLEVBQUUsRUFBRXlCLE1BQU1ULE1BQU0sQ0FBQ3BELFVBQVU7WUFDNUNmLGlCQUFpQjRFLE1BQU16QixFQUFFO1FBQzNCO0lBQ0Y7SUFFQSxNQUFNOEYsdUJBQXVCO1FBQzNCLE9BQVFwSjtZQUNOLEtBQUs7Z0JBQ0gsT0FBT25GLDRJQUFNQSxDQUFDZ0YsY0FBYztZQUM5QixLQUFLO2dCQUNILE9BQU8sR0FBdUVoRixPQUFwRUEsNElBQU1BLENBQUNDLDRJQUFPQSxDQUFDK0UsY0FBYyxDQUFDQSxhQUFhd0osTUFBTSxLQUFLLFVBQVMsT0FBMkUsT0FBdEV4Tyw0SUFBTUEsQ0FBQ0MsNElBQU9BLENBQUMrRSxjQUFjLElBQUVBLGFBQWF3SixNQUFNLEtBQUs7WUFDdkksS0FBSztnQkFDSCxPQUFPeE8sNElBQU1BLENBQUNnRixjQUFjO1lBQzlCO2dCQUNFLE9BQU9oRiw0SUFBTUEsQ0FBQ2dGLGNBQWM7UUFDaEM7SUFDRjtJQUVBLE1BQU15SixvQkFBb0IsT0FBT3ZFO1FBQy9CLElBQUksQ0FBQ3ZELGFBQWE7UUFFbEIsSUFBSTtZQUNGLE1BQU1NLGNBQWNYLFNBQVNBLFFBQVEsQ0FBQ21DLEVBQUUsRUFBRTtnQkFBQ3lCLE1BQU1ULE1BQU0sQ0FBQ2hCLEVBQUU7YUFBQztRQUM3RCxFQUFFLE9BQU9vRSxPQUFPO1lBQ2Q1RSxRQUFRNEUsS0FBSyxDQUFDLHlCQUF5QkE7UUFDekM7SUFDRjtJQUVBLE1BQU02QixrQkFBcUM7UUFDekM7WUFBRWpHLElBQUk7WUFBT3ZFLE9BQU87WUFBT3NGLE9BQU87WUFBT2dCLE1BQU07UUFBTTtRQUNyRDtZQUFFL0IsSUFBSTtZQUFRdkUsT0FBTztZQUFRc0YsT0FBTztZQUFRZ0IsTUFBTTtRQUFPO1FBQ3pEO1lBQUUvQixJQUFJO1lBQVN2RSxPQUFPO1lBQVNzRixPQUFPO1lBQVNnQixNQUFNO1FBQVE7S0FDOUQ7SUFFRCxNQUFNbUUscUJBQXFCeEosYUFBYSxRQUFRO1FBQUM7S0FBTSxHQUFHQSxhQUFhLFNBQVM7UUFBQztLQUFPLEdBQUc7UUFBQztLQUFRO0lBRXBHLHFCQUNFLDhEQUFDeUo7UUFBSUMsV0FBVTs7MEJBQ2QsOERBQUNEO2dCQUFJQyxXQUFXcE8sK0NBQUVBLENBQ2hCLHdDQUNBc0YsaUJBQWlCOztvQkFFaEJuQixXQUNDLHdCQUF3QixpQkFDeEIsOERBQUNnSzt3QkFBSUMsV0FBV3BPLCtDQUFFQSxDQUFDLE9BQU9zRixpQkFBaUI7OzBDQUN6Qyw4REFBQzZJO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0M7d0NBQUdELFdBQVU7a0RBQ1hOOzs7Ozs7a0RBRUgsOERBQUM1TywwREFBTUE7d0NBQ0xvUCxTQUFRO3dDQUNSQyxTQUFTLElBQU10SixvQkFBb0IsQ0FBQ0Q7d0NBQ3BDb0osV0FBVTtrREFDWDs7Ozs7Ozs7Ozs7OzBDQUtILDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ2xQLDBEQUFNQTtnREFDTG9QLFNBQVE7Z0RBQ1JDLFNBQVM3QjtnREFDVDBCLFdBQVdwTywrQ0FBRUEsQ0FDWCx5REFDQXNGLGdCQUFnQixRQUFROzBEQUUzQjs7Ozs7OzBEQUdELDhEQUFDNkk7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDbFAsMERBQU1BO3dEQUNMb1AsU0FBUTt3REFDUkMsU0FBUzVCO3dEQUNUeUIsV0FBV3BPLCtDQUFFQSxDQUNYLGdEQUNBc0YsZ0JBQWdCLFlBQVk7a0VBRzlCLDRFQUFDbkcsZ0ZBQWFBOzREQUFDaVAsV0FBVTs7Ozs7Ozs7Ozs7a0VBRTNCLDhEQUFDbFAsMERBQU1BO3dEQUNMb1AsU0FBUTt3REFDUkMsU0FBUzFCO3dEQUNUdUIsV0FBV3BPLCtDQUFFQSxDQUNYLGdEQUNBc0YsZ0JBQWdCLFlBQVk7a0VBRzlCLDRFQUFDbEcsaUZBQWNBOzREQUFDZ1AsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBS2hDLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBRWIsOERBQUMxTiw2RUFBWUE7Z0RBQ1g4TixTQUFTUDtnREFDVHZILGFBQWF3SDtnREFDYk8sVUFBVSxDQUFDQztvREFDVCxJQUFJQSxTQUFTN0csTUFBTSxHQUFHLEdBQUc7d0RBQ3ZCbEQsWUFBWStKLFFBQVEsQ0FBQyxFQUFFO29EQUN6QjtnREFDRjtnREFDQU4sV0FBV3BPLCtDQUFFQSxDQUNYLDBGQUNBc0YsZ0JBQWdCLFFBQVE7Z0RBRTFCcUosYUFBWTtnREFDWkMsWUFBWTs7Ozs7OzRDQUdiMUksNkJBQ0MsOERBQUNoSCwwREFBTUE7Z0RBQ0xxUCxTQUFTLElBQU16Qix5QkFBeUJ2SSxjQUFjO2dEQUN0RDZKLFdBQVdwTywrQ0FBRUEsQ0FDWCw0RkFDQXNGLGdCQUFnQixRQUFROzBEQUcxQiw0RUFBQ2pHLDJFQUFRQTtvREFBQytPLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBT2hDLDhEQUFDRDt3QkFBSUMsV0FBV3BPLCtDQUFFQSxDQUNoQiwwQ0FDQXNGLGdCQUFnQixTQUFTOzswQ0FFekIsOERBQUM2STtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNsUCwwREFBTUE7d0NBQ0xvUCxTQUFRO3dDQUNSQyxTQUFTN0I7d0NBQ1QwQixXQUFXcE8sK0NBQUVBLENBQ1gseURBQ0FzRixnQkFBZ0IsUUFBUTtrREFFM0I7Ozs7OztrREFHRCw4REFBQzZJO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ2xQLDBEQUFNQTtnREFDTG9QLFNBQVE7Z0RBQ1JDLFNBQVM1QjtnREFDVHlCLFdBQVdwTywrQ0FBRUEsQ0FDWCxnREFDQXNGLGdCQUFnQixZQUFZOzBEQUc5Qiw0RUFBQ25HLGdGQUFhQTtvREFBQ2lQLFdBQVU7Ozs7Ozs7Ozs7OzBEQUUzQiw4REFBQ2xQLDBEQUFNQTtnREFDTG9QLFNBQVE7Z0RBQ1JDLFNBQVMxQjtnREFDVHVCLFdBQVdwTywrQ0FBRUEsQ0FDWCxnREFDQXNGLGdCQUFnQixZQUFZOzBEQUc5Qiw0RUFBQ2xHLGlGQUFjQTtvREFBQ2dQLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUk5Qiw4REFBQ0M7d0NBQUdELFdBQVU7a0RBQ1hOOzs7Ozs7Ozs7Ozs7MENBSUwsOERBQUNLO2dDQUFJQyxXQUFVOztvQ0FDWixDQUFDOUksK0JBQ0EsOERBQUM2STt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUN0Tyx3REFBS0E7Z0RBQ0o2TyxhQUFZO2dEQUNabEwsT0FBT3FCO2dEQUNQMkosVUFBVSxDQUFDSSxJQUFNOUosY0FBYzhKLEVBQUVDLE1BQU0sQ0FBQ3JMLEtBQUs7Z0RBQzdDMkssV0FBVTs7Ozs7OzBEQUVaLDhEQUFDOU8sc0ZBQW1CQTtnREFBQzhPLFdBQVU7Ozs7Ozs7Ozs7OztrREFJbkMsOERBQUMxTiw2RUFBWUE7d0NBQ1g4TixTQUFTUDt3Q0FDVHZILGFBQWF3SDt3Q0FDYk8sVUFBVSxDQUFDQzs0Q0FDVCxJQUFJQSxTQUFTN0csTUFBTSxHQUFHLEdBQUc7Z0RBQ3ZCbEQsWUFBWStKLFFBQVEsQ0FBQyxFQUFFOzRDQUN6Qjt3Q0FDRjt3Q0FDQU4sV0FBV3BPLCtDQUFFQSxDQUNYLHFGQUNBc0YsZ0JBQWdCLGFBQWE7d0NBRS9CcUosYUFBWTt3Q0FDWkMsWUFBWTs7Ozs7O29DQUdiMUksNkJBQ0MsOERBQUNoSCwwREFBTUE7d0NBQ0xxUCxTQUFTLElBQU16Qix5QkFBeUJ2SSxjQUFjO3dDQUN0RDZKLFdBQVdwTywrQ0FBRUEsQ0FDWCxrR0FDQXNGLGdCQUFnQixRQUFROzswREFHMUIsOERBQUNqRywyRUFBUUE7Z0RBQUMrTyxXQUFVOzs7Ozs7NENBQ25CLENBQUM5SSwrQkFBaUIsOERBQUN5SjswREFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O29CQU9sQzVLLFlBQVksQ0FBQ21CLCtCQUNaLDhEQUFDNkk7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ3RPLHdEQUFLQTtvQ0FDSjZPLGFBQVk7b0NBQ1psTCxPQUFPcUI7b0NBQ1AySixVQUFVLENBQUNJLElBQU05SixjQUFjOEosRUFBRUMsTUFBTSxDQUFDckwsS0FBSztvQ0FDN0MySyxXQUFVOzs7Ozs7OENBRVosOERBQUM5TyxzRkFBbUJBO29DQUFDOE8sV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBT3ZDLDhEQUFDRDtnQkFBSUMsV0FBVTs7b0JBQ1pwSixrQ0FDRCw4REFBQ21KO3dCQUFJQyxXQUFXcE8sK0NBQUVBLENBQ2hCLHNCQUNBbUUsV0FBVywyREFBMkQ7OzBDQUV0RSw4REFBQ2dLO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ1k7d0NBQUdaLFdBQVU7a0RBQW1DOzs7Ozs7b0NBQ2hEakssMEJBQ0MsOERBQUNqRiwwREFBTUE7d0NBQ0xvUCxTQUFRO3dDQUNSQyxTQUFTLElBQU10SixvQkFBb0I7d0NBQ25DbUosV0FBVTs7MERBRVYsOERBQUNXO2dEQUFLWCxXQUFVOzBEQUFVOzs7Ozs7NENBQVk7Ozs7Ozs7Ozs7Ozs7MENBSzVDLDhEQUFDblAsOERBQVFBO2dDQUNQZ1EsTUFBSztnQ0FDTFAsVUFBVW5LO2dDQUNWMkssVUFBVSxDQUFDdkQ7b0NBQ1QsSUFBSUEsTUFBTTt3Q0FDUm5ILGdCQUFnQm1IO3dDQUNoQixJQUFJeEgsVUFBVTs0Q0FDWmMsb0JBQW9CO3dDQUN0QjtvQ0FDRjtnQ0FDRjtnQ0FDQW1KLFdBQVU7Ozs7Ozs7Ozs7OztrQ0FLZCw4REFBQ3pOLHNEQUFVQTt3QkFDVG1HLFNBQVNBO3dCQUNUK0MsYUFBYUE7d0JBQ2JrQixXQUFXQTt3QkFDWG9FLFdBQVc7NEJBQUMvTjt5QkFBNEI7OzBDQUUxQyw4REFBQytNO2dDQUFJQyxXQUFVO2dDQUFpQmdCLHlCQUFzQjs7b0NBQ25EMUssYUFBYSx1QkFDZiw4REFBQ3RFLHlEQUFPQTt3Q0FDTm1FLGNBQWNBO3dDQUNka0ksUUFBUUE7d0NBQ1I3SCxlQUFlQTt3Q0FDZkMsa0JBQWtCQTt3Q0FDbEJ3SyxrQkFBa0J2Qzt3Q0FDbEI1RyxhQUFhQTt3Q0FDYmQsZ0JBQWdCQTt3Q0FDaEJ5SSxrQkFBa0JBO3dDQUNsQjNJLGdCQUFnQkE7Ozs7OztvQ0FHbkJSLGFBQWEsd0JBQ1osOERBQUNyRSwyREFBUUE7d0NBQ1BrRSxjQUFjQTt3Q0FDZGtJLFFBQVFBO3dDQUNSN0gsZUFBZUE7d0NBQ2ZDLGtCQUFrQkE7d0NBQ2xCTCxpQkFBaUJBO3dDQUNqQjZLLGtCQUFrQnZDO3dDQUNsQjVHLGFBQWFBO3dDQUNiZCxnQkFBZ0JBO3dDQUNoQnlJLGtCQUFrQkE7d0NBQ2xCM0ksZ0JBQWdCQTs7Ozs7O29DQUduQlIsYUFBYSx5QkFDWiw4REFBQ3BFLDZEQUFTQTt3Q0FDUmlFLGNBQWNBO3dDQUNka0ksUUFBUUE7d0NBQ1I3SCxlQUFlQTt3Q0FDZkMsa0JBQWtCQTt3Q0FDbEJMLGlCQUFpQkE7d0NBQ2pCNkssa0JBQWtCLENBQUMxRCxPQUFTbUIseUJBQXlCbkIsTUFBTTt3Q0FDM0R6RixhQUFhQTt3Q0FDYjJILGtCQUFrQkE7d0NBQ2xCM0ksZ0JBQWdCQTs7Ozs7Ozs7Ozs7OzBDQUtuQiw4REFBQ3RFLHVEQUFXQTtnQ0FBQzBPLGVBQWU7MENBQzFCcEssa0JBQWtCQSxlQUFlb0YsSUFBSSxLQUFLLDBCQUN2Qyw4REFBQ3JKLG1GQUFvQkE7b0NBQ25Cc08sU0FBU3JLLGVBQWVtRixPQUFPO29DQUMvQm1GLE1BQU05SyxhQUFhLFFBQVEsUUFBUTtvQ0FDbkM2SixTQUFTLEtBQU87b0NBQ2hCa0IsT0FBTzt3Q0FDTHpOLE9BQU9rRCxlQUFlbEQsS0FBSzt3Q0FDM0JhLFFBQVFxQyxlQUFlckMsTUFBTTtvQ0FDL0I7Ozs7O2dEQUVBcUMsa0JBQWtCQSxlQUFlb0YsSUFBSSxLQUFLLHdCQUM1Qyw4REFBQy9KLDZFQUFpQkE7b0NBQ2hCa0osT0FBT3ZFLGVBQWVtRixPQUFPO29DQUM3Qm1GLE1BQU05SztvQ0FDTjZKLFNBQVMsS0FBTztvQ0FDaEJrQixPQUFPO3dDQUNMek4sT0FBT2tELGVBQWVsRCxLQUFLO3dDQUMzQmEsUUFBUXFDLGVBQWVyQyxNQUFNO29DQUMvQjs7Ozs7Z0RBRUE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU1aLEVBQUU7SUFocUJXZTs7UUFDbUN0Riw4REFBWUE7UUFFbENHLG9EQUFPQTtRQUNWd0IsaUVBQWFBO1FBQ2RDLDhEQUFjQTtRQUVkeEIsNkRBQWNBO1FBQ1pDLGlFQUFnQkE7UUF5QjJFQyxzREFBUUE7UUFDckZDLDhEQUFnQkE7UUFDVkMsOERBQWdCQTtRQUNqQzBFO1FBQ0ZyRCxtRUFBY0E7UUFFckJhLHNEQUFVQTs7O0tBdkNmNEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvd29ya3NwYWNlL21haW4vdmlld3MvY2FsZW5kYXIvaW5kZXgudHN4P2IxNjQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VSZWYsIHVzZUVmZmVjdCwgdXNlQ2FsbGJhY2sgfSBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IHVzZVdvcmtzcGFjZSB9IGZyb20gXCJAL3Byb3ZpZGVycy93b3Jrc3BhY2VcIjtcbmltcG9ydCB7IE1hdGNoLCBQcm9jZXNzZWREYlJlY29yZCB9IGZyb20gXCJvcGVuZGItYXBwLWRiLXV0aWxzL2xpYi90eXBpbmdzL2RiXCI7XG5pbXBvcnQgeyBQYWdlTG9hZGVyIH0gZnJvbSBcIkAvY29tcG9uZW50cy9jdXN0b20tdWkvbG9hZGVyXCI7XG5pbXBvcnQgeyB1c2VQYWdlIH0gZnJvbSBcIkAvcHJvdmlkZXJzL3BhZ2VcIjtcbmltcG9ydCB7IENhbGVuZGFyVmlld0RlZmluaXRpb24sIFZpZXdEZWZpbml0aW9uIH0gZnJvbSBcIm9wZW5kYi1hcHAtZGItdXRpbHMvbGliL3R5cGluZ3Mvdmlld1wiO1xuaW1wb3J0IHsgUmVjb3JkIH0gZnJvbSBcIkAvdHlwaW5ncy9kYXRhYmFzZVwiO1xuaW1wb3J0IHsgVmlldyB9IGZyb20gXCJAL3R5cGluZ3MvcGFnZVwiO1xuaW1wb3J0IHsgdXNlTWF5YmVTaGFyZWQgfSBmcm9tIFwiQC9wcm92aWRlcnMvc2hhcmVkXCI7XG5pbXBvcnQgeyB1c2VNYXliZVRlbXBsYXRlIH0gZnJvbSBcIkAvcHJvdmlkZXJzL3RlbXBsYXRlXCI7XG5pbXBvcnQgeyB1c2VWaWV3cywgdXNlVmlld0ZpbHRlcmluZywgdXNlVmlld1NlbGVjdGlvbiB9IGZyb20gXCJAL3Byb3ZpZGVycy92aWV3c1wiO1xuaW1wb3J0IHsgZmlsdGVyQW5kU29ydFJlY29yZHMsIHNlYXJjaEZpbHRlcmVkUmVjb3JkcyB9IGZyb20gXCJAL2NvbXBvbmVudHMvd29ya3NwYWNlL21haW4vdmlld3MvdGFibGVcIjtcbmltcG9ydCB7IENhbGVuZGFyIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9jYWxlbmRhclwiO1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9idXR0b25cIjtcbmltcG9ydCB7IERyb3Bkb3duTWVudSwgRHJvcGRvd25NZW51Q29udGVudCwgRHJvcGRvd25NZW51SXRlbSwgRHJvcGRvd25NZW51VHJpZ2dlciB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvZHJvcGRvd24tbWVudVwiO1xuaW1wb3J0IHsgQW5nbGVMZWZ0SWNvbiwgQW5nbGVSaWdodEljb24sIFBsdXNJY29uLCBNYWduaWZ5aW5nR2xhc3NJY29uIH0gZnJvbSBcIkAvY29tcG9uZW50cy9pY29ucy9Gb250QXdlc29tZVJlZ3VsYXJcIjtcbmltcG9ydCB7IGZvcm1hdCwgYWRkRGF5cywgYWRkTW9udGhzLCBzdWJNb250aHMsIGFkZFdlZWtzLCBzdWJXZWVrcywgYWRkSG91cnMsIHNldEhvdXJzLCBzZXRNaW51dGVzLCBzZXRTZWNvbmRzLCBzZXRNaWxsaXNlY29uZHMsIHN0YXJ0T2ZEYXkgfSBmcm9tIFwiZGF0ZS1mbnNcIjtcbmltcG9ydCB7IElucHV0IH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9pbnB1dFwiO1xuaW1wb3J0IHsgdG9hc3QgfSBmcm9tIFwic29ubmVyXCI7XG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiO1xuaW1wb3J0IHsgdXNlQWxlcnQgfSBmcm9tIFwiQC9wcm92aWRlcnMvYWxlcnRcIjtcbmltcG9ydCB7IHVzZVNjcmVlblNpemUgfSBmcm9tIFwiQC9wcm92aWRlcnMvc2NyZWVuU2l6ZVwiO1xuaW1wb3J0IHsgdXNlTWF5YmVSZWNvcmQgfSBmcm9tIFwiQC9wcm92aWRlcnMvcmVjb3JkXCI7XG5pbXBvcnQgeyB1c2VTdGFja2VkUGVlayB9IGZyb20gXCJAL3Byb3ZpZGVycy9zdGFja2VkUGVla1wiO1xuXG5cbmltcG9ydCB7IERheVZpZXcgfSBmcm9tIFwiLi9jb21wb25lbnRzL0RheVZpZXdcIjtcbmltcG9ydCB7IFdlZWtWaWV3IH0gZnJvbSBcIi4vY29tcG9uZW50cy9XZWVrVmlld1wiO1xuaW1wb3J0IHsgTW9udGhWaWV3IH0gZnJvbSBcIi4vY29tcG9uZW50cy9Nb250aFZpZXdcIjtcbmltcG9ydCB7IENhbGVuZGFyRXZlbnRJdGVtIH0gZnJvbSBcIi4vY29tcG9uZW50cy9DYWxlbmRhckV2ZW50SXRlbVwiO1xuaW1wb3J0IHsgZ2V0RGF0YWJhc2VUaXRsZUNvbCwgZ2V0UmVjb3JkVGl0bGUgfSBmcm9tICdAL2NvbXBvbmVudHMvd29ya3NwYWNlL21haW4vdmlld3MvZm9ybS9jb21wb25lbnRzL2VsZW1lbnQvbGlua2VkJztcbmltcG9ydCB7IENhbGVuZGFyVmlld1JlbmRlclByb3BzLCBDYWxlbmRhckV2ZW50LCBDYWxlbmRhclZpZXdUeXBlIH0gZnJvbSAnQC90eXBpbmdzL3BhZ2UnO1xuaW1wb3J0IHsgU2hlZXQsIFNoZWV0Q29udGVudCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9zaGVldCc7XG5pbXBvcnQgeyBDYWxlbmRhckljb24gfSBmcm9tICdAaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUnO1xuaW1wb3J0IHsgQ3VzdG9tU2VsZWN0IH0gZnJvbSAnQC9jb21wb25lbnRzL2N1c3RvbS11aS9jdXN0b21TZWxlY3QnO1xuaW1wb3J0IHsgVGFnSXRlbSB9IGZyb20gJ0AvY29tcG9uZW50cy93b3Jrc3BhY2UvbWFpbi92aWV3cy90YWJsZS9yZW5kZXJlci9jb21tb24vdGFnJztcbmltcG9ydCB7XG4gIERuZENvbnRleHQsXG4gIERyYWdPdmVybGF5LFxuICBQb2ludGVyU2Vuc29yLFxuICBUb3VjaFNlbnNvcixcbiAgdXNlU2Vuc29yLFxuICB1c2VTZW5zb3JzLFxuICBBY3RpdmUsXG4gIE92ZXIsXG4gIGNsb3Nlc3RDZW50ZXIsXG4gIHBvaW50ZXJXaXRoaW4sXG4gIHJlY3RJbnRlcnNlY3Rpb25cbn0gZnJvbSAnQGRuZC1raXQvY29yZSc7XG5pbXBvcnQgeyByZXN0cmljdFRvV2luZG93RWRnZXMgfSBmcm9tICdAZG5kLWtpdC9tb2RpZmllcnMnO1xuaW1wb3J0IHsgRXZlbnRTZWdtZW50IH0gZnJvbSAnQC91dGlscy9tdWx0aURheUV2ZW50VXRpbHMnO1xuaW1wb3J0IHsgQ2FsZW5kYXJFdmVudFNlZ21lbnQgfSBmcm9tICcuL2NvbXBvbmVudHMvQ2FsZW5kYXJFdmVudFNlZ21lbnQnO1xuaW1wb3J0IHsgZGlmZmVyZW5jZUluRGF5cywgZGlmZmVyZW5jZUluTWlsbGlzZWNvbmRzIH0gZnJvbSAnZGF0ZS1mbnMnO1xuXG4vLyBDdXN0b20gbW9kaWZpZXIgdG8gcmVzdHJpY3QgZHJhZ2dpbmcgdG8gdGhlIGNhbGVuZGFyIG1haW4gY29udGVudCBhcmVhIG9ubHlcbmNvbnN0IHJlc3RyaWN0VG9DYWxlbmRhckNvbnRhaW5lciA9ICh7IHRyYW5zZm9ybSwgZHJhZ2dpbmdOb2RlUmVjdCwgd2luZG93UmVjdCB9OiBhbnkpID0+IHtcbiAgaWYgKCFkcmFnZ2luZ05vZGVSZWN0IHx8ICF3aW5kb3dSZWN0KSB7XG4gICAgcmV0dXJuIHRyYW5zZm9ybTtcbiAgfVxuXG4gIC8vIEZpbmQgdGhlIGNhbGVuZGFyIG1haW4gY29udGVudCBjb250YWluZXIgKHRoZSBkaXYgdGhhdCBjb250YWlucyB0aGUgdmlld3MpXG4gIGNvbnN0IGNhbGVuZGFyQ29udGFpbmVyID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvcignW2RhdGEtY2FsZW5kYXItY29udGVudD1cInRydWVcIl0nKTtcbiAgaWYgKCFjYWxlbmRhckNvbnRhaW5lcikge1xuICAgIHJldHVybiB0cmFuc2Zvcm07XG4gIH1cblxuICBjb25zdCBjb250YWluZXJSZWN0ID0gY2FsZW5kYXJDb250YWluZXIuZ2V0Qm91bmRpbmdDbGllbnRSZWN0KCk7XG4gIFxuICAvLyBGb3IgbW9udGggdmlldywgd2UgbmVlZCB0byBhY2NvdW50IGZvciB0aGUgc2lkZSBjYXJkXG4gIC8vIFRoZSBzaWRlIGNhcmQgaXMgcG9zaXRpb25lZCBvbiB0aGUgcmlnaHQgc2lkZSBvZiB0aGUgY2FsZW5kYXJcbiAgY29uc3Qgc2lkZUNhcmQgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCdbZGF0YS1zaWRlLWNhcmQ9XCJ0cnVlXCJdJyk7XG4gIGxldCBtYXhYID0gY29udGFpbmVyUmVjdC5yaWdodCAtIGRyYWdnaW5nTm9kZVJlY3Qud2lkdGg7XG4gIFxuICBpZiAoc2lkZUNhcmQpIHtcbiAgICBjb25zdCBzaWRlQ2FyZFJlY3QgPSBzaWRlQ2FyZC5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKTtcbiAgICAvLyBSZXN0cmljdCBkcmFnZ2luZyB0byBub3QgZ28gcGFzdCB0aGUgc2lkZSBjYXJkXG4gICAgbWF4WCA9IE1hdGgubWluKG1heFgsIHNpZGVDYXJkUmVjdC5sZWZ0IC0gZHJhZ2dpbmdOb2RlUmVjdC53aWR0aCk7XG4gIH1cbiAgXG4gIC8vIEZpbmQgaGVhZGVyIGFyZWFzIHRvIHByZXZlbnQgZHJhZ2dpbmcgb3ZlciB0aGVtXG4gIGNvbnN0IHRpbWVMYWJlbHMgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCdbZGF0YS10aW1lLWxhYmVscz1cInRydWVcIl0nKTtcbiAgY29uc3QgZGF5SGVhZGVycyA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoJ1tkYXRhLWRheS1oZWFkZXJzPVwidHJ1ZVwiXScpO1xuICBjb25zdCBhbGxEYXlSb3cgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCdbZGF0YS1hbGwtZGF5LXJvdz1cInRydWVcIl0nKTtcbiAgXG4gIC8vIENhbGN1bGF0ZSB0aGUgYm91bmRhcmllcyByZWxhdGl2ZSB0byB0aGUgd2luZG93XG4gIGxldCBtaW5YID0gY29udGFpbmVyUmVjdC5sZWZ0O1xuICBsZXQgbWluWSA9IGNvbnRhaW5lclJlY3QudG9wO1xuICBjb25zdCBtYXhZID0gY29udGFpbmVyUmVjdC5ib3R0b20gLSBkcmFnZ2luZ05vZGVSZWN0LmhlaWdodDtcbiAgXG4gIC8vIFByZXZlbnQgZHJhZ2dpbmcgb3ZlciB0aW1lIGxhYmVscyAobGVmdCBzaWRlIGluIGRheS93ZWVrIHZpZXcpXG4gIGlmICh0aW1lTGFiZWxzKSB7XG4gICAgY29uc3QgdGltZUxhYmVsc1JlY3QgPSB0aW1lTGFiZWxzLmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpO1xuICAgIG1pblggPSBNYXRoLm1heChtaW5YLCB0aW1lTGFiZWxzUmVjdC5yaWdodCk7XG4gIH1cbiAgXG4gIC8vIFByZXZlbnQgZHJhZ2dpbmcgb3ZlciBkYXkgaGVhZGVycyAodG9wIG9mIGNhbGVuZGFyKVxuICBpZiAoZGF5SGVhZGVycykge1xuICAgIGNvbnN0IGRheUhlYWRlcnNSZWN0ID0gZGF5SGVhZGVycy5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKTtcbiAgICBtaW5ZID0gTWF0aC5tYXgobWluWSwgZGF5SGVhZGVyc1JlY3QuYm90dG9tKTtcbiAgfVxuICBcbiAgLy8gUHJldmVudCBkcmFnZ2luZyBvdmVyIGFsbC1kYXkgcm93IChpZiBwcmVzZW50KVxuICBpZiAoYWxsRGF5Um93KSB7XG4gICAgY29uc3QgYWxsRGF5Um93UmVjdCA9IGFsbERheVJvdy5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKTtcbiAgICBtaW5ZID0gTWF0aC5tYXgobWluWSwgYWxsRGF5Um93UmVjdC5ib3R0b20pO1xuICB9XG5cbiAgLy8gR2V0IGN1cnJlbnQgcG9pbnRlciBwb3NpdGlvblxuICBjb25zdCBjdXJyZW50WCA9IHRyYW5zZm9ybS54ICsgZHJhZ2dpbmdOb2RlUmVjdC5sZWZ0O1xuICBjb25zdCBjdXJyZW50WSA9IHRyYW5zZm9ybS55ICsgZHJhZ2dpbmdOb2RlUmVjdC50b3A7XG5cbiAgLy8gQ29uc3RyYWluIHRoZSBwb3NpdGlvblxuICBjb25zdCBjb25zdHJhaW5lZFggPSBNYXRoLm1pbihNYXRoLm1heChjdXJyZW50WCwgbWluWCksIG1heFgpO1xuICBjb25zdCBjb25zdHJhaW5lZFkgPSBNYXRoLm1pbihNYXRoLm1heChjdXJyZW50WSwgbWluWSksIG1heFkpO1xuXG4gIHJldHVybiB7XG4gICAgLi4udHJhbnNmb3JtLFxuICAgIHg6IGNvbnN0cmFpbmVkWCAtIGRyYWdnaW5nTm9kZVJlY3QubGVmdCxcbiAgICB5OiBjb25zdHJhaW5lZFkgLSBkcmFnZ2luZ05vZGVSZWN0LnRvcCxcbiAgfTtcbn07XG5cblxuXG4vLyBDdXN0b20gaG9vayB0byB0cmFjayBwcmV2aW91cyB2YWx1ZVxuY29uc3QgdXNlUHJldmlvdXMgPSA8VCw+KHZhbHVlOiBUKSA9PiB7XG4gIGNvbnN0IHJlZiA9IHVzZVJlZjxUPigpO1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIHJlZi5jdXJyZW50ID0gdmFsdWU7XG4gIH0pO1xuICByZXR1cm4gcmVmLmN1cnJlbnQ7XG59O1xuXG5leHBvcnQgY29uc3QgQ2FsZW5kYXJWaWV3ID0gKHByb3BzOiBDYWxlbmRhclZpZXdSZW5kZXJQcm9wcykgPT4ge1xuICBjb25zdCB7IGRhdGFiYXNlU3RvcmUsIG1lbWJlcnMsIHdvcmtzcGFjZSB9ID0gdXNlV29ya3NwYWNlKCk7XG4gIGNvbnN0IHsgZGVmaW5pdGlvbiB9ID0gcHJvcHM7XG4gIGNvbnN0IHsgYWNjZXNzTGV2ZWwgfSA9IHVzZVBhZ2UoKTtcbiAgY29uc3QgeyBpc01vYmlsZSB9ID0gdXNlU2NyZWVuU2l6ZSgpO1xuICBjb25zdCBtYXliZVJlY29yZCA9IHVzZU1heWJlUmVjb3JkKCk7IFxuXG4gIGNvbnN0IG1heWJlU2hhcmVkID0gdXNlTWF5YmVTaGFyZWQoKTtcbiAgY29uc3QgbWF5YmVUZW1wbGF0ZSA9IHVzZU1heWJlVGVtcGxhdGUoKTtcblxuICBjb25zdCBbc2VsZWN0ZWREYXRlLCBzZXRTZWxlY3RlZERhdGVdID0gdXNlU3RhdGU8RGF0ZT4obmV3IERhdGUoKSk7XG4gIGNvbnN0IFt2aWV3VHlwZSwgc2V0Vmlld1R5cGVdID0gdXNlU3RhdGU8Q2FsZW5kYXJWaWV3VHlwZT4oXCJ3ZWVrXCIpO1xuICBjb25zdCBbc2VsZWN0ZWRFdmVudCwgc2V0U2VsZWN0ZWRFdmVudF0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW3NlYXJjaFRlcm0sIHNldFNlYXJjaFRlcm1dID0gdXNlU3RhdGUoXCJcIik7XG4gIGNvbnN0IFtzaG93U2lkZUNhbGVuZGFyLCBzZXRTaG93U2lkZUNhbGVuZGFyXSA9IHVzZVN0YXRlKCFpc01vYmlsZSk7XG4gIGNvbnN0IFthY3RpdmVEcmFnRGF0YSwgc2V0QWN0aXZlRHJhZ0RhdGFdID0gdXNlU3RhdGU8YW55IHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IHNhdmVkU2Nyb2xsVG9wID0gdXNlUmVmKDApO1xuICBjb25zdCBwb2ludGVyQ29vcmRpbmF0ZXMgPSB1c2VSZWYoeyB4OiAwLCB5OiAwIH0pO1xuXG4gIGNvbnN0IGlzSW5SZWNvcmRUYWIgPSAhIW1heWJlUmVjb3JkO1xuXG4gIGRlZmluaXRpb24uZmlsdGVyID0gZGVmaW5pdGlvbi5maWx0ZXIgfHwgeyBjb25kaXRpb25zOiBbXSwgbWF0Y2g6IE1hdGNoLkFsbCB9O1xuICBkZWZpbml0aW9uLnNvcnRzID0gZGVmaW5pdGlvbi5zb3J0cyB8fCBbXTtcblxuICBjb25zdCBkYXRhYmFzZUlkID0gZGVmaW5pdGlvbi5kYXRhYmFzZUlkO1xuICBjb25zdCBkYXRhYmFzZSA9IGRhdGFiYXNlU3RvcmVbZGVmaW5pdGlvbi5kYXRhYmFzZUlkXTtcblxuICBjb25zdCBpc1B1Ymxpc2hlZFZpZXcgPSAhIW1heWJlU2hhcmVkO1xuICBjb25zdCBlZGl0YWJsZSA9ICFkZWZpbml0aW9uLmxvY2tDb250ZW50ICYmICFpc1B1Ymxpc2hlZFZpZXcgJiYgISFhY2Nlc3NMZXZlbDtcblxuICBsZXQgY2FuRWRpdFN0cnVjdHVyZSA9ICEhKCFtYXliZVRlbXBsYXRlICYmICFtYXliZVNoYXJlZCAmJiAhaXNQdWJsaXNoZWRWaWV3ICYmICFkZWZpbml0aW9uLmxvY2tDb250ZW50ICYmIGFjY2Vzc0xldmVsICYmIGVkaXRhYmxlKTtcbiAgbGV0IGNhbkVkaXREYXRhID0gISEoIW1heWJlVGVtcGxhdGUgJiYgIW1heWJlU2hhcmVkICYmICFpc1B1Ymxpc2hlZFZpZXcgJiYgIWRlZmluaXRpb24ubG9ja0NvbnRlbnQgJiYgYWNjZXNzTGV2ZWwgJiYgZWRpdGFibGUpO1xuXG4gICAgICBjb25zdCB7IGNyZWF0ZVJlY29yZHMsIHVwZGF0ZVJlY29yZFZhbHVlcywgc2V0UGVla1JlY29yZElkLCBwZWVrUmVjb3JkSWQsIHJlZnJlc2hEYXRhYmFzZSwgZGVsZXRlUmVjb3JkcyB9ID0gdXNlVmlld3MoKTtcbiAgICBjb25zdCB7IHNvcnRzLCBmaWx0ZXIsIHNlYXJjaCB9ID0gdXNlVmlld0ZpbHRlcmluZygpO1xuICAgIGNvbnN0IHsgc2VsZWN0ZWRJZHMsIHNldFNlbGVjdGVkSWRzIH0gPSB1c2VWaWV3U2VsZWN0aW9uKCk7XG4gIGNvbnN0IHByZXZQZWVrUmVjb3JkSWQgPSB1c2VQcmV2aW91cyhwZWVrUmVjb3JkSWQpO1xuICBjb25zdCB7IG9wZW5SZWNvcmQgfSA9IHVzZVN0YWNrZWRQZWVrKCk7XG5cbiAgY29uc3Qgc2Vuc29ycyA9IHVzZVNlbnNvcnMoXG4gICAgdXNlU2Vuc29yKFBvaW50ZXJTZW5zb3IsIHtcbiAgICAgIGFjdGl2YXRpb25Db25zdHJhaW50OiB7XG4gICAgICAgIGRpc3RhbmNlOiA4LFxuICAgICAgfSxcbiAgICB9KSxcbiAgICB1c2VTZW5zb3IoVG91Y2hTZW5zb3IsIHtcbiAgICAgIGFjdGl2YXRpb25Db25zdHJhaW50OiB7XG4gICAgICAgIGRlbGF5OiAxNTAsXG4gICAgICAgIHRvbGVyYW5jZTogNSxcbiAgICAgIH0sXG4gICAgfSlcbiAgKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGNvbnRhaW5lcklkID0gdmlld1R5cGUgPT09ICdkYXknID8gJ2RheS12aWV3LWNvbnRhaW5lcicgOiAnd2Vlay12aWV3LWNvbnRhaW5lcic7XG4gICAgY29uc3QgY29udGFpbmVyID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoY29udGFpbmVySWQpO1xuXG4gICAgaWYgKGNvbnRhaW5lcikge1xuICAgICAgcmVxdWVzdEFuaW1hdGlvbkZyYW1lKCgpID0+IHtcbiAgICAgICAgY29udGFpbmVyLnNjcm9sbFRvcCA9IHNhdmVkU2Nyb2xsVG9wLmN1cnJlbnQ7XG4gICAgICB9KTtcbiAgICB9XG4gIH0sIFtzZWxlY3RlZEV2ZW50LCB2aWV3VHlwZV0pO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgc2V0U2hvd1NpZGVDYWxlbmRhcighaXNNb2JpbGUpO1xuICB9LCBbaXNNb2JpbGVdKTtcblxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8gT25seSByZWZyZXNoIGlmIHRoZSBwZWVrIHZpZXcgd2FzIG9wZW4gYW5kIGlzIG5vdyBjbG9zZWQuXG4gICAgaWYgKHByZXZQZWVrUmVjb3JkSWQgJiYgIXBlZWtSZWNvcmRJZCkge1xuICAgICAgY29uc29sZS5sb2coXCJQZWVrIHZpZXcgd2FzIGNsb3NlZCwgcmVmcmVzaGluZyBjYWxlbmRhciBkYXRhXCIpO1xuICAgICAgcmVmcmVzaERhdGFiYXNlKGRlZmluaXRpb24uZGF0YWJhc2VJZCk7XG4gICAgfVxuICB9LCBbcGVla1JlY29yZElkLCBwcmV2UGVla1JlY29yZElkLCBkZWZpbml0aW9uLmRhdGFiYXNlSWQsIHJlZnJlc2hEYXRhYmFzZV0pO1xuXG4gIGlmICghZGF0YWJhc2UpIHJldHVybiA8UGFnZUxvYWRlciBzaXplPVwiZnVsbFwiIC8+O1xuXG4gIGNvbnN0IGdldEV2ZW50cyA9ICgpOiBDYWxlbmRhckV2ZW50W10gPT4ge1xuICAgIGlmICghZGF0YWJhc2UpIHJldHVybiBbXTtcblxuICAgIGNvbnN0IHsgcm93cyB9ID0gZmlsdGVyQW5kU29ydFJlY29yZHMoXG4gICAgICBkYXRhYmFzZSxcbiAgICAgIG1lbWJlcnMsXG4gICAgICBkYXRhYmFzZVN0b3JlLFxuICAgICAgZGVmaW5pdGlvbi5maWx0ZXIgfHwgeyBtYXRjaDogTWF0Y2guQWxsLCBjb25kaXRpb25zOiBbXSB9LFxuICAgICAgZmlsdGVyLFxuICAgICAgc29ydHMubGVuZ3RoID8gc29ydHMgOiAoZGVmaW5pdGlvbi5zb3J0cyB8fCBbXSksXG4gICAgICB3b3Jrc3BhY2U/LndvcmtzcGFjZU1lbWJlcj8udXNlcklkIHx8ICcnLFxuICAgICAgZGF0YWJhc2U/LmRhdGFiYXNlPy5pZCB8fCAnJ1xuICAgICk7XG5cbiAgICBjb25zdCBmaWx0ZXJlZFJvd3MgPSBzZWFyY2hGaWx0ZXJlZFJlY29yZHMoc2VhcmNoIHx8IFwiXCIsIHJvd3MpO1xuICAgIGNvbnN0IHRpdGxlQ29sT3B0cyA9IGdldERhdGFiYXNlVGl0bGVDb2woZGF0YWJhc2UuZGF0YWJhc2UpO1xuXG4gICAgcmV0dXJuIGZpbHRlcmVkUm93cy5tYXAocm93ID0+IHtcbiAgICAgIGNvbnN0IHN0YXJ0VmFsdWUgPSByb3cucHJvY2Vzc2VkUmVjb3JkLnByb2Nlc3NlZFJlY29yZFZhbHVlc1tkZWZpbml0aW9uLmV2ZW50U3RhcnRDb2x1bW5JZF07XG4gICAgICBsZXQgc3RhcnREYXRlOiBEYXRlO1xuXG4gICAgICBpZiAoc3RhcnRWYWx1ZSAmJiB0eXBlb2Ygc3RhcnRWYWx1ZSA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgc3RhcnREYXRlID0gbmV3IERhdGUoc3RhcnRWYWx1ZSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBzdGFydERhdGUgPSBuZXcgRGF0ZSgpO1xuICAgICAgfVxuXG4gICAgICBsZXQgZW5kRGF0ZTogRGF0ZTtcbiAgICAgIGlmIChkZWZpbml0aW9uLmV2ZW50RW5kQ29sdW1uSWQpIHtcbiAgICAgICAgY29uc3QgZW5kVmFsdWUgPSByb3cucHJvY2Vzc2VkUmVjb3JkLnByb2Nlc3NlZFJlY29yZFZhbHVlc1tkZWZpbml0aW9uLmV2ZW50RW5kQ29sdW1uSWRdO1xuICAgICAgICBpZiAoZW5kVmFsdWUgJiYgdHlwZW9mIGVuZFZhbHVlID09PSAnc3RyaW5nJykge1xuICAgICAgICAgIGVuZERhdGUgPSBuZXcgRGF0ZShlbmRWYWx1ZSk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgZW5kRGF0ZSA9IG5ldyBEYXRlKHN0YXJ0RGF0ZS5nZXRUaW1lKCkgKyAoZGVmaW5pdGlvbi5kZWZhdWx0RHVyYXRpb24gfHwgMzApICogNjAwMDApO1xuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBlbmREYXRlID0gbmV3IERhdGUoc3RhcnREYXRlLmdldFRpbWUoKSArIChkZWZpbml0aW9uLmRlZmF1bHREdXJhdGlvbiB8fCAzMCkgKiA2MDAwMCk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHRpdGxlID0gZ2V0UmVjb3JkVGl0bGUoXG4gICAgICAgIHJvdy5yZWNvcmQsXG4gICAgICAgIHRpdGxlQ29sT3B0cy50aXRsZUNvbElkLFxuICAgICAgICB0aXRsZUNvbE9wdHMuZGVmYXVsdFRpdGxlLFxuICAgICAgICB0aXRsZUNvbE9wdHMuaXNDb250YWN0c1xuICAgICAgKTtcblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgaWQ6IHJvdy5pZCxcbiAgICAgICAgdGl0bGUsXG4gICAgICAgIHN0YXJ0OiBzdGFydERhdGUsXG4gICAgICAgIGVuZDogZW5kRGF0ZSxcbiAgICAgICAgcmVjb3JkOiByb3cucmVjb3JkLFxuICAgICAgICBwcm9jZXNzZWRSZWNvcmQ6IHJvdy5wcm9jZXNzZWRSZWNvcmRcbiAgICAgIH07XG4gICAgfSk7XG4gIH07XG5cbiAgY29uc3QgZ2V0RmlsdGVyZWRFdmVudHMgPSAoKSA9PiB7XG4gICAgY29uc3QgYmFzZUV2ZW50cyA9IGdldEV2ZW50cygpO1xuXG4gICAgaWYgKCFzZWFyY2hUZXJtLnRyaW0oKSkge1xuICAgICAgcmV0dXJuIGJhc2VFdmVudHM7XG4gICAgfVxuXG4gICAgcmV0dXJuIGJhc2VFdmVudHMuZmlsdGVyKGV2ZW50ID0+IHtcbiAgICAgIGNvbnN0IHNlYXJjaExvd2VyID0gc2VhcmNoVGVybS50b0xvd2VyQ2FzZSgpO1xuICAgICAgcmV0dXJuIGV2ZW50LnRpdGxlLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoTG93ZXIpO1xuICAgIH0pO1xuICB9O1xuXG4gIGNvbnN0IG9uRHJhZ1N0YXJ0ID0gKGV2ZW50OiB7IGFjdGl2ZTogQWN0aXZlIH0pID0+IHtcbiAgICBpZiAoZXZlbnQuYWN0aXZlLmRhdGEuY3VycmVudCkge1xuICAgICAgY29uc3QgaW5pdGlhbFBvaW50ZXJZID0gcG9pbnRlckNvb3JkaW5hdGVzLmN1cnJlbnQueTtcbiAgICAgIGNvbnN0IGluaXRpYWxFdmVudFRvcCA9IGV2ZW50LmFjdGl2ZS5yZWN0LmN1cnJlbnQ/LnRyYW5zbGF0ZWQ/LnRvcCA/PyAwO1xuICAgICAgY29uc3QgZ3JhYk9mZnNldFkgPSBpbml0aWFsUG9pbnRlclkgLSBpbml0aWFsRXZlbnRUb3A7XG5cbiAgICAgIC8vIEdldCB0aGUgZXhhY3QgZGltZW5zaW9ucyBmcm9tIHRoZSBET00gZWxlbWVudFxuICAgICAgLy8gSGFuZGxlIGJvdGggZXZlbnQgYW5kIHNlZ21lbnQgdHlwZXNcbiAgICAgIGNvbnN0IHsgcGF5bG9hZCwgdHlwZSB9ID0gZXZlbnQuYWN0aXZlLmRhdGEuY3VycmVudDtcbiAgICAgIGNvbnN0IGV2ZW50SWQgPSB0eXBlID09PSAnc2VnbWVudCcgPyBwYXlsb2FkLm9yaWdpbmFsRXZlbnQuaWQgOiBwYXlsb2FkLmlkO1xuICAgICAgY29uc3QgZHJhZ2dlZEVsZW1lbnQgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZChgZXZlbnQtJHtldmVudElkfWApO1xuICAgICAgY29uc3Qgd2lkdGggPSBkcmFnZ2VkRWxlbWVudCA/IGRyYWdnZWRFbGVtZW50Lm9mZnNldFdpZHRoIDogZXZlbnQuYWN0aXZlLnJlY3QuY3VycmVudC50cmFuc2xhdGVkPy53aWR0aDtcbiAgICAgIGNvbnN0IGhlaWdodCA9IGRyYWdnZWRFbGVtZW50ID8gZHJhZ2dlZEVsZW1lbnQub2Zmc2V0SGVpZ2h0IDogZXZlbnQuYWN0aXZlLnJlY3QuY3VycmVudC50cmFuc2xhdGVkPy5oZWlnaHQ7XG5cbiAgICAgIHNldEFjdGl2ZURyYWdEYXRhKHtcbiAgICAgICAgLi4uZXZlbnQuYWN0aXZlLmRhdGEuY3VycmVudCxcbiAgICAgICAgZ3JhYk9mZnNldFksXG4gICAgICAgIHdpZHRoLFxuICAgICAgICBoZWlnaHQsXG4gICAgICB9KTtcblxuICAgICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcignbW91c2Vtb3ZlJywgaGFuZGxlTW91c2VNb3ZlKTtcbiAgICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ3RvdWNobW92ZScsIGhhbmRsZVRvdWNoTW92ZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IG9uRHJhZ0VuZCA9IGFzeW5jICh7IGFjdGl2ZSwgb3ZlciB9OiB7IGFjdGl2ZTogQWN0aXZlLCBvdmVyOiBPdmVyIHwgbnVsbCB9KSA9PiB7XG4gICAgZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcignbW91c2Vtb3ZlJywgaGFuZGxlTW91c2VNb3ZlKTtcbiAgICBkb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKCd0b3VjaG1vdmUnLCBoYW5kbGVUb3VjaE1vdmUpO1xuICAgIHNldEFjdGl2ZURyYWdEYXRhKG51bGwpO1xuXG4gICAgaWYgKCFvdmVyIHx8ICFhY3RpdmUgfHwgIWNhbkVkaXREYXRhIHx8IGFjdGl2ZS5pZCA9PT0gb3Zlci5pZCkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIGNvbnN0IGFjdGl2ZURhdGEgPSBhY3RpdmUuZGF0YS5jdXJyZW50O1xuICAgIGNvbnN0IG92ZXJEYXRhID0gb3Zlci5kYXRhLmN1cnJlbnQ7XG5cbiAgICBpZiAoIWFjdGl2ZURhdGEgfHwgIW92ZXJEYXRhKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgY29uc3QgeyBwYXlsb2FkLCB0eXBlIH0gPSBhY3RpdmVEYXRhO1xuICAgIGNvbnN0IGV2ZW50VG9VcGRhdGU6IENhbGVuZGFyRXZlbnQgPSB0eXBlID09PSAnc2VnbWVudCcgPyBwYXlsb2FkLm9yaWdpbmFsRXZlbnQgOiBwYXlsb2FkO1xuICAgIGNvbnN0IG9yaWdpbmFsU3RhcnQgPSBuZXcgRGF0ZShldmVudFRvVXBkYXRlLnN0YXJ0KTtcbiAgICBjb25zdCBvcmlnaW5hbEVuZCA9IG5ldyBEYXRlKGV2ZW50VG9VcGRhdGUuZW5kKTtcbiAgICBjb25zdCBkdXJhdGlvbiA9IGRpZmZlcmVuY2VJbk1pbGxpc2Vjb25kcyhvcmlnaW5hbEVuZCwgb3JpZ2luYWxTdGFydCk7XG5cbiAgICBsZXQgbmV3U3RhcnQ6IERhdGU7XG5cbiAgICBpZiAob3ZlckRhdGEudHlwZS5zdGFydHNXaXRoKCdhbGxkYXknKSkge1xuICAgICAgY29uc3QgZGF5RGlmZmVyZW5jZSA9IGRpZmZlcmVuY2VJbkRheXMob3ZlckRhdGEuZGF0ZSwgc3RhcnRPZkRheShvcmlnaW5hbFN0YXJ0KSk7XG4gICAgICBuZXdTdGFydCA9IGFkZERheXMob3JpZ2luYWxTdGFydCwgZGF5RGlmZmVyZW5jZSk7XG4gICAgICBuZXdTdGFydC5zZXRIb3VycygwLCAwLCAwLCAwKTtcbiAgICB9IGVsc2UgaWYgKG92ZXJEYXRhLnR5cGUgPT09ICd0aW1lc2xvdC1taW51dGUnKSB7XG4gICAgICAvLyBIYW5kbGUgcHJlY2lzZSBtaW51dGUtYmFzZWQgZHJvcHNcbiAgICAgIG5ld1N0YXJ0ID0gbmV3IERhdGUob3ZlckRhdGEuZGF0ZSk7XG4gICAgICBuZXdTdGFydC5zZXRIb3VycyhvdmVyRGF0YS5ob3VyLCBvdmVyRGF0YS5taW51dGUsIDAsIDApO1xuICAgIH0gZWxzZSBpZiAob3ZlckRhdGEudHlwZSA9PT0gJ2RheWNlbGwnKSB7XG4gICAgICBjb25zdCBkYXlEaWZmZXJlbmNlID0gZGlmZmVyZW5jZUluRGF5cyhvdmVyRGF0YS5kYXRlLCBzdGFydE9mRGF5KG9yaWdpbmFsU3RhcnQpKTtcbiAgICAgIG5ld1N0YXJ0ID0gYWRkRGF5cyhvcmlnaW5hbFN0YXJ0LCBkYXlEaWZmZXJlbmNlKTtcbiAgICB9IGVsc2Uge1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIGNvbnN0IG5ld0VuZCA9IG5ldyBEYXRlKG5ld1N0YXJ0LmdldFRpbWUoKSArIGR1cmF0aW9uKTtcblxuICAgIGNvbnN0IHJlY29yZElkID0gZXZlbnRUb1VwZGF0ZS5yZWNvcmQuaWQ7XG4gICAgY29uc3QgbmV3VmFsdWVzID0ge1xuICAgICAgW2RlZmluaXRpb24uZXZlbnRTdGFydENvbHVtbklkXTogbmV3U3RhcnQudG9JU09TdHJpbmcoKSxcbiAgICAgIC4uLihkZWZpbml0aW9uLmV2ZW50RW5kQ29sdW1uSWQgJiYgeyBbZGVmaW5pdGlvbi5ldmVudEVuZENvbHVtbklkXTogbmV3RW5kLnRvSVNPU3RyaW5nKCkgfSksXG4gICAgfTtcblxuICAgIHRyeSB7XG4gICAgICBhd2FpdCB1cGRhdGVSZWNvcmRWYWx1ZXMoZGVmaW5pdGlvbi5kYXRhYmFzZUlkLCBbcmVjb3JkSWRdLCBuZXdWYWx1ZXMpO1xuICAgICAgdG9hc3Quc3VjY2VzcyhgRXZlbnQgXCIke2V2ZW50VG9VcGRhdGUudGl0bGV9XCIgdXBkYXRlZC5gKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgdG9hc3QuZXJyb3IoXCJGYWlsZWQgdG8gdXBkYXRlIGV2ZW50LlwiKTtcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciB1cGRhdGluZyBldmVudDpcIiwgZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVNb3VzZU1vdmUgPSAoZXZlbnQ6IE1vdXNlRXZlbnQpID0+IHtcbiAgICBwb2ludGVyQ29vcmRpbmF0ZXMuY3VycmVudCA9IHsgeDogZXZlbnQuY2xpZW50WCwgeTogZXZlbnQuY2xpZW50WSB9O1xuICB9O1xuICBjb25zdCBoYW5kbGVUb3VjaE1vdmUgPSAoZXZlbnQ6IFRvdWNoRXZlbnQpID0+IHtcbiAgICAgIGNvbnN0IHRvdWNoID0gZXZlbnQudG91Y2hlc1swXTtcbiAgICAgIHBvaW50ZXJDb29yZGluYXRlcy5jdXJyZW50ID0geyB4OiB0b3VjaC5jbGllbnRYLCB5OiB0b3VjaC5jbGllbnRZIH07XG4gIH07XG5cblxuXG5cbiAgY29uc3QgZXZlbnRzID0gZ2V0RmlsdGVyZWRFdmVudHMoKTtcblxuICBjb25zdCBnb1RvVG9kYXkgPSAoKSA9PiBzZXRTZWxlY3RlZERhdGUobmV3IERhdGUoKSk7XG5cbiAgY29uc3QgZ29Ub1ByZXZpb3VzID0gKCkgPT4ge1xuICAgIHN3aXRjaCAodmlld1R5cGUpIHtcbiAgICAgIGNhc2UgXCJkYXlcIjpcbiAgICAgICAgc2V0U2VsZWN0ZWREYXRlKHByZXZEYXRlID0+IGFkZERheXMocHJldkRhdGUsIC0xKSk7XG4gICAgICAgIGJyZWFrO1xuICAgICAgY2FzZSBcIndlZWtcIjpcbiAgICAgICAgc2V0U2VsZWN0ZWREYXRlKHByZXZEYXRlID0+IHN1YldlZWtzKHByZXZEYXRlLCAxKSk7XG4gICAgICAgIGJyZWFrO1xuICAgICAgY2FzZSBcIm1vbnRoXCI6XG4gICAgICAgIHNldFNlbGVjdGVkRGF0ZShwcmV2RGF0ZSA9PiBzdWJNb250aHMocHJldkRhdGUsIDEpKTtcbiAgICAgICAgYnJlYWs7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGdvVG9OZXh0ID0gKCkgPT4ge1xuICAgIHN3aXRjaCAodmlld1R5cGUpIHtcbiAgICAgIGNhc2UgXCJkYXlcIjpcbiAgICAgICAgc2V0U2VsZWN0ZWREYXRlKHByZXZEYXRlID0+IGFkZERheXMocHJldkRhdGUsIDEpKTtcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlIFwid2Vla1wiOlxuICAgICAgICBzZXRTZWxlY3RlZERhdGUocHJldkRhdGUgPT4gYWRkV2Vla3MocHJldkRhdGUsIDEpKTtcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlIFwibW9udGhcIjpcbiAgICAgICAgc2V0U2VsZWN0ZWREYXRlKHByZXZEYXRlID0+IGFkZE1vbnRocyhwcmV2RGF0ZSwgMSkpO1xuICAgICAgICBicmVhaztcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlUmVxdWVzdENyZWF0ZUV2ZW50ID0gKGRhdGU6IERhdGUsIHVzZVN5c3RlbVRpbWU6IGJvb2xlYW4gPSBmYWxzZSkgPT4ge1xuICAgIGlmICh1c2VTeXN0ZW1UaW1lKSB7XG4gICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpO1xuICAgICAgY29uc3QgbmV3RGF0ZSA9IG5ldyBEYXRlKGRhdGUpO1xuICAgICAgbmV3RGF0ZS5zZXRIb3Vycyhub3cuZ2V0SG91cnMoKSwgbm93LmdldE1pbnV0ZXMoKSwgbm93LmdldFNlY29uZHMoKSwgbm93LmdldE1pbGxpc2Vjb25kcygpKTtcbiAgICAgIGhhbmRsZUNyZWF0ZUV2ZW50KG5ld0RhdGUpO1xuICAgIH0gZWxzZSB7XG4gICAgICBoYW5kbGVDcmVhdGVFdmVudChkYXRlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlQ3JlYXRlRXZlbnQgPSBhc3luYyAoZGF0ZTogRGF0ZSA9IG5ldyBEYXRlKCkpID0+IHtcbiAgICBpZiAoIWNhbkVkaXREYXRhKSByZXR1cm47XG5cbiAgICBjb25zdCBzdGFydFRpbWUgPSBuZXcgRGF0ZShkYXRlKTtcbiAgICBjb25zdCBlbmRUaW1lID0gbmV3IERhdGUoc3RhcnRUaW1lLmdldFRpbWUoKSArIChkZWZpbml0aW9uLmRlZmF1bHREdXJhdGlvbiB8fCAzMCkgKiA2MDAwMCk7XG4gICAgY29uc3QgdGl0bGVDb2xPcHRzID0gZ2V0RGF0YWJhc2VUaXRsZUNvbChkYXRhYmFzZS5kYXRhYmFzZSk7XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVjb3JkVmFsdWVzOiBhbnkgPSB7XG4gICAgICAgIFtkZWZpbml0aW9uLmV2ZW50U3RhcnRDb2x1bW5JZF06IHN0YXJ0VGltZS50b0lTT1N0cmluZygpLFxuICAgICAgICAuLi4oZGVmaW5pdGlvbi5ldmVudEVuZENvbHVtbklkICYmIHsgW2RlZmluaXRpb24uZXZlbnRFbmRDb2x1bW5JZF06IGVuZFRpbWUudG9JU09TdHJpbmcoKSB9KVxuICAgICAgfTtcblxuICAgICAgaWYgKHRpdGxlQ29sT3B0cy50aXRsZUNvbElkKSB7XG4gICAgICAgIHJlY29yZFZhbHVlc1t0aXRsZUNvbE9wdHMudGl0bGVDb2xJZF0gPSBcIk5ldyBFdmVudFwiO1xuICAgICAgfVxuXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBjcmVhdGVSZWNvcmRzKGRlZmluaXRpb24uZGF0YWJhc2VJZCwgW3JlY29yZFZhbHVlc10pO1xuXG4gICAgICBpZiAocmVzdWx0ICYmIHJlc3VsdC5yZWNvcmRzICYmIHJlc3VsdC5yZWNvcmRzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgY29uc3QgbmV3UmVjb3JkSWQgPSByZXN1bHQucmVjb3Jkc1swXS5pZDtcblxuICAgICAgICBpZiAobmV3UmVjb3JkSWQpIHtcbiAgICAgICAgICBhd2FpdCByZWZyZXNoRGF0YWJhc2UoZGVmaW5pdGlvbi5kYXRhYmFzZUlkKTtcbiAgICAgICAgICBzZXRQZWVrUmVjb3JkSWQobmV3UmVjb3JkSWQpO1xuICAgICAgICAgIHRvYXN0LnN1Y2Nlc3MoXCJOZXcgZXZlbnQgY3JlYXRlZFwiKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICB0b2FzdC5lcnJvcihcIkVycm9yIGFjY2Vzc2luZyB0aGUgbmV3IGV2ZW50XCIpO1xuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0b2FzdC5lcnJvcihcIkZhaWxlZCB0byBjcmVhdGUgZXZlbnQgcHJvcGVybHlcIik7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHRvYXN0LmVycm9yKFwiRmFpbGVkIHRvIGNyZWF0ZSBldmVudFwiKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlRXZlbnRDbGljayA9IChldmVudDogQ2FsZW5kYXJFdmVudCkgPT4ge1xuICAgIGlmIChldmVudCAmJiBldmVudC5pZCkge1xuICAgICAgY29uc3QgY29udGFpbmVySWQgPSB2aWV3VHlwZSA9PT0gJ2RheScgPyAnZGF5LXZpZXctY29udGFpbmVyJyA6ICd3ZWVrLXZpZXctY29udGFpbmVyJztcbiAgICAgIGNvbnN0IGNvbnRhaW5lciA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKGNvbnRhaW5lcklkKTtcbiAgICAgIGlmIChjb250YWluZXIpIHtcbiAgICAgICAgc2F2ZWRTY3JvbGxUb3AuY3VycmVudCA9IGNvbnRhaW5lci5zY3JvbGxUb3A7XG4gICAgICB9XG5cbiAgICAgIG9wZW5SZWNvcmQoZXZlbnQuaWQsIGV2ZW50LnJlY29yZC5kYXRhYmFzZUlkKTtcbiAgICAgIHNldFNlbGVjdGVkRXZlbnQoZXZlbnQuaWQpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBnZXRIZWFkZXJEYXRlRGlzcGxheSA9ICgpID0+IHtcbiAgICBzd2l0Y2ggKHZpZXdUeXBlKSB7XG4gICAgICBjYXNlIFwiZGF5XCI6XG4gICAgICAgIHJldHVybiBmb3JtYXQoc2VsZWN0ZWREYXRlLCAnTU1NTSBkLCB5eXl5Jyk7XG4gICAgICBjYXNlIFwid2Vla1wiOlxuICAgICAgICByZXR1cm4gYCR7Zm9ybWF0KGFkZERheXMoc2VsZWN0ZWREYXRlLCAtc2VsZWN0ZWREYXRlLmdldERheSgpKSwgJ01NTSBkJyl9IC0gJHtmb3JtYXQoYWRkRGF5cyhzZWxlY3RlZERhdGUsIDYtc2VsZWN0ZWREYXRlLmdldERheSgpKSwgJ01NTSBkLCB5eXl5Jyl9YDtcbiAgICAgIGNhc2UgXCJtb250aFwiOlxuICAgICAgICByZXR1cm4gZm9ybWF0KHNlbGVjdGVkRGF0ZSwgJ01NTU0geXl5eScpO1xuICAgICAgZGVmYXVsdDpcbiAgICAgICAgcmV0dXJuIGZvcm1hdChzZWxlY3RlZERhdGUsICdNTU1NIGQsIHl5eXknKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlRXZlbnREZWxldGUgPSBhc3luYyAoZXZlbnQ6IENhbGVuZGFyRXZlbnQpID0+IHtcbiAgICBpZiAoIWNhbkVkaXREYXRhKSByZXR1cm47XG4gICAgXG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IGRlbGV0ZVJlY29yZHMoZGF0YWJhc2UuZGF0YWJhc2UuaWQsIFtldmVudC5yZWNvcmQuaWRdKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZGVsZXRpbmcgZXZlbnQ6JywgZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCB2aWV3VHlwZU9wdGlvbnM6IFRhZ0l0ZW08c3RyaW5nPltdID0gW1xuICAgIHsgaWQ6ICdkYXknLCB2YWx1ZTogJ2RheScsIHRpdGxlOiAnRGF5JywgZGF0YTogJ2RheScgfSxcbiAgICB7IGlkOiAnd2VlaycsIHZhbHVlOiAnd2VlaycsIHRpdGxlOiAnV2VlaycsIGRhdGE6ICd3ZWVrJyB9LFxuICAgIHsgaWQ6ICdtb250aCcsIHZhbHVlOiAnbW9udGgnLCB0aXRsZTogJ01vbnRoJywgZGF0YTogJ21vbnRoJyB9XG4gIF07XG5cbiAgY29uc3Qgc2VsZWN0ZWRWaWV3T3B0aW9uID0gdmlld1R5cGUgPT09ICdkYXknID8gWydkYXknXSA6IHZpZXdUeXBlID09PSAnd2VlaycgPyBbJ3dlZWsnXSA6IFsnbW9udGgnXTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiaC1mdWxsIGZsZXggZmxleC1jb2wgYmctd2hpdGVcIj5cbiAgICAgPGRpdiBjbGFzc05hbWU9e2NuKFxuICAgICAgIFwiYm9yZGVyLWIgYm9yZGVyLW5ldXRyYWwtMzAwIGJnLXdoaXRlXCIsXG4gICAgICAgaXNJblJlY29yZFRhYiAmJiBcInB5LTFcIiBcbiAgICAgKX0+XG4gICAgICAge2lzTW9iaWxlID8gKFxuICAgICAgICAgLyogTW9iaWxlIEhlYWRlciBMYXlvdXQgKi9cbiAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtjbihcInAtMlwiLCBpc0luUmVjb3JkVGFiICYmIFwicHktMVwiKX0+XG4gICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTJcIj5cbiAgICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC14cyBmb250LXNlbWlib2xkIHRleHQtYmxhY2sgdHJ1bmNhdGUgZmxleC0xIG1yLTJcIj5cbiAgICAgICAgICAgICAgIHtnZXRIZWFkZXJEYXRlRGlzcGxheSgpfVxuICAgICAgICAgICAgIDwvaDE+XG4gICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dTaWRlQ2FsZW5kYXIoIXNob3dTaWRlQ2FsZW5kYXIpfVxuICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicm91bmRlZC1mdWxsIGgtOCBweC0zIHRleHQteHMgdGV4dC1ibGFjayBob3ZlcjpiZy1ncmF5LTUwXCJcbiAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICBDYWxlbmRhclxuICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0xXCI+XG4gICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2dvVG9Ub2RheX1cbiAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgICAgICBcInJvdW5kZWQtZnVsbCBweC0zIHRleHQteHMgdGV4dC1ibGFjayBob3ZlcjpiZy1ncmF5LTUwXCIsXG4gICAgICAgICAgICAgICAgICAgaXNJblJlY29yZFRhYiA/IFwiaC02XCIgOiBcImgtOFwiXG4gICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgIFRvZGF5XG4gICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgICAgb25DbGljaz17Z29Ub1ByZXZpb3VzfVxuICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICAgICAgICAgICBcInJvdW5kZWQtZnVsbCBwLTEgdGV4dC1ibGFjayBob3ZlcjpiZy1ncmF5LTUwXCIsXG4gICAgICAgICAgICAgICAgICAgICBpc0luUmVjb3JkVGFiID8gXCJoLTYgdy02XCIgOiBcImgtOCB3LThcIlxuICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICA8QW5nbGVMZWZ0SWNvbiBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2dvVG9OZXh0fVxuICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICAgICAgICAgICBcInJvdW5kZWQtZnVsbCBwLTEgdGV4dC1ibGFjayBob3ZlcjpiZy1ncmF5LTUwXCIsXG4gICAgICAgICAgICAgICAgICAgICBpc0luUmVjb3JkVGFiID8gXCJoLTYgdy02XCIgOiBcImgtOCB3LThcIlxuICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICA8QW5nbGVSaWdodEljb24gY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICAgey8qIFZpZXcgVHlwZSBTZWxlY3RvciAqL31cbiAgICAgICAgICAgICAgIDxDdXN0b21TZWxlY3RcbiAgICAgICAgICAgICAgICAgb3B0aW9ucz17dmlld1R5cGVPcHRpb25zfVxuICAgICAgICAgICAgICAgICBzZWxlY3RlZElkcz17c2VsZWN0ZWRWaWV3T3B0aW9ufVxuICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KHNlbGVjdGVkKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgaWYgKHNlbGVjdGVkLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICAgICAgICAgICAgIHNldFZpZXdUeXBlKHNlbGVjdGVkWzBdIGFzIENhbGVuZGFyVmlld1R5cGUpO1xuICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgICAgICBcInB4LTMgdGV4dC14cyBib3JkZXItbmV1dHJhbC0zMDAgcm91bmRlZC1mdWxsIGJnLXdoaXRlIGhvdmVyOmJnLWdyYXktNTAgdGV4dC1ibGFjayB3LTIwXCIsXG4gICAgICAgICAgICAgICAgICAgaXNJblJlY29yZFRhYiA/IFwiaC02XCIgOiBcImgtOFwiXG4gICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiVmlld1wiXG4gICAgICAgICAgICAgICAgIGhpZGVTZWFyY2g9e3RydWV9XG4gICAgICAgICAgICAgICAvPlxuXG4gICAgICAgICAgICAgICB7Y2FuRWRpdERhdGEgJiYgKFxuICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlUmVxdWVzdENyZWF0ZUV2ZW50KHNlbGVjdGVkRGF0ZSwgdHJ1ZSl9XG4gICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgICAgICAgIFwicm91bmRlZC1mdWxsIHB4LTMgdGV4dC14cyBib3JkZXIgYm9yZGVyLW5ldXRyYWwtMzAwIGJnLXdoaXRlIGhvdmVyOmJnLWdyYXktNTAgdGV4dC1ibGFja1wiLFxuICAgICAgICAgICAgICAgICAgICAgaXNJblJlY29yZFRhYiA/IFwiaC02XCIgOiBcImgtOFwiXG4gICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgIDxQbHVzSWNvbiBjbGFzc05hbWU9XCJoLTMgdy0zXCIgLz5cbiAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICkgOiAoXG4gICAgICAgPGRpdiBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgXCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gcHgtNFwiLFxuICAgICAgICAgaXNJblJlY29yZFRhYiA/IFwicHktMVwiIDogXCJweS0yXCJcbiAgICAgICApfT5cbiAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgIG9uQ2xpY2s9e2dvVG9Ub2RheX1cbiAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgICAgXCJyb3VuZGVkLWZ1bGwgcHgtMyB0ZXh0LXhzIHRleHQtYmxhY2sgaG92ZXI6YmctZ3JheS01MFwiLFxuICAgICAgICAgICAgICAgaXNJblJlY29yZFRhYiA/IFwiaC02XCIgOiBcImgtOFwiXG4gICAgICAgICAgICAgKX1cbiAgICAgICAgICAgPlxuICAgICAgICAgICAgIFRvZGF5XG4gICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgICAgIG9uQ2xpY2s9e2dvVG9QcmV2aW91c31cbiAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICAgICAgIFwicm91bmRlZC1mdWxsIHAtMSB0ZXh0LWJsYWNrIGhvdmVyOmJnLWdyYXktNTBcIixcbiAgICAgICAgICAgICAgICAgaXNJblJlY29yZFRhYiA/IFwiaC02IHctNlwiIDogXCJoLTggdy04XCJcbiAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgPEFuZ2xlTGVmdEljb24gY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgICAgIG9uQ2xpY2s9e2dvVG9OZXh0fVxuICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgICAgXCJyb3VuZGVkLWZ1bGwgcC0xIHRleHQtYmxhY2sgaG92ZXI6YmctZ3JheS01MFwiLFxuICAgICAgICAgICAgICAgICBpc0luUmVjb3JkVGFiID8gXCJoLTYgdy02XCIgOiBcImgtOCB3LThcIlxuICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICA8QW5nbGVSaWdodEljb24gY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZvbnQtc2VtaWJvbGQgdGV4dC1ibGFja1wiPlxuICAgICAgICAgICAgIHtnZXRIZWFkZXJEYXRlRGlzcGxheSgpfVxuICAgICAgICAgICA8L2gxPlxuICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgIHshaXNJblJlY29yZFRhYiAmJiAoXG4gICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU2VhcmNoIGV2ZW50cy4uLlwiXG4gICAgICAgICAgICAgICAgIHZhbHVlPXtzZWFyY2hUZXJtfVxuICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNlYXJjaFRlcm0oZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTQ4IHByLTggaC04IHRleHQteHMgYm9yZGVyLW5ldXRyYWwtMzAwIGJnLXRyYW5zcGFyZW50IHNoYWRvdy1ub25lXCJcbiAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICA8TWFnbmlmeWluZ0dsYXNzSWNvbiBjbGFzc05hbWU9XCJoLTMgdy0zIGFic29sdXRlIHJpZ2h0LTIgdG9wLTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS15LTEvMiB0ZXh0LW5ldXRyYWwtNDAwXCIgLz5cbiAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgKX1cblxuICAgICAgICAgICA8Q3VzdG9tU2VsZWN0XG4gICAgICAgICAgICAgb3B0aW9ucz17dmlld1R5cGVPcHRpb25zfVxuICAgICAgICAgICAgIHNlbGVjdGVkSWRzPXtzZWxlY3RlZFZpZXdPcHRpb259XG4gICAgICAgICAgICAgb25DaGFuZ2U9eyhzZWxlY3RlZCkgPT4ge1xuICAgICAgICAgICAgICAgaWYgKHNlbGVjdGVkLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICAgICAgICAgc2V0Vmlld1R5cGUoc2VsZWN0ZWRbMF0gYXMgQ2FsZW5kYXJWaWV3VHlwZSk7XG4gICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgICAgXCJweC0zIHRleHQteHMgYm9yZGVyLW5ldXRyYWwtMzAwIHJvdW5kZWQtZnVsbCBiZy13aGl0ZSBob3ZlcjpiZy1ncmF5LTUwIHRleHQtYmxhY2tcIixcbiAgICAgICAgICAgICAgIGlzSW5SZWNvcmRUYWIgPyBcImgtNiB3LTIwXCIgOiBcImgtOCB3LTI4XCJcbiAgICAgICAgICAgICApfVxuICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiVmlld1wiXG4gICAgICAgICAgICAgaGlkZVNlYXJjaD17dHJ1ZX1cbiAgICAgICAgICAgLz5cblxuICAgICAgICAgICB7Y2FuRWRpdERhdGEgJiYgKFxuICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVJlcXVlc3RDcmVhdGVFdmVudChzZWxlY3RlZERhdGUsIHRydWUpfVxuICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgICAgXCJyb3VuZGVkLWZ1bGwgcHgtMyB0ZXh0LXhzIGJvcmRlciBib3JkZXItbmV1dHJhbC0zMDAgYmctd2hpdGUgaG92ZXI6YmctZ3JheS01MCB0ZXh0LWJsYWNrIGdhcC0xXCIsXG4gICAgICAgICAgICAgICAgIGlzSW5SZWNvcmRUYWIgPyBcImgtNlwiIDogXCJoLThcIlxuICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICA8UGx1c0ljb24gY2xhc3NOYW1lPVwiaC0zIHctM1wiIC8+XG4gICAgICAgICAgICAgICB7IWlzSW5SZWNvcmRUYWIgJiYgPHNwYW4+QWRkIEV2ZW50PC9zcGFuPn1cbiAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgKX1cbiAgICAgICAgIDwvZGl2PlxuICAgICAgIDwvZGl2PlxuICAgICApfVxuXG4gICAgIHtpc01vYmlsZSAmJiAhaXNJblJlY29yZFRhYiAmJiAoXG4gICAgICAgPGRpdiBjbGFzc05hbWU9XCJweC0yIHBiLTJcIj5cbiAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTZWFyY2ggZXZlbnRzLi4uXCJcbiAgICAgICAgICAgICB2YWx1ZT17c2VhcmNoVGVybX1cbiAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNlYXJjaFRlcm0oZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBwci04IGgtOCB0ZXh0LXhzIGJvcmRlci1uZXV0cmFsLTMwMCBiZy10cmFuc3BhcmVudCBzaGFkb3ctbm9uZVwiXG4gICAgICAgICAgIC8+XG4gICAgICAgICAgIDxNYWduaWZ5aW5nR2xhc3NJY29uIGNsYXNzTmFtZT1cImgtMyB3LTMgYWJzb2x1dGUgcmlnaHQtMiB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIHRleHQtbmV1dHJhbC00MDBcIiAvPlxuICAgICAgICAgPC9kaXY+XG4gICAgICAgPC9kaXY+XG4gICAgICl9XG4gICA8L2Rpdj5cblxuICAgey8qIE1haW4gY29udGVudCAqL31cbiAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIGZsZXggbWluLWgtMFwiPlxuICAgICB7c2hvd1NpZGVDYWxlbmRhciAmJiAoXG4gICAgIDxkaXYgY2xhc3NOYW1lPXtjbihcbiAgICAgICBcImZsZXgtbm9uZSBiZy13aGl0ZVwiLFxuICAgICAgIGlzTW9iaWxlID8gXCJ3LWZ1bGwgYWJzb2x1dGUgei01MCBiYWNrZHJvcC1ibHVyLXNtIGgtZnVsbCBzaGFkb3ctbGdcIiA6IFwidy1maXQgYm9yZGVyLXIgYm9yZGVyLW5ldXRyYWwtMzAwXCJcbiAgICAgKX0+XG4gICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgcC0yIGJvcmRlci1iIGJvcmRlci1uZXV0cmFsLTMwMFwiPlxuICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteHMgZm9udC1zZW1pYm9sZCB0ZXh0LWJsYWNrXCI+Q2FsZW5kYXI8L2gzPlxuICAgICAgICAge2lzTW9iaWxlICYmIChcbiAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd1NpZGVDYWxlbmRhcihmYWxzZSl9XG4gICAgICAgICAgICAgY2xhc3NOYW1lPVwicm91bmRlZC1mdWxsIGgtOCB3LTggcC0xIHRleHQtYmxhY2sgaG92ZXI6YmctbmV1dHJhbC0xMDBcIlxuICAgICAgICAgICA+XG4gICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwic3Itb25seVwiPkNsb3NlPC9zcGFuPlxuICAgICAgICAgICAgIMOXXG4gICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgKX1cbiAgICAgICA8L2Rpdj5cbiAgICAgICA8Q2FsZW5kYXJcbiAgICAgICAgIG1vZGU9XCJzaW5nbGVcIlxuICAgICAgICAgc2VsZWN0ZWQ9e3NlbGVjdGVkRGF0ZX1cbiAgICAgICAgIG9uU2VsZWN0PXsoZGF0ZSkgPT4ge1xuICAgICAgICAgICBpZiAoZGF0ZSkge1xuICAgICAgICAgICAgIHNldFNlbGVjdGVkRGF0ZShkYXRlKTtcbiAgICAgICAgICAgICBpZiAoaXNNb2JpbGUpIHtcbiAgICAgICAgICAgICAgIHNldFNob3dTaWRlQ2FsZW5kYXIoZmFsc2UpO1xuICAgICAgICAgICAgIH1cbiAgICAgICAgICAgfVxuICAgICAgICAgfX1cbiAgICAgICAgIGNsYXNzTmFtZT1cInJvdW5kZWQtbWQgYm9yZGVyLTBcIlxuICAgICAgIC8+XG4gICAgIDwvZGl2PlxuICAgICApfVxuICAgICBcbiAgICAgPERuZENvbnRleHRcbiAgICAgICBzZW5zb3JzPXtzZW5zb3JzfVxuICAgICAgIG9uRHJhZ1N0YXJ0PXtvbkRyYWdTdGFydH1cbiAgICAgICBvbkRyYWdFbmQ9e29uRHJhZ0VuZH1cbiAgICAgICBtb2RpZmllcnM9e1tyZXN0cmljdFRvQ2FsZW5kYXJDb250YWluZXJdfVxuICAgICA+XG4gICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG1pbi13LTBcIiBkYXRhLWNhbGVuZGFyLWNvbnRlbnQ9XCJ0cnVlXCI+XG4gICAgICAge3ZpZXdUeXBlID09PSAnZGF5JyAmJiAoXG4gICAgICA8RGF5Vmlld1xuICAgICAgICBzZWxlY3RlZERhdGU9e3NlbGVjdGVkRGF0ZX1cbiAgICAgICAgZXZlbnRzPXtldmVudHN9XG4gICAgICAgIHNlbGVjdGVkRXZlbnQ9e3NlbGVjdGVkRXZlbnR9XG4gICAgICAgIHNldFNlbGVjdGVkRXZlbnQ9e3NldFNlbGVjdGVkRXZlbnR9XG4gICAgICAgIG9wZW5BZGRFdmVudEZvcm09e2hhbmRsZVJlcXVlc3RDcmVhdGVFdmVudH1cbiAgICAgICAgY2FuRWRpdERhdGE9e2NhbkVkaXREYXRhfVxuICAgICAgICBzYXZlZFNjcm9sbFRvcD17c2F2ZWRTY3JvbGxUb3B9XG4gICAgICAgIGhhbmRsZUV2ZW50Q2xpY2s9e2hhbmRsZUV2ZW50Q2xpY2t9XG4gICAgICAgIGFjdGl2ZURyYWdEYXRhPXthY3RpdmVEcmFnRGF0YX1cbiAgICAgIC8+XG4gICAgKX1cbiAgICB7dmlld1R5cGUgPT09ICd3ZWVrJyAmJiAoXG4gICAgICA8V2Vla1ZpZXdcbiAgICAgICAgc2VsZWN0ZWREYXRlPXtzZWxlY3RlZERhdGV9XG4gICAgICAgIGV2ZW50cz17ZXZlbnRzfVxuICAgICAgICBzZWxlY3RlZEV2ZW50PXtzZWxlY3RlZEV2ZW50fVxuICAgICAgICBzZXRTZWxlY3RlZEV2ZW50PXtzZXRTZWxlY3RlZEV2ZW50fVxuICAgICAgICBzZXRTZWxlY3RlZERhdGU9e3NldFNlbGVjdGVkRGF0ZX1cbiAgICAgICAgb3BlbkFkZEV2ZW50Rm9ybT17aGFuZGxlUmVxdWVzdENyZWF0ZUV2ZW50fVxuICAgICAgICBjYW5FZGl0RGF0YT17Y2FuRWRpdERhdGF9XG4gICAgICAgIHNhdmVkU2Nyb2xsVG9wPXtzYXZlZFNjcm9sbFRvcH1cbiAgICAgICAgaGFuZGxlRXZlbnRDbGljaz17aGFuZGxlRXZlbnRDbGlja31cbiAgICAgICAgYWN0aXZlRHJhZ0RhdGE9e2FjdGl2ZURyYWdEYXRhfVxuICAgICAgLz5cbiAgICApfVxuICAgIHt2aWV3VHlwZSA9PT0gJ21vbnRoJyAmJiAoXG4gICAgICA8TW9udGhWaWV3XG4gICAgICAgIHNlbGVjdGVkRGF0ZT17c2VsZWN0ZWREYXRlfVxuICAgICAgICBldmVudHM9e2V2ZW50c31cbiAgICAgICAgc2VsZWN0ZWRFdmVudD17c2VsZWN0ZWRFdmVudH1cbiAgICAgICAgc2V0U2VsZWN0ZWRFdmVudD17c2V0U2VsZWN0ZWRFdmVudH1cbiAgICAgICAgc2V0U2VsZWN0ZWREYXRlPXtzZXRTZWxlY3RlZERhdGV9XG4gICAgICAgIG9wZW5BZGRFdmVudEZvcm09eyhkYXRlKSA9PiBoYW5kbGVSZXF1ZXN0Q3JlYXRlRXZlbnQoZGF0ZSwgdHJ1ZSl9XG4gICAgICAgIGNhbkVkaXREYXRhPXtjYW5FZGl0RGF0YX1cbiAgICAgICAgaGFuZGxlRXZlbnRDbGljaz17aGFuZGxlRXZlbnRDbGlja31cbiAgICAgICAgYWN0aXZlRHJhZ0RhdGE9e2FjdGl2ZURyYWdEYXRhfVxuICAgICAgLz5cbiAgICApfVxuICAgICA8L2Rpdj5cblxuICAgICA8RHJhZ092ZXJsYXkgZHJvcEFuaW1hdGlvbj17bnVsbH0+XG4gICAgICB7YWN0aXZlRHJhZ0RhdGEgJiYgYWN0aXZlRHJhZ0RhdGEudHlwZSA9PT0gJ3NlZ21lbnQnID8gKFxuICAgICAgICAgIDxDYWxlbmRhckV2ZW50U2VnbWVudFxuICAgICAgICAgICAgc2VnbWVudD17YWN0aXZlRHJhZ0RhdGEucGF5bG9hZCBhcyBFdmVudFNlZ21lbnR9XG4gICAgICAgICAgICB2aWV3PXt2aWV3VHlwZSA9PT0gJ2RheScgPyAnZGF5JyA6ICd3ZWVrJ31cbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHt9fVxuICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgd2lkdGg6IGFjdGl2ZURyYWdEYXRhLndpZHRoLFxuICAgICAgICAgICAgICBoZWlnaHQ6IGFjdGl2ZURyYWdEYXRhLmhlaWdodCxcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgLz5cbiAgICAgICAgKSA6IGFjdGl2ZURyYWdEYXRhICYmIGFjdGl2ZURyYWdEYXRhLnR5cGUgPT09ICdldmVudCcgPyAoXG4gICAgICAgICAgPENhbGVuZGFyRXZlbnRJdGVtXG4gICAgICAgICAgICBldmVudD17YWN0aXZlRHJhZ0RhdGEucGF5bG9hZCBhcyBDYWxlbmRhckV2ZW50fVxuICAgICAgICAgICAgdmlldz17dmlld1R5cGV9XG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7fX1cbiAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgIHdpZHRoOiBhY3RpdmVEcmFnRGF0YS53aWR0aCxcbiAgICAgICAgICAgICAgaGVpZ2h0OiBhY3RpdmVEcmFnRGF0YS5oZWlnaHQsXG4gICAgICAgICAgICB9fVxuICAgICAgICAgIC8+XG4gICAgICAgICkgOiBudWxsfVxuICAgICAgPC9EcmFnT3ZlcmxheT5cbiAgIDwvRG5kQ29udGV4dD5cbiAgIDwvZGl2PlxuICA8L2Rpdj5cbiAgKTtcbn07Il0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VSZWYiLCJ1c2VFZmZlY3QiLCJ1c2VXb3Jrc3BhY2UiLCJNYXRjaCIsIlBhZ2VMb2FkZXIiLCJ1c2VQYWdlIiwidXNlTWF5YmVTaGFyZWQiLCJ1c2VNYXliZVRlbXBsYXRlIiwidXNlVmlld3MiLCJ1c2VWaWV3RmlsdGVyaW5nIiwidXNlVmlld1NlbGVjdGlvbiIsImZpbHRlckFuZFNvcnRSZWNvcmRzIiwic2VhcmNoRmlsdGVyZWRSZWNvcmRzIiwiQ2FsZW5kYXIiLCJCdXR0b24iLCJBbmdsZUxlZnRJY29uIiwiQW5nbGVSaWdodEljb24iLCJQbHVzSWNvbiIsIk1hZ25pZnlpbmdHbGFzc0ljb24iLCJmb3JtYXQiLCJhZGREYXlzIiwiYWRkTW9udGhzIiwic3ViTW9udGhzIiwiYWRkV2Vla3MiLCJzdWJXZWVrcyIsInN0YXJ0T2ZEYXkiLCJJbnB1dCIsInRvYXN0IiwiY24iLCJ1c2VTY3JlZW5TaXplIiwidXNlTWF5YmVSZWNvcmQiLCJ1c2VTdGFja2VkUGVlayIsIkRheVZpZXciLCJXZWVrVmlldyIsIk1vbnRoVmlldyIsIkNhbGVuZGFyRXZlbnRJdGVtIiwiZ2V0RGF0YWJhc2VUaXRsZUNvbCIsImdldFJlY29yZFRpdGxlIiwiQ3VzdG9tU2VsZWN0IiwiRG5kQ29udGV4dCIsIkRyYWdPdmVybGF5IiwiUG9pbnRlclNlbnNvciIsIlRvdWNoU2Vuc29yIiwidXNlU2Vuc29yIiwidXNlU2Vuc29ycyIsIkNhbGVuZGFyRXZlbnRTZWdtZW50IiwiZGlmZmVyZW5jZUluRGF5cyIsImRpZmZlcmVuY2VJbk1pbGxpc2Vjb25kcyIsInJlc3RyaWN0VG9DYWxlbmRhckNvbnRhaW5lciIsInRyYW5zZm9ybSIsImRyYWdnaW5nTm9kZVJlY3QiLCJ3aW5kb3dSZWN0IiwiY2FsZW5kYXJDb250YWluZXIiLCJkb2N1bWVudCIsInF1ZXJ5U2VsZWN0b3IiLCJjb250YWluZXJSZWN0IiwiZ2V0Qm91bmRpbmdDbGllbnRSZWN0Iiwic2lkZUNhcmQiLCJtYXhYIiwicmlnaHQiLCJ3aWR0aCIsInNpZGVDYXJkUmVjdCIsIk1hdGgiLCJtaW4iLCJsZWZ0IiwidGltZUxhYmVscyIsImRheUhlYWRlcnMiLCJhbGxEYXlSb3ciLCJtaW5YIiwibWluWSIsInRvcCIsIm1heFkiLCJib3R0b20iLCJoZWlnaHQiLCJ0aW1lTGFiZWxzUmVjdCIsIm1heCIsImRheUhlYWRlcnNSZWN0IiwiYWxsRGF5Um93UmVjdCIsImN1cnJlbnRYIiwieCIsImN1cnJlbnRZIiwieSIsImNvbnN0cmFpbmVkWCIsImNvbnN0cmFpbmVkWSIsInVzZVByZXZpb3VzIiwidmFsdWUiLCJyZWYiLCJjdXJyZW50IiwiQ2FsZW5kYXJWaWV3IiwicHJvcHMiLCJkYXRhYmFzZVN0b3JlIiwibWVtYmVycyIsIndvcmtzcGFjZSIsImRlZmluaXRpb24iLCJhY2Nlc3NMZXZlbCIsImlzTW9iaWxlIiwibWF5YmVSZWNvcmQiLCJtYXliZVNoYXJlZCIsIm1heWJlVGVtcGxhdGUiLCJzZWxlY3RlZERhdGUiLCJzZXRTZWxlY3RlZERhdGUiLCJEYXRlIiwidmlld1R5cGUiLCJzZXRWaWV3VHlwZSIsInNlbGVjdGVkRXZlbnQiLCJzZXRTZWxlY3RlZEV2ZW50Iiwic2VhcmNoVGVybSIsInNldFNlYXJjaFRlcm0iLCJzaG93U2lkZUNhbGVuZGFyIiwic2V0U2hvd1NpZGVDYWxlbmRhciIsImFjdGl2ZURyYWdEYXRhIiwic2V0QWN0aXZlRHJhZ0RhdGEiLCJzYXZlZFNjcm9sbFRvcCIsInBvaW50ZXJDb29yZGluYXRlcyIsImlzSW5SZWNvcmRUYWIiLCJmaWx0ZXIiLCJjb25kaXRpb25zIiwibWF0Y2giLCJBbGwiLCJzb3J0cyIsImRhdGFiYXNlSWQiLCJkYXRhYmFzZSIsImlzUHVibGlzaGVkVmlldyIsImVkaXRhYmxlIiwibG9ja0NvbnRlbnQiLCJjYW5FZGl0U3RydWN0dXJlIiwiY2FuRWRpdERhdGEiLCJjcmVhdGVSZWNvcmRzIiwidXBkYXRlUmVjb3JkVmFsdWVzIiwic2V0UGVla1JlY29yZElkIiwicGVla1JlY29yZElkIiwicmVmcmVzaERhdGFiYXNlIiwiZGVsZXRlUmVjb3JkcyIsInNlYXJjaCIsInNlbGVjdGVkSWRzIiwic2V0U2VsZWN0ZWRJZHMiLCJwcmV2UGVla1JlY29yZElkIiwib3BlblJlY29yZCIsInNlbnNvcnMiLCJhY3RpdmF0aW9uQ29uc3RyYWludCIsImRpc3RhbmNlIiwiZGVsYXkiLCJ0b2xlcmFuY2UiLCJjb250YWluZXJJZCIsImNvbnRhaW5lciIsImdldEVsZW1lbnRCeUlkIiwicmVxdWVzdEFuaW1hdGlvbkZyYW1lIiwic2Nyb2xsVG9wIiwiY29uc29sZSIsImxvZyIsInNpemUiLCJnZXRFdmVudHMiLCJyb3dzIiwibGVuZ3RoIiwid29ya3NwYWNlTWVtYmVyIiwidXNlcklkIiwiaWQiLCJmaWx0ZXJlZFJvd3MiLCJ0aXRsZUNvbE9wdHMiLCJtYXAiLCJyb3ciLCJzdGFydFZhbHVlIiwicHJvY2Vzc2VkUmVjb3JkIiwicHJvY2Vzc2VkUmVjb3JkVmFsdWVzIiwiZXZlbnRTdGFydENvbHVtbklkIiwic3RhcnREYXRlIiwiZW5kRGF0ZSIsImV2ZW50RW5kQ29sdW1uSWQiLCJlbmRWYWx1ZSIsImdldFRpbWUiLCJkZWZhdWx0RHVyYXRpb24iLCJ0aXRsZSIsInJlY29yZCIsInRpdGxlQ29sSWQiLCJkZWZhdWx0VGl0bGUiLCJpc0NvbnRhY3RzIiwic3RhcnQiLCJlbmQiLCJnZXRGaWx0ZXJlZEV2ZW50cyIsImJhc2VFdmVudHMiLCJ0cmltIiwiZXZlbnQiLCJzZWFyY2hMb3dlciIsInRvTG93ZXJDYXNlIiwiaW5jbHVkZXMiLCJvbkRyYWdTdGFydCIsImFjdGl2ZSIsImRhdGEiLCJpbml0aWFsUG9pbnRlclkiLCJpbml0aWFsRXZlbnRUb3AiLCJyZWN0IiwidHJhbnNsYXRlZCIsImdyYWJPZmZzZXRZIiwicGF5bG9hZCIsInR5cGUiLCJldmVudElkIiwib3JpZ2luYWxFdmVudCIsImRyYWdnZWRFbGVtZW50Iiwib2Zmc2V0V2lkdGgiLCJvZmZzZXRIZWlnaHQiLCJhZGRFdmVudExpc3RlbmVyIiwiaGFuZGxlTW91c2VNb3ZlIiwiaGFuZGxlVG91Y2hNb3ZlIiwib25EcmFnRW5kIiwib3ZlciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJhY3RpdmVEYXRhIiwib3ZlckRhdGEiLCJldmVudFRvVXBkYXRlIiwib3JpZ2luYWxTdGFydCIsIm9yaWdpbmFsRW5kIiwiZHVyYXRpb24iLCJuZXdTdGFydCIsInN0YXJ0c1dpdGgiLCJkYXlEaWZmZXJlbmNlIiwiZGF0ZSIsInNldEhvdXJzIiwiaG91ciIsIm1pbnV0ZSIsIm5ld0VuZCIsInJlY29yZElkIiwibmV3VmFsdWVzIiwidG9JU09TdHJpbmciLCJzdWNjZXNzIiwiZXJyb3IiLCJjbGllbnRYIiwiY2xpZW50WSIsInRvdWNoIiwidG91Y2hlcyIsImV2ZW50cyIsImdvVG9Ub2RheSIsImdvVG9QcmV2aW91cyIsInByZXZEYXRlIiwiZ29Ub05leHQiLCJoYW5kbGVSZXF1ZXN0Q3JlYXRlRXZlbnQiLCJ1c2VTeXN0ZW1UaW1lIiwibm93IiwibmV3RGF0ZSIsImdldEhvdXJzIiwiZ2V0TWludXRlcyIsImdldFNlY29uZHMiLCJnZXRNaWxsaXNlY29uZHMiLCJoYW5kbGVDcmVhdGVFdmVudCIsInN0YXJ0VGltZSIsImVuZFRpbWUiLCJyZWNvcmRWYWx1ZXMiLCJyZXN1bHQiLCJyZWNvcmRzIiwibmV3UmVjb3JkSWQiLCJoYW5kbGVFdmVudENsaWNrIiwiZ2V0SGVhZGVyRGF0ZURpc3BsYXkiLCJnZXREYXkiLCJoYW5kbGVFdmVudERlbGV0ZSIsInZpZXdUeXBlT3B0aW9ucyIsInNlbGVjdGVkVmlld09wdGlvbiIsImRpdiIsImNsYXNzTmFtZSIsImgxIiwidmFyaWFudCIsIm9uQ2xpY2siLCJvcHRpb25zIiwib25DaGFuZ2UiLCJzZWxlY3RlZCIsInBsYWNlaG9sZGVyIiwiaGlkZVNlYXJjaCIsImUiLCJ0YXJnZXQiLCJzcGFuIiwiaDMiLCJtb2RlIiwib25TZWxlY3QiLCJtb2RpZmllcnMiLCJkYXRhLWNhbGVuZGFyLWNvbnRlbnQiLCJvcGVuQWRkRXZlbnRGb3JtIiwiZHJvcEFuaW1hdGlvbiIsInNlZ21lbnQiLCJ2aWV3Iiwic3R5bGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/index.tsx\n"));

/***/ })

});