"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx":
/*!*******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/MonthView.tsx ***!
  \*******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MonthView: function() { return /* binding */ MonthView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CalendarEventItem */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx\");\n/* harmony import */ var _NoEvents__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./NoEvents */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/NoEvents.tsx\");\n/* harmony import */ var _CalendarSideCard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./CalendarSideCard */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarSideCard.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n/* harmony import */ var _utils_calendarSlotUtils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/calendarSlotUtils */ \"(app-pages-browser)/./src/utils/calendarSlotUtils.ts\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst DayCell = (param)=>{\n    let { date, children, onClick, isCurrentMonth } = param;\n    _s();\n    const { setNodeRef, isOver } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__.useDroppable)({\n        id: \"daycell-\".concat((0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(date, \"yyyy-MM-dd\")),\n        data: {\n            date: date,\n            type: \"daycell\"\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: setNodeRef,\n        onClick: onClick,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-b border-r border-neutral-300 relative cursor-pointer transition-colors group flex flex-col p-2 min-h-[120px] lg:p-3 lg:min-h-[140px]\", isCurrentMonth ? \"bg-white hover:bg-neutral-50\" : \"bg-neutral-100 hover:bg-neutral-200\", isOver && \"bg-blue-50 border-blue-200\"),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DayCell, \"DmJTTt6A5xWIX/faBiFge3FOLrw=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__.useDroppable\n    ];\n});\n_c = DayCell;\n// New helper function to process events for the entire month\nconst useMonthEvents = (weeks, events)=>{\n    _s1();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const positionedEventsByWeek = new Map();\n        weeks.forEach((week, weekIndex)=>{\n            const weekStart = week[0];\n            const weekEnd = week[6];\n            const weekEvents = events.filter((event)=>{\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                return eventStart <= weekEnd && eventEnd >= weekStart;\n            });\n            const spanningEvents = [];\n            weekEvents.forEach((event)=>{\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                const startDayIndex = week.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(day, eventStart));\n                const endDayIndex = week.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(day, eventEnd));\n                const eventSpansWeek = startDayIndex !== -1 || endDayIndex !== -1 || eventStart < weekStart && eventEnd > weekEnd;\n                if (eventSpansWeek) {\n                    const start = startDayIndex !== -1 ? startDayIndex : 0;\n                    const end = endDayIndex !== -1 ? endDayIndex : 6;\n                    spanningEvents.push({\n                        event,\n                        startDayIndex: start,\n                        endDayIndex: end,\n                        colSpan: end - start + 1\n                    });\n                }\n            });\n            const positioned = [];\n            const rows = [];\n            const sortedEvents = spanningEvents.sort((a, b)=>a.startDayIndex - b.startDayIndex || b.colSpan - a.colSpan);\n            sortedEvents.forEach((event)=>{\n                let assigned = false;\n                for(let i = 0; i < rows.length; i++){\n                    const row = rows[i];\n                    if (!row.some((e)=>event.startDayIndex <= e.endDayIndex && event.endDayIndex >= e.startDayIndex)) {\n                        row.push(event);\n                        positioned.push({\n                            ...event,\n                            row: i\n                        });\n                        assigned = true;\n                        break;\n                    }\n                }\n                if (!assigned) {\n                    rows.push([\n                        event\n                    ]);\n                    positioned.push({\n                        ...event,\n                        row: rows.length - 1\n                    });\n                }\n            });\n            positionedEventsByWeek.set(weekIndex, positioned);\n        });\n        return positionedEventsByWeek;\n    }, [\n        weeks,\n        events\n    ]);\n};\n_s1(useMonthEvents, \"nwk+m61qLgjDVUp4IGV/072DDN4=\");\nconst MonthView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, setSelectedDate, openAddEventForm, canEditData, handleEventClick, activeDragData } = param;\n    _s2();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_5__.useMaybeRecord)();\n    const isInRecordTab = !!maybeRecord;\n    // Memoize month calculations\n    const monthCalculations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const monthStart = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(selectedDate);\n        const monthEnd = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(selectedDate);\n        const startDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(monthStart, {\n            weekStartsOn: 0\n        });\n        const endDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(monthEnd, {\n            weekStartsOn: 0\n        });\n        const days = [];\n        let day = startDay;\n        while(day <= endDay){\n            days.push(day);\n            day = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(day, 1);\n        }\n        const weeks = [];\n        for(let i = 0; i < days.length; i += 7){\n            weeks.push(days.slice(i, i + 7));\n        }\n        return {\n            monthStart,\n            monthEnd,\n            startDay,\n            endDay,\n            days,\n            weeks\n        };\n    }, [\n        selectedDate\n    ]);\n    // Memoize month events\n    const monthEvents = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>events.filter((event)=>{\n            const eventStart = new Date(event.start);\n            return eventStart >= monthCalculations.startDay && eventStart <= monthCalculations.endDay;\n        }), [\n        events,\n        monthCalculations.startDay,\n        monthCalculations.endDay\n    ]);\n    const positionedEventsByWeek = useMonthEvents(monthCalculations.weeks, events);\n    // Calculate slot assignments for the entire month\n    const slotCalculations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_utils_calendarSlotUtils__WEBPACK_IMPORTED_MODULE_10__.calculateMonthSlots)(monthCalculations.monthStart, events), [\n        monthCalculations.monthStart,\n        events\n    ]);\n    // Render empty state when no events\n    const renderEmptyState = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-7 border-b border-neutral-300 bg-white\",\n                    children: [\n                        \"Sunday\",\n                        \"Monday\",\n                        \"Tuesday\",\n                        \"Wednesday\",\n                        \"Thursday\",\n                        \"Friday\",\n                        \"Saturday\"\n                    ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-black\", \"py-2 text-xs\"),\n                            children: dayName.substring(0, 3)\n                        }, dayName, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NoEvents__WEBPACK_IMPORTED_MODULE_7__.NoEvents, {\n                    title: \"No events this month\",\n                    message: \"\".concat((0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(selectedDate, \"MMMM yyyy\"), \" is completely free. Start planning your month!\"),\n                    showCreateButton: canEditData,\n                    onCreate: ()=>openAddEventForm(selectedDate)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n            lineNumber: 185,\n            columnNumber: 5\n        }, undefined);\n    // Render day cell content\n    const renderDayCellContent = (day)=>{\n        const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n        const isCurrentDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_18__[\"default\"])(day);\n        const dateKey = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(day, \"yyyy-MM-dd\");\n        const slotResult = slotCalculations.get(dateKey);\n        if (!slotResult) return null;\n        const { visibleEvents, overflowCount, slotAssignments } = slotResult;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center rounded-full font-semibold text-xs w-6 h-6\", isCurrentDay ? \"bg-black text-white\" : isCurrentMonth ? \"text-black hover:bg-neutral-100\" : \"text-neutral-400\"),\n                            children: (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(day, \"d\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, undefined),\n                        canEditData && isCurrentMonth && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            className: \"rounded-full h-6 w-6 p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-neutral-100 hidden lg:inline-flex\",\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                setTimeout(()=>openAddEventForm(day), 150);\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                className: \"h-3 w-3 text-black\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-1\",\n                    children: [\n                        visibleEvents.map((event)=>{\n                            var _activeDragData_payload;\n                            const slot = slotAssignments.get(event.id);\n                            if (slot === undefined) return null;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__.CalendarEventItem, {\n                                event: event,\n                                style: {\n                                    position: \"relative\",\n                                    zIndex: selectedEvent === event.id ? 20 : 10\n                                },\n                                onClick: (e)=>{\n                                    e.stopPropagation();\n                                    setSelectedEvent(event.id);\n                                    handleEventClick(event);\n                                },\n                                view: \"month\",\n                                isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === event.id\n                            }, event.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 15\n                            }, undefined);\n                        }),\n                        overflowCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"w-full text-left px-2 py-1 text-xs text-neutral-600 hover:text-neutral-900 font-medium\",\n                            onClick: ()=>{\n                                // TODO: Show overflow events in a popup\n                                console.log(\"Show overflow events\");\n                            },\n                            children: [\n                                \"+ \",\n                                overflowCount,\n                                \" more\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true);\n    };\n    // Render main view\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-background flex flex-col lg:flex-row\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col min-h-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        \"data-day-headers\": \"true\",\n                        className: \"grid grid-cols-7 border-b border-neutral-300 sticky top-0 z-10 bg-white\",\n                        children: [\n                            \"Sunday\",\n                            \"Monday\",\n                            \"Tuesday\",\n                            \"Wednesday\",\n                            \"Thursday\",\n                            \"Friday\",\n                            \"Saturday\"\n                        ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-black\", \"py-2 text-xs\"),\n                                children: dayName.substring(0, 3)\n                            }, dayName, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n                        className: \"flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-7 border-neutral-300 border-b\",\n                            children: monthCalculations.weeks.map((week, weekIndex)=>week.map((day, dayIndex)=>{\n                                    const weekEvents = positionedEventsByWeek.get(weekIndex) || [];\n                                    const dayEvents = weekEvents.filter((pe)=>pe.startDayIndex === dayIndex);\n                                    const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DayCell, {\n                                        date: day,\n                                        isCurrentMonth: isCurrentMonth,\n                                        onClick: ()=>setSelectedDate(day),\n                                        children: renderDayCellContent(day)\n                                    }, \"\".concat(weekIndex, \"-\").concat(dayIndex), false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 19\n                                    }, undefined);\n                                }))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 283,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarSideCard__WEBPACK_IMPORTED_MODULE_8__.CalendarSideCard, {\n                selectedDate: selectedDate,\n                events: events,\n                selectedEvent: selectedEvent,\n                setSelectedEvent: setSelectedEvent,\n                handleEventClick: handleEventClick\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 324,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 282,\n        columnNumber: 5\n    }, undefined);\n};\n_s2(MonthView, \"v8CRx9zploCJ4LUg3Rc7KC8ks8I=\", false, function() {\n    return [\n        _providers_record__WEBPACK_IMPORTED_MODULE_5__.useMaybeRecord,\n        useMonthEvents\n    ];\n});\n_c1 = MonthView;\nfunction isMultiDay(event) {\n    return !(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(new Date(event.start), new Date(event.end));\n}\nvar _c, _c1;\n$RefreshReg$(_c, \"DayCell\");\n$RefreshReg$(_c1, \"MonthView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/calendarSlotUtils.ts":
/*!****************************************!*\
  !*** ./src/utils/calendarSlotUtils.ts ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateDaySlots: function() { return /* binding */ calculateDaySlots; },\n/* harmony export */   calculateMonthSlots: function() { return /* binding */ calculateMonthSlots; },\n/* harmony export */   calculateWeekSlots: function() { return /* binding */ calculateWeekSlots; }\n/* harmony export */ });\n/* harmony import */ var _barrel_optimize_names_endOfDay_format_isSameDay_isWithinInterval_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! __barrel_optimize__?names=endOfDay,format,isSameDay,isWithinInterval,startOfDay!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_endOfDay_format_isSameDay_isWithinInterval_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=endOfDay,format,isSameDay,isWithinInterval,startOfDay!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_endOfDay_format_isSameDay_isWithinInterval_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=endOfDay,format,isSameDay,isWithinInterval,startOfDay!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_endOfDay_format_isSameDay_isWithinInterval_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=endOfDay,format,isSameDay,isWithinInterval,startOfDay!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isWithinInterval/index.js\");\n/* harmony import */ var _barrel_optimize_names_endOfDay_format_isSameDay_isWithinInterval_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=endOfDay,format,isSameDay,isWithinInterval,startOfDay!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n\nfunction calculateDaySlots(date, events) {\n    let maxVisibleSlots = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 4;\n    // First, sort events by priority:\n    // 1. Multi-day events first (they should appear at the top)\n    // 2. Then by start time for single-day events\n    const sortedEvents = [\n        ...events\n    ].sort((a, b)=>{\n        const aIsMultiDay = !(0,_barrel_optimize_names_endOfDay_format_isSameDay_isWithinInterval_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(new Date(a.start), new Date(a.end));\n        const bIsMultiDay = !(0,_barrel_optimize_names_endOfDay_format_isSameDay_isWithinInterval_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(new Date(b.start), new Date(b.end));\n        if (aIsMultiDay && !bIsMultiDay) return -1;\n        if (!aIsMultiDay && bIsMultiDay) return 1;\n        return new Date(a.start).getTime() - new Date(b.start).getTime();\n    });\n    // Filter events that should appear in this cell\n    const relevantEvents = sortedEvents.filter((event)=>{\n        const eventStart = (0,_barrel_optimize_names_endOfDay_format_isSameDay_isWithinInterval_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(new Date(event.start));\n        const eventEnd = (0,_barrel_optimize_names_endOfDay_format_isSameDay_isWithinInterval_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(new Date(event.end));\n        const cellDate = (0,_barrel_optimize_names_endOfDay_format_isSameDay_isWithinInterval_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(date);\n        return (0,_barrel_optimize_names_endOfDay_format_isSameDay_isWithinInterval_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(cellDate, {\n            start: eventStart,\n            end: eventEnd\n        });\n    });\n    // Calculate which events are visible and which go to overflow\n    const visibleEvents = relevantEvents.slice(0, maxVisibleSlots);\n    const overflowCount = Math.max(0, relevantEvents.length - maxVisibleSlots);\n    // Assign slot numbers to visible events\n    const slotAssignments = new Map();\n    visibleEvents.forEach((event, index)=>{\n        slotAssignments.set(event.id, index);\n    });\n    return {\n        visibleEvents,\n        overflowCount,\n        slotAssignments\n    };\n}\n// Helper function to get events for a specific week\nfunction calculateWeekSlots(weekStart, events) {\n    let maxVisibleSlots = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 4;\n    const results = new Map();\n    // Calculate slots for each day in the week\n    for(let i = 0; i < 7; i++){\n        const currentDate = new Date(weekStart);\n        currentDate.setDate(weekStart.getDate() + i);\n        const dateKey = (0,_barrel_optimize_names_endOfDay_format_isSameDay_isWithinInterval_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(currentDate, \"yyyy-MM-dd\");\n        results.set(dateKey, calculateDaySlots(currentDate, events, maxVisibleSlots));\n    }\n    return results;\n}\n// Helper function to get events for a month\nfunction calculateMonthSlots(monthStart, events) {\n    let maxVisibleSlots = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 4;\n    const results = new Map();\n    // Get the first day of the calendar view (might be in previous month)\n    const firstDay = new Date(monthStart);\n    firstDay.setDate(1);\n    const startDay = new Date(firstDay);\n    startDay.setDate(firstDay.getDate() - firstDay.getDay());\n    // Calculate 6 weeks worth of days (42 days total)\n    for(let i = 0; i < 42; i++){\n        const currentDate = new Date(startDay);\n        currentDate.setDate(startDay.getDate() + i);\n        const dateKey = (0,_barrel_optimize_names_endOfDay_format_isSameDay_isWithinInterval_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(currentDate, \"yyyy-MM-dd\");\n        results.set(dateKey, calculateDaySlots(currentDate, events, maxVisibleSlots));\n    }\n    return results;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/calendarSlotUtils.ts\n"));

/***/ })

});