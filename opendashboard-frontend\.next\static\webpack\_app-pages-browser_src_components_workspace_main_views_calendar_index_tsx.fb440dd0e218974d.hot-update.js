/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx":
/*!*******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/MonthView.tsx ***!
  \*******************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// import React, { useMemo } from 'react';\n// import { format, startOfMonth, endOfMonth, startOfWeek, endOfWeek, addDays, isSameDay, isToday } from 'date-fns';\n// import { cn } from '@/lib/utils';\n// import { Button } from '@/components/ui/button';\n// import { PlusIcon } from '@heroicons/react/24/outline';\n// import { ScrollArea } from '@/components/ui/scroll-area';\n// import { CalendarEvent } from '@/typings/page';\n// import { useMaybeRecord } from '@/providers/record';\n// import { CalendarEventItem } from './CalendarEventItem';\n// import { NoEvents } from './NoEvents';\n// import { CalendarSideCard } from './CalendarSideCard';\n// import { useDroppable } from '@dnd-kit/core';\n// interface MonthViewProps {\n//   selectedDate: Date;\n//   events: CalendarEvent[];\n//   selectedEvent: string | null;\n//   setSelectedEvent: (id: string) => void;\n//   setSelectedDate: (date: Date) => void;\n//   openAddEventForm: (date: Date) => void;\n//   canEditData: boolean;\n//   handleEventClick: (event: CalendarEvent) => void;\n//   activeDragData: any;\n// }\n// const DayCell = ({\n//   date,\n//   children,\n//   onClick,\n//   isCurrentMonth\n// }: {\n//   date: Date;\n//   children: React.ReactNode;\n//   onClick: () => void;\n//   isCurrentMonth: boolean;\n// }) => {\n//   const { setNodeRef, isOver } = useDroppable({\n//     id: `daycell-${format(date, 'yyyy-MM-dd')}`,\n//     data: {\n//       date: date,\n//       type: 'daycell'\n//     }\n//   });\n//   return (\n//     <div\n//       ref={setNodeRef}\n//       onClick={onClick}\n//       className={cn(\n//         \"border-b border-r border-neutral-300 relative cursor-pointer transition-colors group flex flex-col p-2 min-h-[120px] lg:p-3 lg:min-h-[140px]\",\n//         isCurrentMonth\n//           ? \"bg-white hover:bg-neutral-50\"\n//           : \"bg-neutral-100 hover:bg-neutral-200\",\n//         isOver && \"bg-blue-50 border-blue-200\"\n//       )}\n//     >\n//       {children}\n//     </div>\n//   );\n// };\n// // Helper function to check if an event affects a specific day\n// const eventAffectsDay = (event: CalendarEvent, day: Date): boolean => {\n//   const eventStart = new Date(event.start);\n//   const eventEnd = new Date(event.end);\n//   const dayStart = new Date(day);\n//   const dayEnd = new Date(day);\n//   dayEnd.setHours(23, 59, 59, 999);\n//   return eventStart <= dayEnd && eventEnd >= dayStart;\n// };\n// // New helper function to process events for the entire month with proper slot tracking\n// const useMonthEvents = (weeks: Date[][], events: CalendarEvent[]) => {\n//   return useMemo(() => {\n//     const positionedEventsByWeek = new Map<number, any[]>();\n//     const slotUsageByDay = new Map<string, number>(); // Track slot usage per day\n//     weeks.forEach((week, weekIndex) => {\n//       const weekStart = week[0];\n//       const weekEnd = week[6];\n//       // Get all events that intersect with this week\n//       const weekEvents = events.filter(event => {\n//         const eventStart = new Date(event.start);\n//         const eventEnd = new Date(event.end);\n//         return eventStart <= weekEnd && eventEnd >= weekStart;\n//       });\n//       const spanningEvents: any[] = [];\n//       weekEvents.forEach(event => {\n//         const eventStart = new Date(event.start);\n//         const eventEnd = new Date(event.end);\n//         // Find which days this event spans in this week\n//         const startDayIndex = week.findIndex(day => isSameDay(day, eventStart));\n//         const endDayIndex = week.findIndex(day => isSameDay(day, eventEnd));\n//         // Calculate actual span within this week\n//         let actualStart = 0;\n//         let actualEnd = 6;\n//         if (startDayIndex !== -1) {\n//           actualStart = startDayIndex;\n//         }\n//         if (endDayIndex !== -1) {\n//           actualEnd = endDayIndex;\n//         }\n//         // Check if event intersects with this week\n//         const eventSpansWeek = startDayIndex !== -1 || endDayIndex !== -1 || (eventStart < weekStart && eventEnd > weekEnd);\n//         if (eventSpansWeek) {\n//           spanningEvents.push({\n//             event,\n//             startDayIndex: actualStart,\n//             endDayIndex: actualEnd,\n//             colSpan: actualEnd - actualStart + 1,\n//           });\n//         }\n//       });\n//       // Sort events by start day, then by span length (longer events first)\n//       const sortedEvents = spanningEvents.sort((a, b) => {\n//         if (a.startDayIndex !== b.startDayIndex) {\n//           return a.startDayIndex - b.startDayIndex;\n//         }\n//         return b.colSpan - a.colSpan;\n//       });\n//       // Position events and track slot usage\n//       const positioned: any[] = [];\n//       const rows: any[][] = [];\n//       sortedEvents.forEach(eventData => {\n//         // Check if this event can be placed (all days it spans have available slots)\n//         const affectedDays = week.slice(eventData.startDayIndex, eventData.endDayIndex + 1);\n//         const canPlace = affectedDays.every(day => {\n//           const dayKey = format(day, 'yyyy-MM-dd');\n//           const currentUsage = slotUsageByDay.get(dayKey) || 0;\n//           return currentUsage < 4; // Maximum 4 slots per day\n//         });\n//         if (!canPlace) {\n//           // Event cannot be placed, skip it (it will be in the \"+more\" count)\n//           return;\n//         }\n//         // Find available row\n//         let assigned = false;\n//         for (let i = 0; i < rows.length; i++) {\n//           const row = rows[i];\n//           const hasConflict = row.some(existingEvent => \n//             eventData.startDayIndex <= existingEvent.endDayIndex && \n//             eventData.endDayIndex >= existingEvent.startDayIndex\n//           );\n//           if (!hasConflict) {\n//             row.push(eventData);\n//             positioned.push({ ...eventData, row: i });\n//             assigned = true;\n//             break;\n//           }\n//         }\n//         if (!assigned) {\n//           rows.push([eventData]);\n//           positioned.push({ ...eventData, row: rows.length - 1 });\n//         }\n//         // Update slot usage for all affected days\n//         affectedDays.forEach(day => {\n//           const dayKey = format(day, 'yyyy-MM-dd');\n//           const currentUsage = slotUsageByDay.get(dayKey) || 0;\n//           slotUsageByDay.set(dayKey, currentUsage + 1);\n//         });\n//       });\n//       positionedEventsByWeek.set(weekIndex, positioned);\n//     });\n//     return { positionedEventsByWeek, slotUsageByDay };\n//   }, [weeks, events]);\n// };\n// export const MonthView: React.FC<MonthViewProps> = ({\n//   selectedDate,\n//   events,\n//   selectedEvent,\n//   setSelectedEvent,\n//   setSelectedDate,\n//   openAddEventForm,\n//   canEditData,\n//   handleEventClick,\n//   activeDragData,\n// }) => {\n//   const maybeRecord = useMaybeRecord();\n//   const isInRecordTab = !!maybeRecord;\n//   // Memoize month calculations\n//   const monthCalculations = useMemo(() => {\n//     const monthStart = startOfMonth(selectedDate);\n//     const monthEnd = endOfMonth(selectedDate);\n//     const startDay = startOfWeek(monthStart, { weekStartsOn: 0 });\n//     const endDay = endOfWeek(monthEnd, { weekStartsOn: 0 });\n//     const days = [];\n//     let day = startDay;\n//     while (day <= endDay) {\n//       days.push(day);\n//       day = addDays(day, 1);\n//     }\n//     const weeks = [];\n//     for (let i = 0; i < days.length; i += 7) {\n//       weeks.push(days.slice(i, i + 7));\n//     }\n//     return { monthStart, monthEnd, startDay, endDay, days, weeks };\n//   }, [selectedDate]);\n//   // Memoize month events\n//   const monthEvents = useMemo(() => \n//     events.filter(event => {\n//       const eventStart = new Date(event.start);\n//       const eventEnd = new Date(event.end);\n//       return eventStart <= monthCalculations.endDay && \n//              eventEnd >= monthCalculations.startDay;\n//     }), \n//     [events, monthCalculations.startDay, monthCalculations.endDay]\n//   );\n//   const { positionedEventsByWeek, slotUsageByDay } = useMonthEvents(monthCalculations.weeks, monthEvents);\n//   // Render empty state when no events\n//   const renderEmptyState = () => (\n//     <div className=\"flex flex-col h-full bg-background\">\n//       <div className=\"grid grid-cols-7 border-b border-neutral-300 bg-white\">\n//         {['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'].map(dayName => (\n//           <div key={dayName} className={cn(\n//             \"text-center font-semibold text-black\",\n//             \"py-2 text-xs\"\n//           )}>\n//             {dayName.substring(0, 3)}\n//           </div>\n//         ))}\n//       </div>\n//       <NoEvents\n//         title=\"No events this month\"\n//         message={`${format(selectedDate, 'MMMM yyyy')} is completely free. Start planning your month!`}\n//         showCreateButton={canEditData}\n//         onCreate={() => openAddEventForm(selectedDate)}\n//       />\n//     </div>\n//   );\n//   // Render day cell content\n//   const renderDayCellContent = (day: Date, dayEvents: any[]) => {\n//     const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n//     const isCurrentDay = isToday(day);\n//     const MAX_VISIBLE_EVENTS = 4;\n//     const ROW_HEIGHT = 28;\n//     const dayKey = format(day, 'yyyy-MM-dd');\n//     // Get all events that affect this day (for the +more count)\n//     const allDayEvents = monthEvents.filter(event => eventAffectsDay(event, day));\n//     const totalEventsForDay = allDayEvents.length;\n//     const sortedEvents = dayEvents.sort((a, b) => a.row - b.row);\n//     const visibleEvents = sortedEvents.slice(0, MAX_VISIBLE_EVENTS);\n//     const hasMore = totalEventsForDay > MAX_VISIBLE_EVENTS;\n//     const hiddenEventsCount = Math.max(0, totalEventsForDay - MAX_VISIBLE_EVENTS);\n//     // Calculate container height\n//     const maxRow = visibleEvents.reduce((max, event) => Math.max(max, event.row), -1);\n//     const containerHeight = hasMore\n//       ? (MAX_VISIBLE_EVENTS * ROW_HEIGHT) + 20 \n//       : (maxRow + 1) * ROW_HEIGHT;\n//     return (\n//       <>\n//         <div className=\"flex items-center justify-between mb-2\">\n//           <span className={cn(\n//             \"inline-flex items-center justify-center rounded-full font-semibold text-xs w-6 h-6\",\n//             isCurrentDay ? \"bg-black text-white\" : isCurrentMonth ? \"text-black hover:bg-neutral-100\" : \"text-neutral-400\"\n//           )}>\n//             {format(day, 'd')}\n//           </span>\n//           {canEditData && isCurrentMonth && (\n//             <Button\n//               variant=\"ghost\"\n//               className=\"rounded-full h-6 w-6 p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-neutral-100 hidden lg:inline-flex\"\n//               onClick={(e) => {\n//                 e.stopPropagation();\n//                 openAddEventForm(day);\n//               }}\n//             >\n//               <PlusIcon className=\"h-3 w-3 text-black\" />\n//             </Button>\n//           )}\n//         </div>\n//         <div className=\"relative\" style={{ height: `${containerHeight}px` }}>\n//           {visibleEvents.map(pe => (\n//             <div\n//               key={pe.event.id}\n//               className=\"absolute\"\n//               style={{\n//                 top: `${pe.row * ROW_HEIGHT}px`,\n//                 left: '2px',\n//                 width: pe.colSpan > 1 \n//                   ? `calc(${pe.colSpan * 100}% + ${(pe.colSpan - 1) * 19}px)`\n//                   : 'calc(100% - 4px)',\n//                 zIndex: 10 + pe.row,\n//               }}\n//             >\n//               <CalendarEventItem\n//                 event={pe.event}\n//                 view=\"month\"\n//                 onClick={(e) => {\n//                   e.stopPropagation();\n//                   setSelectedEvent(pe.event.id);\n//                   handleEventClick(pe.event);\n//                 }}\n//                 isDragging={activeDragData?.payload?.id === pe.event.id}\n//               />\n//             </div>\n//           ))}\n//           {hasMore && (\n//             <div \n//               className=\"text-black hover:text-black font-medium text-xs cursor-pointer\"\n//               style={{\n//                 position: 'absolute',\n//                 top: `${MAX_VISIBLE_EVENTS * ROW_HEIGHT}px`,\n//                 left: '2px',\n//               }}\n//               onClick={(e) => {\n//                 e.stopPropagation();\n//                 setSelectedDate(day);\n//               }}\n//             >\n//               + {hiddenEventsCount} more\n//             </div>\n//           )}\n//         </div>\n//       </>\n//     );\n//   };\n//   // Render main view\n//   return (\n//     <div className=\"h-full bg-background flex flex-col lg:flex-row\">\n//       <div className=\"flex-1 flex flex-col min-h-0\">\n//         {/* Day Headers */}\n//         <div \n//           data-day-headers=\"true\"\n//           className=\"grid grid-cols-7 border-b border-neutral-300 sticky top-0 z-10 bg-white\"\n//         >\n//           {['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'].map(dayName => (\n//             <div key={dayName} className={cn(\n//               \"text-center font-semibold text-black\",\n//               \"py-2 text-xs\"\n//             )}>\n//               {dayName.substring(0, 3)}\n//             </div>\n//           ))}\n//         </div>\n//         {/* Month Grid */}\n//         <ScrollArea className=\"flex-1\">\n//           <div className=\"grid grid-cols-7 border-neutral-300 border-b\">\n//             {monthCalculations.weeks.map((week, weekIndex) =>\n//               week.map((day, dayIndex) => {\n//                 const weekEvents = positionedEventsByWeek.get(weekIndex) || [];\n//                 const dayEvents = weekEvents.filter(pe => pe.startDayIndex === dayIndex);\n//                 const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n//                 return (\n//                   <DayCell\n//                     key={`${weekIndex}-${dayIndex}`}\n//                     date={day}\n//                     isCurrentMonth={isCurrentMonth}\n//                     onClick={() => setSelectedDate(day)}\n//                   >\n//                     {renderDayCellContent(day, dayEvents)}\n//                   </DayCell>\n//                 );\n//               }),\n//             )}\n//           </div>\n//         </ScrollArea>\n//       </div>\n//       <CalendarSideCard\n//         selectedDate={selectedDate}\n//         events={events}\n//         selectedEvent={selectedEvent}\n//         setSelectedEvent={setSelectedEvent}\n//         handleEventClick={handleEventClick}\n//       />\n//     </div>\n//   );\n// };\n// function isMultiDay(event: CalendarEvent): boolean {\n//   return !isSameDay(new Date(event.start), new Date(event.end));\n// }\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx\n"));

/***/ })

});