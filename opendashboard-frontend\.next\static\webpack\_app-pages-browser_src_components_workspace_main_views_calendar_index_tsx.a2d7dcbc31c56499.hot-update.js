"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/DayView.tsx":
/*!*****************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/DayView.tsx ***!
  \*****************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DayView: function() { return /* binding */ DayView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=format,isToday,setHours!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=format,isToday,setHours!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=format,isToday,setHours!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/setHours/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CalendarEventSegment */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx\");\n/* harmony import */ var _NoEvents__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./NoEvents */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/NoEvents.tsx\");\n/* harmony import */ var _utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/multiDayEventUtils */ \"(app-pages-browser)/./src/utils/multiDayEventUtils.ts\");\n/* harmony import */ var _utils_eventCollisionUtils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/eventCollisionUtils */ \"(app-pages-browser)/./src/utils/eventCollisionUtils.ts\");\n/* harmony import */ var _AllDayRow__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./AllDayRow */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/AllDayRow.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst TimeSlot = (param)=>{\n    let { hour, date, onDoubleClick, children } = param;\n    _s();\n    const [isHovering, setIsHovering] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const timeSlotRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Create minute segments only when hovering\n    const minuteSegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (!isHovering) return [];\n        return Array.from({\n            length: 60\n        }, (_, i)=>i);\n    }, [\n        isHovering\n    ]);\n    const handleMouseEnter = ()=>{\n        setIsHovering(true);\n    };\n    const handleMouseLeave = ()=>{\n        setIsHovering(false);\n        setMousePosition(null);\n    };\n    const handleMouseMove = (e)=>{\n        if (!timeSlotRef.current) return;\n        const rect = timeSlotRef.current.getBoundingClientRect();\n        setMousePosition({\n            x: e.clientX - rect.left,\n            y: e.clientY - rect.top\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: timeSlotRef,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-1 relative min-h-[60px] cursor-pointer\"),\n        style: {\n            height: \"60px\"\n        },\n        onMouseEnter: handleMouseEnter,\n        onMouseLeave: handleMouseLeave,\n        onMouseMove: handleMouseMove,\n        children: [\n            isHovering && mousePosition && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex flex-col\",\n                children: minuteSegments.map((minute)=>{\n                    const segmentTop = minute / 60 * 100;\n                    const segmentBottom = (minute + 1) / 60 * 100;\n                    const isActive = mousePosition.y >= segmentTop / 100 * 60 && mousePosition.y < segmentBottom / 100 * 60;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MinuteSegment, {\n                        date: date,\n                        hour: hour,\n                        minute: minute,\n                        style: {\n                            height: \"\".concat(100 / 60, \"%\"),\n                            top: \"\".concat(segmentTop, \"%\"),\n                            opacity: isActive ? 0.1 : 0\n                        },\n                        onDoubleClick: ()=>onDoubleClick(minute)\n                    }, minute, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 15\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                lineNumber: 84,\n                columnNumber: 9\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TimeSlot, \"dQOYw4CYBhxjpdw8b31LvmtVyxA=\");\n_c = TimeSlot;\n// MinuteSegment component optimized for performance\nconst MinuteSegment = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(_s1((param)=>{\n    let { date, hour, minute, style, onDoubleClick } = param;\n    _s1();\n    const { setNodeRef, isOver } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__.useDroppable)({\n        id: \"timeslot-\".concat((0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(date, \"yyyy-MM-dd\"), \"-\").concat(hour, \"-\").concat(minute),\n        data: {\n            date: date,\n            hour,\n            minute,\n            type: \"timeslot-minute\"\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: setNodeRef,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"absolute w-full\", isOver && \"bg-blue-50\"),\n        style: style,\n        onDoubleClick: onDoubleClick\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n        lineNumber: 138,\n        columnNumber: 5\n    }, undefined);\n}, \"DmJTTt6A5xWIX/faBiFge3FOLrw=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__.useDroppable\n    ];\n}));\n_c1 = MinuteSegment;\nconst DayView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, openAddEventForm, canEditData, savedScrollTop, handleEventClick, activeDragData } = param;\n    _s2();\n    const hours = Array.from({\n        length: 24\n    }, (_, i)=>i);\n    // Memoize event segments to prevent unnecessary recalculations\n    const daySegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const allSegments = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.eventsToSegments)(events);\n        return (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.getSegmentsForDay)(allSegments, selectedDate);\n    }, [\n        events,\n        selectedDate\n    ]);\n    // Separate all-day and time-slot segments\n    const allDaySegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.getAllDaySegments)(daySegments), [\n        daySegments\n    ]);\n    const timeSlotSegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.getTimeSlotSegments)(daySegments), [\n        daySegments\n    ]);\n    // Calculate layout for overlapping segments\n    const { segmentLayouts } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return (0,_utils_eventCollisionUtils__WEBPACK_IMPORTED_MODULE_6__.calculateLayout)(timeSlotSegments);\n    }, [\n        timeSlotSegments\n    ]);\n    // Memoize current time position\n    const currentTimePosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate) ? {\n            hour: new Date().getHours(),\n            minutes: new Date().getMinutes()\n        } : null, [\n        selectedDate\n    ]);\n    // Render empty state when no events\n    const renderEmptyState = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NoEvents__WEBPACK_IMPORTED_MODULE_4__.NoEvents, {\n            title: \"No events scheduled\",\n            message: (0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate) ? \"You have a free day ahead! Add an event to get started.\" : \"\".concat((0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(selectedDate, \"EEEE, MMMM d\"), \" is completely free.\"),\n            showCreateButton: canEditData,\n            onCreate: ()=>{\n                const newDate = new Date(selectedDate);\n                newDate.setHours(9, 0, 0, 0);\n                openAddEventForm(newDate);\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n            lineNumber: 191,\n            columnNumber: 5\n        }, undefined);\n    // Render time slots with events\n    const renderTimeSlots = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 overflow-auto relative bg-white\",\n            id: \"day-view-container\",\n            children: [\n                hours.map((hour, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex border-b border-neutral-300 hover:bg-neutral-50 transition-colors\", i === hours.length - 1 && \"border-b-neutral-300\"),\n                        style: {\n                            height: \"60px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                \"data-time-labels\": \"true\",\n                                className: \"flex items-start justify-end pr-4 pt-2 text-xs font-medium text-black border-r border-neutral-300 sticky left-0 bg-white z-10 w-14 lg:w-20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs font-semibold\",\n                                            children: (0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(selectedDate, hour), \"h\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-black opacity-60\",\n                                            children: (0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(selectedDate, hour), \"a\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TimeSlot, {\n                                hour: hour,\n                                date: selectedDate,\n                                onDoubleClick: (minute)=>{\n                                    if (canEditData) {\n                                        const newDate = new Date(selectedDate);\n                                        newDate.setHours(hour, minute, 0, 0);\n                                        openAddEventForm(newDate);\n                                    }\n                                },\n                                children: segmentLayouts.map((layout)=>{\n                                    var _activeDragData_payload;\n                                    const segmentStart = layout.segment.startTime;\n                                    const isFirstHour = segmentStart.getHours() === hour;\n                                    if (!isFirstHour) return null;\n                                    const segmentHeight = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.getSegmentHeight)(layout.segment);\n                                    const topOffset = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.getSegmentTopOffset)(layout.segment);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__.CalendarEventSegment, {\n                                        segment: layout.segment,\n                                        style: {\n                                            height: \"\".concat(segmentHeight, \"px\"),\n                                            position: \"absolute\",\n                                            top: \"\".concat(topOffset, \"px\"),\n                                            left: \"\".concat(layout.left, \"%\"),\n                                            width: \"\".concat(layout.width, \"%\"),\n                                            zIndex: selectedEvent === layout.segment.originalEventId ? 20 : layout.zIndex,\n                                            paddingRight: \"2px\",\n                                            border: layout.hasOverlap ? \"1px solid white\" : \"none\"\n                                        },\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            const container = document.getElementById(\"day-view-container\");\n                                            if (container) {\n                                                savedScrollTop.current = container.scrollTop;\n                                            }\n                                            setSelectedEvent(layout.segment.originalEventId);\n                                            handleEventClick(layout.segment.originalEvent);\n                                        },\n                                        view: \"day\",\n                                        isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === layout.segment.id\n                                    }, layout.segment.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 17\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, i, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, undefined)),\n                currentTimePosition && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute flex items-center z-30 pointer-events-none left-14 lg:left-20\",\n                    style: {\n                        top: \"\".concat((currentTimePosition.hour + currentTimePosition.minutes / 60) * 60, \"px\"),\n                        right: \"4px\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-3 h-3 rounded-full bg-red-500 border-2 border-white shadow-lg -ml-1.5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 border-t-2 border-red-500 shadow-sm\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n            lineNumber: 207,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center bg-white border-b border-neutral-300 py-2 px-2 lg:px-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"font-semibold text-black mb-1 text-xs\",\n                        children: (0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(selectedDate, \"EEEE\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center font-medium text-xs w-6 h-6 rounded-full\", (0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate) ? \"bg-black text-white\" : \"text-black hover:bg-neutral-100\"),\n                        children: (0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(selectedDate, \"d\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                lineNumber: 304,\n                columnNumber: 7\n            }, undefined),\n            daySegments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AllDayRow__WEBPACK_IMPORTED_MODULE_7__.AllDayRow, {\n                selectedDate: selectedDate,\n                segments: allDaySegments,\n                selectedEvent: selectedEvent,\n                setSelectedEvent: setSelectedEvent,\n                handleEventClick: handleEventClick,\n                canEditData: canEditData,\n                openAddEventForm: openAddEventForm,\n                view: \"day\",\n                activeDragData: activeDragData\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                lineNumber: 320,\n                columnNumber: 9\n            }, undefined),\n            daySegments.length === 0 ? renderEmptyState() : renderTimeSlots()\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n        lineNumber: 302,\n        columnNumber: 5\n    }, undefined);\n};\n_s2(DayView, \"rKQaW4qzJjohXAHqKTEXOPEYpLE=\");\n_c2 = DayView;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"TimeSlot\");\n$RefreshReg$(_c1, \"MinuteSegment\");\n$RefreshReg$(_c2, \"DayView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/DayView.tsx\n"));

/***/ })

});