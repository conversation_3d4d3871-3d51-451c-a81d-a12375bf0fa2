"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx":
/*!*******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/MonthView.tsx ***!
  \*******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MonthView: function() { return /* binding */ MonthView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CalendarEventItem */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx\");\n/* harmony import */ var _NoEvents__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./NoEvents */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/NoEvents.tsx\");\n/* harmony import */ var _CalendarSideCard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./CalendarSideCard */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarSideCard.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst DayCell = (param)=>{\n    let { date, children, onClick, isCurrentMonth } = param;\n    _s();\n    const { setNodeRef, isOver } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__.useDroppable)({\n        id: \"daycell-\".concat((0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(date, \"yyyy-MM-dd\")),\n        data: {\n            date: date,\n            type: \"daycell\"\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: setNodeRef,\n        onClick: onClick,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-b border-r border-neutral-300 relative cursor-pointer transition-colors group flex flex-col p-2 min-h-[120px] lg:p-3 lg:min-h-[140px]\", isCurrentMonth ? \"bg-white hover:bg-neutral-50\" : \"bg-neutral-100 hover:bg-neutral-200\", isOver && \"bg-blue-50 border-blue-200\"),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DayCell, \"DmJTTt6A5xWIX/faBiFge3FOLrw=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__.useDroppable\n    ];\n});\n_c = DayCell;\n// New helper function to process events for the entire month\nconst useMonthEvents = (weeks, events)=>{\n    _s1();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const positionedEventsByWeek = new Map();\n        const eventOccupiedRows = new Map(); // Track which row each event occupies\n        weeks.forEach((week, weekIndex)=>{\n            const weekStart = week[0];\n            const weekEnd = week[6];\n            const weekEvents = events.filter((event)=>{\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                return eventStart <= weekEnd && eventEnd >= weekStart;\n            });\n            const spanningEvents = [];\n            weekEvents.forEach((event)=>{\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                const startDayIndex = week.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(day, eventStart));\n                const endDayIndex = week.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(day, eventEnd));\n                const eventSpansWeek = startDayIndex !== -1 || endDayIndex !== -1 || eventStart < weekStart && eventEnd > weekEnd;\n                if (eventSpansWeek) {\n                    const start = startDayIndex !== -1 ? startDayIndex : 0;\n                    const end = endDayIndex !== -1 ? endDayIndex : 6;\n                    // Check if this event already has an assigned row from a previous week\n                    const existingRow = eventOccupiedRows.get(event.id);\n                    spanningEvents.push({\n                        event,\n                        startDayIndex: start,\n                        endDayIndex: end,\n                        colSpan: end - start + 1,\n                        preferredRow: existingRow // Pass the preferred row if it exists\n                    });\n                }\n            });\n            const positioned = [];\n            const rows = [];\n            // Sort events: Multi-day events first, then by start date, then by duration\n            const sortedEvents = spanningEvents.sort((a, b)=>{\n                // Multi-day events take precedence\n                const aIsMultiDay = a.colSpan > 1;\n                const bIsMultiDay = b.colSpan > 1;\n                if (aIsMultiDay !== bIsMultiDay) return bIsMultiDay ? 1 : -1;\n                // Then sort by start position\n                if (a.startDayIndex !== b.startDayIndex) return a.startDayIndex - b.startDayIndex;\n                // Then by duration (longer events first)\n                return b.colSpan - a.colSpan;\n            });\n            // First pass: Place events with preferred rows\n            sortedEvents.forEach((event)=>{\n                if (event.preferredRow !== undefined) {\n                    // Ensure the preferred row exists\n                    while(rows.length <= event.preferredRow){\n                        rows.push([]);\n                    }\n                    // Only use preferred row if it's available\n                    if (!rows[event.preferredRow].some((e)=>event.startDayIndex <= e.endDayIndex && event.endDayIndex >= e.startDayIndex)) {\n                        rows[event.preferredRow].push(event);\n                        positioned.push({\n                            ...event,\n                            row: event.preferredRow\n                        });\n                        return;\n                    }\n                }\n            });\n            // Second pass: Place remaining events\n            sortedEvents.forEach((event)=>{\n                if (positioned.some((p)=>p.event.id === event.event.id)) return;\n                let assigned = false;\n                for(let i = 0; i < rows.length; i++){\n                    const row = rows[i];\n                    if (!row.some((e)=>event.startDayIndex <= e.endDayIndex && event.endDayIndex >= e.startDayIndex)) {\n                        row.push(event);\n                        positioned.push({\n                            ...event,\n                            row: i\n                        });\n                        eventOccupiedRows.set(event.event.id, i); // Remember this row for future weeks\n                        assigned = true;\n                        break;\n                    }\n                }\n                if (!assigned) {\n                    rows.push([\n                        event\n                    ]);\n                    const newRow = rows.length - 1;\n                    positioned.push({\n                        ...event,\n                        row: newRow\n                    });\n                    eventOccupiedRows.set(event.event.id, newRow);\n                }\n            });\n            positionedEventsByWeek.set(weekIndex, positioned);\n        });\n        return positionedEventsByWeek;\n    }, [\n        weeks,\n        events\n    ]);\n};\n_s1(useMonthEvents, \"nwk+m61qLgjDVUp4IGV/072DDN4=\");\n// Modify the renderDayCellContent function\nconst renderDayCellContent = (day, dayEvents, weekEvents)=>{\n    const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n    const isCurrentDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(day);\n    const MAX_VISIBLE_EVENTS = 4;\n    const ROW_HEIGHT = 28;\n    // Get all events that affect this day's space\n    const relevantEvents = weekEvents.filter((pe)=>pe.startDayIndex <= day.getDay() && pe.endDayIndex >= day.getDay());\n    // Sort events by their row number to maintain consistent order\n    const sortedEvents = relevantEvents.sort((a, b)=>a.row - b.row);\n    // All events (multi-day and single-day) count towards the MAX_VISIBLE_EVENTS limit\n    const singleDayEvents = dayEvents.filter((e)=>!sortedEvents.some((se)=>se.event.id === e.event.id));\n    // Calculate how many slots are already used by multi-day events\n    const slotsUsedByMultiDayEvents = sortedEvents.length;\n    const remainingSlots = Math.max(0, MAX_VISIBLE_EVENTS - slotsUsedByMultiDayEvents);\n    // Get single-day events that can fit in remaining slots\n    const visibleSingleDayEvents = singleDayEvents.slice(0, remainingSlots);\n    // Combine multi-day events and visible single-day events\n    const visibleEvents = [\n        ...sortedEvents,\n        ...visibleSingleDayEvents\n    ];\n    // Calculate if we need to show \"more\" indicator\n    // We show \"more\" if either:\n    // 1. There are more single day events than remaining slots\n    // 2. The total number of events (multi-day + single-day) exceeds MAX_VISIBLE_EVENTS\n    const totalEvents = sortedEvents.length + singleDayEvents.length;\n    const hasMore = totalEvents > MAX_VISIBLE_EVENTS;\n    // Calculate container height based on actual content\n    const containerHeight = hasMore ? MAX_VISIBLE_EVENTS * ROW_HEIGHT + 20 // Add space for \"+more\" button\n     : Math.min(MAX_VISIBLE_EVENTS, visibleEvents.length) * ROW_HEIGHT;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center rounded-full font-semibold text-xs w-6 h-6\", isCurrentDay ? \"bg-black text-white\" : isCurrentMonth ? \"text-black hover:bg-neutral-100\" : \"text-neutral-400\"),\n                        children: (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(day, \"d\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, undefined),\n                    canEditData && isCurrentMonth && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"ghost\",\n                        className: \"rounded-full h-6 w-6 p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-neutral-100 hidden lg:inline-flex\",\n                        onClick: (e)=>{\n                            e.stopPropagation();\n                            setTimeout(()=>openAddEventForm(day), 150);\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"h-3 w-3 text-black\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                style: {\n                    height: \"\".concat(containerHeight, \"px\")\n                },\n                children: [\n                    visibleEvents.map((pe, index)=>{\n                        var _activeDragData_payload, _activeDragData;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute\",\n                            style: {\n                                top: \"\".concat((pe.row || index) * ROW_HEIGHT, \"px\"),\n                                left: \"2px\",\n                                width: pe.colSpan > 1 ? \"calc(\".concat(pe.colSpan * 100, \"% + \").concat((pe.colSpan - 1) * 19, \"px)\") : \"calc(100% - 4px)\",\n                                zIndex: pe.colSpan > 1 ? 2 : 1 // Multi-day events appear above single-day events\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__.CalendarEventItem, {\n                                event: pe.event,\n                                onClick: (e)=>{\n                                    e.stopPropagation();\n                                    setSelectedEvent(pe.event.id);\n                                    handleEventClick(pe.event);\n                                },\n                                view: \"month\",\n                                isDragging: ((_activeDragData = activeDragData) === null || _activeDragData === void 0 ? void 0 : (_activeDragData_payload = _activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === pe.event.id\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, undefined)\n                        }, pe.event.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 11\n                        }, undefined);\n                    }),\n                    hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute left-0 right-0\",\n                        style: {\n                            top: \"\".concat(MAX_VISIBLE_EVENTS * ROW_HEIGHT, \"px\")\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            className: \"text-xs text-neutral-600 hover:text-neutral-900\",\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                            // TODO: Show all events for this day\n                            },\n                            children: [\n                                \"+\",\n                                singleDayEvents.length - remainingSlots,\n                                \" more\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 237,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\nconst MonthView = (param)=>{\n    let { selectedDate: selectedDate1, events, selectedEvent, setSelectedEvent: setSelectedEvent1, setSelectedDate, openAddEventForm: openAddEventForm1, canEditData: canEditData1, handleEventClick: handleEventClick1, activeDragData: activeDragData1 } = param;\n    _s2();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_5__.useMaybeRecord)();\n    const isInRecordTab = !!maybeRecord;\n    // Memoize month calculations\n    const monthCalculations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const monthStart = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(selectedDate1);\n        const monthEnd = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(selectedDate1);\n        const startDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(monthStart, {\n            weekStartsOn: 0\n        });\n        const endDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(monthEnd, {\n            weekStartsOn: 0\n        });\n        const days = [];\n        let day = startDay;\n        while(day <= endDay){\n            days.push(day);\n            day = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_18__[\"default\"])(day, 1);\n        }\n        const weeks = [];\n        for(let i = 0; i < days.length; i += 7){\n            weeks.push(days.slice(i, i + 7));\n        }\n        return {\n            monthStart,\n            monthEnd,\n            startDay,\n            endDay,\n            days,\n            weeks\n        };\n    }, [\n        selectedDate1\n    ]);\n    // Memoize month events\n    const monthEvents = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>events.filter((event)=>{\n            const eventStart = new Date(event.start);\n            return eventStart >= monthCalculations.startDay && eventStart <= monthCalculations.endDay;\n        }), [\n        events,\n        monthCalculations.startDay,\n        monthCalculations.endDay\n    ]);\n    const positionedEventsByWeek = useMonthEvents(monthCalculations.weeks, events);\n    // Render empty state when no events\n    const renderEmptyState = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-7 border-b border-neutral-300 bg-white\",\n                    children: [\n                        \"Sunday\",\n                        \"Monday\",\n                        \"Tuesday\",\n                        \"Wednesday\",\n                        \"Thursday\",\n                        \"Friday\",\n                        \"Saturday\"\n                    ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-black\", \"py-2 text-xs\"),\n                            children: dayName.substring(0, 3)\n                        }, dayName, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 11\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 340,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NoEvents__WEBPACK_IMPORTED_MODULE_7__.NoEvents, {\n                    title: \"No events this month\",\n                    message: \"\".concat((0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate1, \"MMMM yyyy\"), \" is completely free. Start planning your month!\"),\n                    showCreateButton: canEditData1,\n                    onCreate: ()=>openAddEventForm1(selectedDate1)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 351,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n            lineNumber: 339,\n            columnNumber: 5\n        }, undefined);\n    // Render day cell content\n    const renderDayCellContent = (day, dayEvents, weekEvents)=>{\n        const isCurrentMonth = day.getMonth() === selectedDate1.getMonth();\n        const isCurrentDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(day);\n        const MAX_VISIBLE_EVENTS = 4;\n        const ROW_HEIGHT = 28;\n        // Get all events that affect this day's space\n        const relevantEvents = weekEvents.filter((pe)=>pe.startDayIndex <= day.getDay() && pe.endDayIndex >= day.getDay());\n        // Sort events by their row number to maintain consistent order\n        const sortedEvents = relevantEvents.sort((a, b)=>a.row - b.row);\n        // All events (multi-day and single-day) count towards the MAX_VISIBLE_EVENTS limit\n        const singleDayEvents = dayEvents.filter((e)=>!sortedEvents.some((se)=>se.event.id === e.event.id));\n        // Calculate how many slots are already used by multi-day events\n        const slotsUsedByMultiDayEvents = sortedEvents.length;\n        const remainingSlots = Math.max(0, MAX_VISIBLE_EVENTS - slotsUsedByMultiDayEvents);\n        // Get single-day events that can fit in remaining slots\n        const visibleSingleDayEvents = singleDayEvents.slice(0, remainingSlots);\n        // Combine multi-day events and visible single-day events\n        const visibleEvents = [\n            ...sortedEvents,\n            ...visibleSingleDayEvents\n        ];\n        // Calculate if we need to show \"more\" indicator\n        // We show \"more\" if either:\n        // 1. There are more single day events than remaining slots\n        // 2. The total number of events (multi-day + single-day) exceeds MAX_VISIBLE_EVENTS\n        const totalEvents = sortedEvents.length + singleDayEvents.length;\n        const hasMore = totalEvents > MAX_VISIBLE_EVENTS;\n        // Calculate container height based on actual content\n        const containerHeight = hasMore ? MAX_VISIBLE_EVENTS * ROW_HEIGHT + 20 // Add space for \"+more\" button\n         : Math.min(MAX_VISIBLE_EVENTS, visibleEvents.length) * ROW_HEIGHT;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center rounded-full font-semibold text-xs w-6 h-6\", isCurrentDay ? \"bg-black text-white\" : isCurrentMonth ? \"text-black hover:bg-neutral-100\" : \"text-neutral-400\"),\n                            children: (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(day, \"d\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 403,\n                            columnNumber: 11\n                        }, undefined),\n                        canEditData1 && isCurrentMonth && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            className: \"rounded-full h-6 w-6 p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-neutral-100 hidden lg:inline-flex\",\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                setTimeout(()=>openAddEventForm1(day), 150);\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-3 w-3 text-black\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 418,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 410,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 402,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    style: {\n                        height: \"\".concat(containerHeight, \"px\")\n                    },\n                    children: [\n                        visibleEvents.map((pe, index)=>{\n                            var _activeDragData_payload;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute\",\n                                style: {\n                                    top: \"\".concat((pe.row || index) * ROW_HEIGHT, \"px\"),\n                                    left: \"2px\",\n                                    width: pe.colSpan > 1 ? \"calc(\".concat(pe.colSpan * 100, \"% + \").concat((pe.colSpan - 1) * 19, \"px)\") : \"calc(100% - 4px)\",\n                                    zIndex: pe.colSpan > 1 ? 2 : 1 // Multi-day events appear above single-day events\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__.CalendarEventItem, {\n                                    event: pe.event,\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        setSelectedEvent1(pe.event.id);\n                                        handleEventClick1(pe.event);\n                                    },\n                                    view: \"month\",\n                                    isDragging: (activeDragData1 === null || activeDragData1 === void 0 ? void 0 : (_activeDragData_payload = activeDragData1.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === pe.event.id\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                    lineNumber: 437,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, pe.event.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 13\n                            }, undefined);\n                        }),\n                        hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute left-0 right-0\",\n                            style: {\n                                top: \"\".concat(MAX_VISIBLE_EVENTS * ROW_HEIGHT, \"px\")\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                className: \"text-xs text-neutral-600 hover:text-neutral-900\",\n                                onClick: (e)=>{\n                                    e.stopPropagation();\n                                // TODO: Show all events for this day\n                                },\n                                children: [\n                                    \"+\",\n                                    singleDayEvents.length - remainingSlots,\n                                    \" more\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 457,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 451,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 423,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true);\n    };\n    // Render main view\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-background flex flex-col lg:flex-row\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col min-h-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        \"data-day-headers\": \"true\",\n                        className: \"grid grid-cols-7 border-b border-neutral-300 sticky top-0 z-10 bg-white\",\n                        children: [\n                            \"Sunday\",\n                            \"Monday\",\n                            \"Tuesday\",\n                            \"Wednesday\",\n                            \"Thursday\",\n                            \"Friday\",\n                            \"Saturday\"\n                        ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-black\", \"py-2 text-xs\"),\n                                children: dayName.substring(0, 3)\n                            }, dayName, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 484,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 479,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n                        className: \"flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-7 border-neutral-300 border-b\",\n                            children: monthCalculations.weeks.map((week, weekIndex)=>week.map((day, dayIndex)=>{\n                                    const weekEvents = positionedEventsByWeek.get(weekIndex) || [];\n                                    const dayEvents = weekEvents.filter((pe)=>pe.startDayIndex === dayIndex);\n                                    const isCurrentMonth = day.getMonth() === selectedDate1.getMonth();\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DayCell, {\n                                        date: day,\n                                        isCurrentMonth: isCurrentMonth,\n                                        onClick: ()=>setSelectedDate(day),\n                                        children: renderDayCellContent(day, dayEvents, weekEvents)\n                                    }, \"\".concat(weekIndex, \"-\").concat(dayIndex), false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                        lineNumber: 503,\n                                        columnNumber: 19\n                                    }, undefined);\n                                }))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 495,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 494,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 477,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarSideCard__WEBPACK_IMPORTED_MODULE_8__.CalendarSideCard, {\n                selectedDate: selectedDate1,\n                events: events,\n                selectedEvent: selectedEvent,\n                setSelectedEvent: setSelectedEvent1,\n                handleEventClick: handleEventClick1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 518,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 476,\n        columnNumber: 5\n    }, undefined);\n};\n_s2(MonthView, \"LfpiC9GNlfiXFS91dl5Hp92L/ME=\", false, function() {\n    return [\n        _providers_record__WEBPACK_IMPORTED_MODULE_5__.useMaybeRecord,\n        useMonthEvents\n    ];\n});\n_c1 = MonthView;\nfunction isMultiDay(event) {\n    return !(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(new Date(event.start), new Date(event.end));\n}\nvar _c, _c1;\n$RefreshReg$(_c, \"DayCell\");\n$RefreshReg$(_c1, \"MonthView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx\n"));

/***/ })

});