"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx":
/*!*******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/MonthView.tsx ***!
  \*******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MonthView: function() { return /* binding */ MonthView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CalendarEventItem */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx\");\n/* harmony import */ var _NoEvents__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./NoEvents */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/NoEvents.tsx\");\n/* harmony import */ var _CalendarSideCard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./CalendarSideCard */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarSideCard.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst DayCell = (param)=>{\n    let { date, children, onClick, isCurrentMonth } = param;\n    _s();\n    const { setNodeRef, isOver } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__.useDroppable)({\n        id: \"daycell-\".concat((0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(date, \"yyyy-MM-dd\")),\n        data: {\n            date: date,\n            type: \"daycell\"\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: setNodeRef,\n        onClick: onClick,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-b border-r border-neutral-300 relative cursor-pointer transition-colors group flex flex-col p-2 min-h-[120px] lg:p-3 lg:min-h-[140px]\", isCurrentMonth ? \"bg-white hover:bg-neutral-50\" : \"bg-neutral-100 hover:bg-neutral-200\", isOver && \"bg-blue-50 border-blue-200\"),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DayCell, \"DmJTTt6A5xWIX/faBiFge3FOLrw=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__.useDroppable\n    ];\n});\n_c = DayCell;\n// New helper function to process events for the entire month\nconst useMonthEvents = (weeks, events)=>{\n    _s1();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const positionedEventsByWeek = new Map();\n        const eventOccupiedRows = new Map(); // Track which row each event occupies\n        weeks.forEach((week, weekIndex)=>{\n            const weekStart = week[0];\n            const weekEnd = week[6];\n            const weekEvents = events.filter((event)=>{\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                return eventStart <= weekEnd && eventEnd >= weekStart;\n            });\n            const spanningEvents = [];\n            weekEvents.forEach((event)=>{\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                const startDayIndex = week.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(day, eventStart));\n                const endDayIndex = week.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(day, eventEnd));\n                const eventSpansWeek = startDayIndex !== -1 || endDayIndex !== -1 || eventStart < weekStart && eventEnd > weekEnd;\n                if (eventSpansWeek) {\n                    const start = startDayIndex !== -1 ? startDayIndex : 0;\n                    const end = endDayIndex !== -1 ? endDayIndex : 6;\n                    // Check if this event already has an assigned row from a previous week\n                    const existingRow = eventOccupiedRows.get(event.id);\n                    spanningEvents.push({\n                        event,\n                        startDayIndex: start,\n                        endDayIndex: end,\n                        colSpan: end - start + 1,\n                        preferredRow: existingRow // Pass the preferred row if it exists\n                    });\n                }\n            });\n            const positioned = [];\n            const rows = [];\n            // Sort events: Multi-day events first, then by start date, then by duration\n            const sortedEvents = spanningEvents.sort((a, b)=>{\n                // Multi-day events take precedence\n                const aIsMultiDay = a.colSpan > 1;\n                const bIsMultiDay = b.colSpan > 1;\n                if (aIsMultiDay !== bIsMultiDay) return bIsMultiDay ? 1 : -1;\n                // Then sort by start position\n                if (a.startDayIndex !== b.startDayIndex) return a.startDayIndex - b.startDayIndex;\n                // Then by duration (longer events first)\n                return b.colSpan - a.colSpan;\n            });\n            // First pass: Place events with preferred rows\n            sortedEvents.forEach((event)=>{\n                if (event.preferredRow !== undefined) {\n                    // Ensure the preferred row exists\n                    while(rows.length <= event.preferredRow){\n                        rows.push([]);\n                    }\n                    // Only use preferred row if it's available\n                    if (!rows[event.preferredRow].some((e)=>event.startDayIndex <= e.endDayIndex && event.endDayIndex >= e.startDayIndex)) {\n                        rows[event.preferredRow].push(event);\n                        positioned.push({\n                            ...event,\n                            row: event.preferredRow\n                        });\n                        return;\n                    }\n                }\n            });\n            // Second pass: Place remaining events\n            sortedEvents.forEach((event)=>{\n                if (positioned.some((p)=>p.event.id === event.event.id)) return;\n                let assigned = false;\n                for(let i = 0; i < rows.length; i++){\n                    const row = rows[i];\n                    if (!row.some((e)=>event.startDayIndex <= e.endDayIndex && event.endDayIndex >= e.startDayIndex)) {\n                        row.push(event);\n                        positioned.push({\n                            ...event,\n                            row: i\n                        });\n                        eventOccupiedRows.set(event.event.id, i); // Remember this row for future weeks\n                        assigned = true;\n                        break;\n                    }\n                }\n                if (!assigned) {\n                    rows.push([\n                        event\n                    ]);\n                    const newRow = rows.length - 1;\n                    positioned.push({\n                        ...event,\n                        row: newRow\n                    });\n                    eventOccupiedRows.set(event.event.id, newRow);\n                }\n            });\n            positionedEventsByWeek.set(weekIndex, positioned);\n        });\n        return positionedEventsByWeek;\n    }, [\n        weeks,\n        events\n    ]);\n};\n_s1(useMonthEvents, \"nwk+m61qLgjDVUp4IGV/072DDN4=\");\n// Modify the renderDayCellContent function\nconst renderDayCellContent = (day, dayEvents, weekEvents)=>{\n    const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n    const isCurrentDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(day);\n    const MAX_VISIBLE_EVENTS = 4;\n    const ROW_HEIGHT = 28;\n    // Get ALL events that pass through this day (both multi-day and single-day)\n    const allEventsInCell = [\n        // Multi-day events from the week that pass through this day\n        ...weekEvents.filter((pe)=>pe.startDayIndex <= day.getDay() && pe.endDayIndex >= day.getDay()),\n        // Single-day events for this specific day that aren't already counted\n        ...dayEvents.filter((e)=>!weekEvents.some((we)=>we.event.id === e.event.id))\n    ];\n    // Sort all events by priority (multi-day events first, then by row)\n    const sortedEvents = allEventsInCell.sort((a, b)=>{\n        // Multi-day events take precedence\n        const aIsMultiDay = a.colSpan > 1 || a.event && a.event.end > a.event.start;\n        const bIsMultiDay = b.colSpan > 1 || b.event && b.event.end > b.event.start;\n        if (aIsMultiDay !== bIsMultiDay) return bIsMultiDay ? -1 : 1;\n        // Then sort by row number if available\n        return (a.row || 0) - (b.row || 0);\n    });\n    // Take only the first MAX_VISIBLE_EVENTS\n    const visibleEvents = sortedEvents.slice(0, MAX_VISIBLE_EVENTS);\n    // Calculate if we need to show \"more\" indicator\n    const hasMore = sortedEvents.length > MAX_VISIBLE_EVENTS;\n    const moreCount = sortedEvents.length - MAX_VISIBLE_EVENTS;\n    // Calculate container height\n    const containerHeight = Math.min(MAX_VISIBLE_EVENTS, visibleEvents.length) * ROW_HEIGHT + (hasMore ? 20 : 0);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center rounded-full font-semibold text-xs w-6 h-6\", isCurrentDay ? \"bg-black text-white\" : isCurrentMonth ? \"text-black hover:bg-neutral-100\" : \"text-neutral-400\"),\n                        children: (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(day, \"d\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, undefined),\n                    canEditData && isCurrentMonth && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"ghost\",\n                        className: \"rounded-full h-6 w-6 p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-neutral-100 hidden lg:inline-flex\",\n                        onClick: (e)=>{\n                            e.stopPropagation();\n                            setTimeout(()=>openAddEventForm(day), 150);\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"h-3 w-3 text-black\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                style: {\n                    height: \"\".concat(containerHeight, \"px\")\n                },\n                children: [\n                    visibleEvents.map((event, index)=>{\n                        var _activeDragData_payload, _activeDragData;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute w-full\",\n                            style: {\n                                top: \"\".concat(index * ROW_HEIGHT, \"px\"),\n                                left: \"2px\",\n                                width: event.colSpan > 1 ? \"calc(\".concat(event.colSpan * 100, \"% + \").concat((event.colSpan - 1) * 19, \"px)\") : \"calc(100% - 4px)\",\n                                zIndex: event.colSpan > 1 ? 2 : 1\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__.CalendarEventItem, {\n                                event: event.event,\n                                onClick: (e)=>{\n                                    e.stopPropagation();\n                                    setSelectedEvent(event.event.id);\n                                    handleEventClick(event.event);\n                                },\n                                view: \"month\",\n                                isDragging: ((_activeDragData = activeDragData) === null || _activeDragData === void 0 ? void 0 : (_activeDragData_payload = _activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === event.event.id\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, undefined)\n                        }, event.event.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 11\n                        }, undefined);\n                    }),\n                    hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute left-0 right-0\",\n                        style: {\n                            top: \"\".concat(MAX_VISIBLE_EVENTS * ROW_HEIGHT, \"px\")\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            className: \"text-xs text-neutral-600 hover:text-neutral-900\",\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                            // TODO: Show all events for this day\n                            },\n                            children: [\n                                \"+\",\n                                moreCount,\n                                \" more\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 237,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\nconst MonthView = (param)=>{\n    let { selectedDate: selectedDate1, events, selectedEvent, setSelectedEvent: setSelectedEvent1, setSelectedDate, openAddEventForm: openAddEventForm1, canEditData: canEditData1, handleEventClick: handleEventClick1, activeDragData: activeDragData1 } = param;\n    _s2();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_5__.useMaybeRecord)();\n    const isInRecordTab = !!maybeRecord;\n    // Memoize month calculations\n    const monthCalculations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const monthStart = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(selectedDate1);\n        const monthEnd = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(selectedDate1);\n        const startDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(monthStart, {\n            weekStartsOn: 0\n        });\n        const endDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(monthEnd, {\n            weekStartsOn: 0\n        });\n        const days = [];\n        let day = startDay;\n        while(day <= endDay){\n            days.push(day);\n            day = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_18__[\"default\"])(day, 1);\n        }\n        const weeks = [];\n        for(let i = 0; i < days.length; i += 7){\n            weeks.push(days.slice(i, i + 7));\n        }\n        return {\n            monthStart,\n            monthEnd,\n            startDay,\n            endDay,\n            days,\n            weeks\n        };\n    }, [\n        selectedDate1\n    ]);\n    // Memoize month events\n    const monthEvents = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>events.filter((event)=>{\n            const eventStart = new Date(event.start);\n            return eventStart >= monthCalculations.startDay && eventStart <= monthCalculations.endDay;\n        }), [\n        events,\n        monthCalculations.startDay,\n        monthCalculations.endDay\n    ]);\n    const positionedEventsByWeek = useMonthEvents(monthCalculations.weeks, events);\n    // Render empty state when no events\n    const renderEmptyState = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-7 border-b border-neutral-300 bg-white\",\n                    children: [\n                        \"Sunday\",\n                        \"Monday\",\n                        \"Tuesday\",\n                        \"Wednesday\",\n                        \"Thursday\",\n                        \"Friday\",\n                        \"Saturday\"\n                    ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-black\", \"py-2 text-xs\"),\n                            children: dayName.substring(0, 3)\n                        }, dayName, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 11\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 340,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NoEvents__WEBPACK_IMPORTED_MODULE_7__.NoEvents, {\n                    title: \"No events this month\",\n                    message: \"\".concat((0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate1, \"MMMM yyyy\"), \" is completely free. Start planning your month!\"),\n                    showCreateButton: canEditData1,\n                    onCreate: ()=>openAddEventForm1(selectedDate1)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 351,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n            lineNumber: 339,\n            columnNumber: 5\n        }, undefined);\n    // Render day cell content\n    const renderDayCellContent = (day, dayEvents, weekEvents)=>{\n        const isCurrentMonth = day.getMonth() === selectedDate1.getMonth();\n        const isCurrentDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(day);\n        const MAX_VISIBLE_EVENTS = 4;\n        const ROW_HEIGHT = 28;\n        // Get ALL events that pass through this day (both multi-day and single-day)\n        const allEventsInCell = [\n            // Multi-day events from the week that pass through this day\n            ...weekEvents.filter((pe)=>pe.startDayIndex <= day.getDay() && pe.endDayIndex >= day.getDay()),\n            // Single-day events for this specific day that aren't already counted\n            ...dayEvents.filter((e)=>!weekEvents.some((we)=>we.event.id === e.event.id))\n        ];\n        // Sort all events by priority (multi-day events first, then by row)\n        const sortedEvents = allEventsInCell.sort((a, b)=>{\n            // Multi-day events take precedence\n            const aIsMultiDay = a.colSpan > 1 || a.event && a.event.end > a.event.start;\n            const bIsMultiDay = b.colSpan > 1 || b.event && b.event.end > b.event.start;\n            if (aIsMultiDay !== bIsMultiDay) return bIsMultiDay ? -1 : 1;\n            // Then sort by row number if available\n            return (a.row || 0) - (b.row || 0);\n        });\n        // Take only the first MAX_VISIBLE_EVENTS\n        const visibleEvents = sortedEvents.slice(0, MAX_VISIBLE_EVENTS);\n        // Calculate if we need to show \"more\" indicator\n        const hasMore = sortedEvents.length > MAX_VISIBLE_EVENTS;\n        const moreCount = sortedEvents.length - MAX_VISIBLE_EVENTS;\n        // Calculate container height\n        const containerHeight = Math.min(MAX_VISIBLE_EVENTS, visibleEvents.length) * ROW_HEIGHT + (hasMore ? 20 : 0);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center rounded-full font-semibold text-xs w-6 h-6\", isCurrentDay ? \"bg-black text-white\" : isCurrentMonth ? \"text-black hover:bg-neutral-100\" : \"text-neutral-400\"),\n                            children: (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(day, \"d\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 403,\n                            columnNumber: 11\n                        }, undefined),\n                        canEditData1 && isCurrentMonth && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            className: \"rounded-full h-6 w-6 p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-neutral-100 hidden lg:inline-flex\",\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                setTimeout(()=>openAddEventForm1(day), 150);\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-3 w-3 text-black\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 418,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 410,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 402,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    style: {\n                        height: \"\".concat(containerHeight, \"px\")\n                    },\n                    children: [\n                        visibleEvents.map((event, index)=>{\n                            var _activeDragData_payload;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute w-full\",\n                                style: {\n                                    top: \"\".concat(index * ROW_HEIGHT, \"px\"),\n                                    left: \"2px\",\n                                    width: event.colSpan > 1 ? \"calc(\".concat(event.colSpan * 100, \"% + \").concat((event.colSpan - 1) * 19, \"px)\") : \"calc(100% - 4px)\",\n                                    zIndex: event.colSpan > 1 ? 2 : 1\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__.CalendarEventItem, {\n                                    event: event.event,\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        setSelectedEvent1(event.event.id);\n                                        handleEventClick1(event.event);\n                                    },\n                                    view: \"month\",\n                                    isDragging: (activeDragData1 === null || activeDragData1 === void 0 ? void 0 : (_activeDragData_payload = activeDragData1.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === event.event.id\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                    lineNumber: 437,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, event.event.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 13\n                            }, undefined);\n                        }),\n                        hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute left-0 right-0\",\n                            style: {\n                                top: \"\".concat(MAX_VISIBLE_EVENTS * ROW_HEIGHT, \"px\")\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                className: \"text-xs text-neutral-600 hover:text-neutral-900\",\n                                onClick: (e)=>{\n                                    e.stopPropagation();\n                                // TODO: Show all events for this day\n                                },\n                                children: [\n                                    \"+\",\n                                    moreCount,\n                                    \" more\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 457,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 451,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 423,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true);\n    };\n    // Render main view\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-background flex flex-col lg:flex-row\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col min-h-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        \"data-day-headers\": \"true\",\n                        className: \"grid grid-cols-7 border-b border-neutral-300 sticky top-0 z-10 bg-white\",\n                        children: [\n                            \"Sunday\",\n                            \"Monday\",\n                            \"Tuesday\",\n                            \"Wednesday\",\n                            \"Thursday\",\n                            \"Friday\",\n                            \"Saturday\"\n                        ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-black\", \"py-2 text-xs\"),\n                                children: dayName.substring(0, 3)\n                            }, dayName, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 484,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 479,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n                        className: \"flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-7 border-neutral-300 border-b\",\n                            children: monthCalculations.weeks.map((week, weekIndex)=>week.map((day, dayIndex)=>{\n                                    const weekEvents = positionedEventsByWeek.get(weekIndex) || [];\n                                    const dayEvents = weekEvents.filter((pe)=>pe.startDayIndex === dayIndex);\n                                    const isCurrentMonth = day.getMonth() === selectedDate1.getMonth();\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DayCell, {\n                                        date: day,\n                                        isCurrentMonth: isCurrentMonth,\n                                        onClick: ()=>setSelectedDate(day),\n                                        children: renderDayCellContent(day, dayEvents, weekEvents)\n                                    }, \"\".concat(weekIndex, \"-\").concat(dayIndex), false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                        lineNumber: 503,\n                                        columnNumber: 19\n                                    }, undefined);\n                                }))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 495,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 494,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 477,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarSideCard__WEBPACK_IMPORTED_MODULE_8__.CalendarSideCard, {\n                selectedDate: selectedDate1,\n                events: events,\n                selectedEvent: selectedEvent,\n                setSelectedEvent: setSelectedEvent1,\n                handleEventClick: handleEventClick1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 518,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 476,\n        columnNumber: 5\n    }, undefined);\n};\n_s2(MonthView, \"LfpiC9GNlfiXFS91dl5Hp92L/ME=\", false, function() {\n    return [\n        _providers_record__WEBPACK_IMPORTED_MODULE_5__.useMaybeRecord,\n        useMonthEvents\n    ];\n});\n_c1 = MonthView;\nfunction isMultiDay(event) {\n    return !(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(new Date(event.start), new Date(event.end));\n}\nvar _c, _c1;\n$RefreshReg$(_c, \"DayCell\");\n$RefreshReg$(_c1, \"MonthView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx\n"));

/***/ })

});