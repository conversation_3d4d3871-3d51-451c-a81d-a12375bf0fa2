"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/DayView.tsx":
/*!*****************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/DayView.tsx ***!
  \*****************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DayView: function() { return /* binding */ DayView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=format,isToday,setHours!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=format,isToday,setHours!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=format,isToday,setHours!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/setHours/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CalendarEventSegment */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx\");\n/* harmony import */ var _NoEvents__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./NoEvents */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/NoEvents.tsx\");\n/* harmony import */ var _utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/multiDayEventUtils */ \"(app-pages-browser)/./src/utils/multiDayEventUtils.ts\");\n/* harmony import */ var _utils_eventCollisionUtils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/eventCollisionUtils */ \"(app-pages-browser)/./src/utils/eventCollisionUtils.ts\");\n/* harmony import */ var _AllDayRow__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./AllDayRow */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/AllDayRow.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst TimeSlot = (param)=>{\n    let { hour, date, onDoubleClick, children } = param;\n    // Create 60 minute segments for precise dropping\n    const minuteSegments = Array.from({\n        length: 60\n    }, (_, i)=>i);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-1 relative min-h-[60px] cursor-pointer\"),\n        style: {\n            height: \"60px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex flex-col\",\n                children: minuteSegments.map((minute)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MinuteSegment, {\n                        date: date,\n                        hour: hour,\n                        minute: minute,\n                        style: {\n                            height: \"\".concat(100 / 60, \"%\"),\n                            top: \"\".concat(minute / 60 * 100, \"%\")\n                        },\n                        onDoubleClick: ()=>onDoubleClick(minute)\n                    }, minute, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, undefined);\n};\n_c = TimeSlot;\n// New component for minute-level droppable segments\nconst MinuteSegment = (param)=>{\n    let { date, hour, minute, style, onDoubleClick } = param;\n    _s();\n    const { setNodeRef, isOver } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__.useDroppable)({\n        id: \"timeslot-\".concat((0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(date, \"yyyy-MM-dd\"), \"-\").concat(hour, \"-\").concat(minute),\n        data: {\n            date: date,\n            hour,\n            minute,\n            type: \"timeslot-minute\"\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: setNodeRef,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"absolute w-full\", isOver && \"bg-blue-50\"),\n        style: style,\n        onDoubleClick: onDoubleClick\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n        lineNumber: 99,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MinuteSegment, \"DmJTTt6A5xWIX/faBiFge3FOLrw=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__.useDroppable\n    ];\n});\n_c1 = MinuteSegment;\nconst DayView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, openAddEventForm, canEditData, savedScrollTop, handleEventClick, activeDragData } = param;\n    _s1();\n    const hours = Array.from({\n        length: 24\n    }, (_, i)=>i);\n    // Memoize event segments to prevent unnecessary recalculations\n    const daySegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const allSegments = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.eventsToSegments)(events);\n        return (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.getSegmentsForDay)(allSegments, selectedDate);\n    }, [\n        events,\n        selectedDate\n    ]);\n    // Separate all-day and time-slot segments\n    const allDaySegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.getAllDaySegments)(daySegments), [\n        daySegments\n    ]);\n    const timeSlotSegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.getTimeSlotSegments)(daySegments), [\n        daySegments\n    ]);\n    // Calculate layout for overlapping segments\n    const { segmentLayouts } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return (0,_utils_eventCollisionUtils__WEBPACK_IMPORTED_MODULE_6__.calculateLayout)(timeSlotSegments);\n    }, [\n        timeSlotSegments\n    ]);\n    // Memoize current time position\n    const currentTimePosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate) ? {\n            hour: new Date().getHours(),\n            minutes: new Date().getMinutes()\n        } : null, [\n        selectedDate\n    ]);\n    // Render empty state when no events\n    const renderEmptyState = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NoEvents__WEBPACK_IMPORTED_MODULE_4__.NoEvents, {\n            title: \"No events scheduled\",\n            message: (0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate) ? \"You have a free day ahead! Add an event to get started.\" : \"\".concat((0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(selectedDate, \"EEEE, MMMM d\"), \" is completely free.\"),\n            showCreateButton: canEditData,\n            onCreate: ()=>{\n                const newDate = new Date(selectedDate);\n                newDate.setHours(9, 0, 0, 0);\n                openAddEventForm(newDate);\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n            lineNumber: 152,\n            columnNumber: 5\n        }, undefined);\n    // Render time slots with events\n    const renderTimeSlots = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 overflow-auto relative bg-white\",\n            id: \"day-view-container\",\n            children: [\n                hours.map((hour, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex border-b border-neutral-300 hover:bg-neutral-50 transition-colors\", i === hours.length - 1 && \"border-b-neutral-300\"),\n                        style: {\n                            height: \"60px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                \"data-time-labels\": \"true\",\n                                className: \"flex items-start justify-end pr-4 pt-2 text-xs font-medium text-black border-r border-neutral-300 sticky left-0 bg-white z-10 w-14 lg:w-20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs font-semibold\",\n                                            children: (0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(selectedDate, hour), \"h\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-black opacity-60\",\n                                            children: (0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(selectedDate, hour), \"a\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TimeSlot, {\n                                hour: hour,\n                                date: selectedDate,\n                                onDoubleClick: (minute)=>{\n                                    if (canEditData) {\n                                        const newDate = new Date(selectedDate);\n                                        newDate.setHours(hour, minute, 0, 0);\n                                        openAddEventForm(newDate);\n                                    }\n                                },\n                                children: segmentLayouts.map((layout)=>{\n                                    var _activeDragData_payload;\n                                    const segmentStart = layout.segment.startTime;\n                                    const isFirstHour = segmentStart.getHours() === hour;\n                                    if (!isFirstHour) return null;\n                                    const segmentHeight = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.getSegmentHeight)(layout.segment);\n                                    const topOffset = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.getSegmentTopOffset)(layout.segment);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__.CalendarEventSegment, {\n                                        segment: layout.segment,\n                                        style: {\n                                            height: \"\".concat(segmentHeight, \"px\"),\n                                            position: \"absolute\",\n                                            top: \"\".concat(topOffset, \"px\"),\n                                            left: \"\".concat(layout.left, \"%\"),\n                                            width: \"\".concat(layout.width, \"%\"),\n                                            zIndex: selectedEvent === layout.segment.originalEventId ? 20 : layout.zIndex,\n                                            paddingRight: \"2px\",\n                                            border: layout.hasOverlap ? \"1px solid white\" : \"none\"\n                                        },\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            const container = document.getElementById(\"day-view-container\");\n                                            if (container) {\n                                                savedScrollTop.current = container.scrollTop;\n                                            }\n                                            setSelectedEvent(layout.segment.originalEventId);\n                                            handleEventClick(layout.segment.originalEvent);\n                                        },\n                                        view: \"day\",\n                                        isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === layout.segment.id\n                                    }, layout.segment.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 17\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, i, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, undefined)),\n                currentTimePosition && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute flex items-center z-30 pointer-events-none left-14 lg:left-20\",\n                    style: {\n                        top: \"\".concat((currentTimePosition.hour + currentTimePosition.minutes / 60) * 60, \"px\"),\n                        right: \"4px\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-3 h-3 rounded-full bg-red-500 border-2 border-white shadow-lg -ml-1.5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 border-t-2 border-red-500 shadow-sm\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n            lineNumber: 168,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center bg-white border-b border-neutral-300 py-2 px-2 lg:px-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"font-semibold text-black mb-1 text-xs\",\n                        children: (0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(selectedDate, \"EEEE\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center font-medium text-xs w-6 h-6 rounded-full\", (0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate) ? \"bg-black text-white\" : \"text-black hover:bg-neutral-100\"),\n                        children: (0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(selectedDate, \"d\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                lineNumber: 265,\n                columnNumber: 7\n            }, undefined),\n            daySegments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AllDayRow__WEBPACK_IMPORTED_MODULE_7__.AllDayRow, {\n                selectedDate: selectedDate,\n                segments: allDaySegments,\n                selectedEvent: selectedEvent,\n                setSelectedEvent: setSelectedEvent,\n                handleEventClick: handleEventClick,\n                canEditData: canEditData,\n                openAddEventForm: openAddEventForm,\n                view: \"day\",\n                activeDragData: activeDragData\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                lineNumber: 281,\n                columnNumber: 9\n            }, undefined),\n            daySegments.length === 0 ? renderEmptyState() : renderTimeSlots()\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n        lineNumber: 263,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(DayView, \"rKQaW4qzJjohXAHqKTEXOPEYpLE=\");\n_c2 = DayView;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"TimeSlot\");\n$RefreshReg$(_c1, \"MinuteSegment\");\n$RefreshReg$(_c2, \"DayView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/DayView.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/WeekView.tsx":
/*!******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/WeekView.tsx ***!
  \******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WeekView: function() { return /* binding */ WeekView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/setHours/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CalendarEventSegment */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx\");\n/* harmony import */ var _AllDayRow__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./AllDayRow */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/AllDayRow.tsx\");\n/* harmony import */ var _NoEvents__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./NoEvents */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/NoEvents.tsx\");\n/* harmony import */ var _utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/multiDayEventUtils */ \"(app-pages-browser)/./src/utils/multiDayEventUtils.ts\");\n/* harmony import */ var _utils_eventCollisionUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/eventCollisionUtils */ \"(app-pages-browser)/./src/utils/eventCollisionUtils.ts\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst TimeSlot = (param)=>{\n    let { day, hour, children, onDoubleClick } = param;\n    // Create 60 minute segments for precise dropping\n    const minuteSegments = Array.from({\n        length: 60\n    }, (_, i)=>i);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-1 border-r border-neutral-300 last:border-r-0 relative min-h-[60px] cursor-pointer\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex flex-col\",\n                children: minuteSegments.map((minute)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MinuteSegment, {\n                        day: day,\n                        hour: hour,\n                        minute: minute,\n                        style: {\n                            height: \"\".concat(100 / 60, \"%\"),\n                            top: \"\".concat(minute / 60 * 100, \"%\")\n                        },\n                        onDoubleClick: ()=>onDoubleClick(minute)\n                    }, minute, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, undefined);\n};\n_c = TimeSlot;\n// New component for minute-level droppable segments\nconst MinuteSegment = (param)=>{\n    let { day, hour, minute, style, onDoubleClick } = param;\n    _s();\n    const { setNodeRef, isOver } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__.useDroppable)({\n        id: \"timeslot-\".concat((0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day, \"yyyy-MM-dd\"), \"-\").concat(hour, \"-\").concat(minute),\n        data: {\n            date: day,\n            hour,\n            minute,\n            type: \"timeslot-minute\"\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: setNodeRef,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"absolute w-full\", isOver && \"bg-blue-50\"),\n        style: style,\n        onDoubleClick: onDoubleClick\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MinuteSegment, \"DmJTTt6A5xWIX/faBiFge3FOLrw=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__.useDroppable\n    ];\n});\n_c1 = MinuteSegment;\nconst WeekView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, setSelectedDate, openAddEventForm, canEditData, savedScrollTop, handleEventClick, activeDragData } = param;\n    _s1();\n    // Memoize week-related calculations\n    const weekCalculations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const weekStart = (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        });\n        const weekEnd = (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        });\n        const days = Array.from({\n            length: 7\n        }, (_, i)=>(0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(weekStart, i));\n        const todayIndex = days.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(day));\n        return {\n            weekStart,\n            weekEnd,\n            days,\n            todayIndex\n        };\n    }, [\n        selectedDate\n    ]);\n    const { days, todayIndex } = weekCalculations;\n    const hours = Array.from({\n        length: 24\n    }, (_, i)=>i);\n    // Memoize week segments\n    const weekSegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const allSegments = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.eventsToSegments)(events);\n        return (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getSegmentsForWeek)(allSegments, weekCalculations.weekStart, weekCalculations.weekEnd);\n    }, [\n        events,\n        weekCalculations.weekStart,\n        weekCalculations.weekEnd\n    ]);\n    // Separate all-day and time-slot segments\n    const allDaySegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getAllDaySegments)(weekSegments), [\n        weekSegments\n    ]);\n    const timeSlotSegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getTimeSlotSegments)(weekSegments), [\n        weekSegments\n    ]);\n    // Memoize current time position\n    const currentTimePosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>todayIndex !== -1 ? {\n            dayIndex: todayIndex,\n            hour: new Date().getHours(),\n            minutes: new Date().getMinutes()\n        } : null, [\n        todayIndex\n    ]);\n    // Helper to get event duration in minutes\n    const getEventDurationInMinutes = (event)=>{\n        const start = new Date(event.start);\n        const end = new Date(event.end);\n        return Math.max(20, (end.getTime() - start.getTime()) / (1000 * 60));\n    };\n    // Render empty state when no events\n    const renderEmptyState = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NoEvents__WEBPACK_IMPORTED_MODULE_5__.NoEvents, {\n            title: \"No events this week\",\n            message: \"Your week is completely free. Add some events to get organized!\",\n            showCreateButton: canEditData,\n            onCreate: ()=>openAddEventForm(selectedDate)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n            lineNumber: 176,\n            columnNumber: 5\n        }, undefined);\n    // Render time slots with events\n    const renderTimeSlots = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 relative bg-white border-b border-neutral-300 overflow-y-auto lg:overflow-auto\",\n            id: \"week-view-container\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative overflow-x-auto lg:overflow-x-visible\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col min-w-[700px] lg:min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: hours.map((hour, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex border-b border-neutral-300 hover:bg-neutral-50 transition-colors\", i === hours.length - 1 && \"border-b-neutral-300\"),\n                                    style: {\n                                        height: \"60px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            \"data-time-labels\": \"true\",\n                                            className: \"sticky left-0 flex items-start justify-end pr-4 pt-2 text-xs font-medium text-black border-r border-neutral-200 bg-white z-20 w-14 lg:w-20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs font-semibold\",\n                                                    children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(new Date(), hour), \"h a\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        days.map((day)=>{\n                                            const daySegments = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getSegmentsForDay)(timeSlotSegments, day);\n                                            const { segmentLayouts } = (0,_utils_eventCollisionUtils__WEBPACK_IMPORTED_MODULE_7__.calculateLayout)(daySegments);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TimeSlot, {\n                                                day: day,\n                                                hour: hour,\n                                                onDoubleClick: (minute)=>{\n                                                    if (canEditData) {\n                                                        const newDate = new Date(day);\n                                                        newDate.setHours(hour, minute, 0, 0);\n                                                        openAddEventForm(newDate);\n                                                    }\n                                                },\n                                                children: segmentLayouts.map((layout)=>{\n                                                    var _activeDragData_payload;\n                                                    const segmentStart = layout.segment.startTime;\n                                                    const isFirstHour = segmentStart.getHours() === hour;\n                                                    if (!isFirstHour) return null;\n                                                    const segmentHeight = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getSegmentHeight)(layout.segment);\n                                                    const topOffset = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getSegmentTopOffset)(layout.segment);\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__.CalendarEventSegment, {\n                                                        segment: layout.segment,\n                                                        style: {\n                                                            height: \"\".concat(segmentHeight, \"px\"),\n                                                            position: \"absolute\",\n                                                            top: \"\".concat(topOffset, \"px\"),\n                                                            left: \"\".concat(layout.left, \"%\"),\n                                                            width: \"\".concat(layout.width, \"%\"),\n                                                            zIndex: selectedEvent === layout.segment.originalEventId ? 20 : layout.zIndex,\n                                                            paddingRight: \"2px\",\n                                                            border: layout.hasOverlap ? \"1px solid white\" : \"none\"\n                                                        },\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            const container = document.getElementById(\"week-view-container\");\n                                                            if (container) {\n                                                                savedScrollTop.current = container.scrollTop;\n                                                            }\n                                                            setSelectedEvent(layout.segment.originalEventId);\n                                                            handleEventClick(layout.segment.originalEvent);\n                                                        },\n                                                        view: \"week\",\n                                                        isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === layout.segment.id\n                                                    }, layout.segment.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 27\n                                                    }, undefined);\n                                                })\n                                            }, \"\".concat(day.toISOString(), \"-\").concat(hour), false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 21\n                                            }, undefined);\n                                        })\n                                    ]\n                                }, hour, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, undefined),\n                        currentTimePosition && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-0 bottom-0 left-14 lg:left-20 right-0 pointer-events-none z-30\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative h-full w-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute flex items-center\",\n                                    style: {\n                                        top: \"\".concat((currentTimePosition.hour + currentTimePosition.minutes / 60) * 60, \"px\"),\n                                        left: \"\".concat(currentTimePosition.dayIndex / 7 * 100, \"%\"),\n                                        width: \"\".concat(1 / 7 * 100, \"%\")\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 rounded-full bg-red-500 border-2 border-white shadow-lg -ml-1.5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 border-t-2 border-red-500 shadow-sm\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n            lineNumber: 186,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                \"data-day-headers\": \"true\",\n                className: \"border-b border-neutral-300 bg-white sticky top-0 z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex overflow-x-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"sticky left-0 bg-white z-10 w-14 lg:w-20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-1 min-w-[calc(100vw-3.5rem)] lg:min-w-0\",\n                            children: days.map((day, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 text-center cursor-pointer py-3 px-0 lg:py-4\",\n                                    onClick: ()=>setSelectedDate(day),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-semibold text-black mb-1\", \"text-xs\"),\n                                            children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day, \"EEE\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center font-medium text-xs w-6 h-6 rounded-full\", (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(day) ? \"bg-black text-white\" : \"text-black hover:bg-neutral-100\"),\n                                            children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day, \"d\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                    lineNumber: 303,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 299,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AllDayRow__WEBPACK_IMPORTED_MODULE_4__.AllDayRow, {\n                selectedDate: selectedDate,\n                segments: allDaySegments,\n                selectedEvent: selectedEvent,\n                setSelectedEvent: setSelectedEvent,\n                handleEventClick: handleEventClick,\n                canEditData: canEditData,\n                openAddEventForm: openAddEventForm,\n                view: \"week\",\n                activeDragData: activeDragData\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 333,\n                columnNumber: 7\n            }, undefined),\n            weekSegments.length === 0 ? renderEmptyState() : renderTimeSlots()\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n        lineNumber: 297,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(WeekView, \"OfgQ1j/ZHWAQ0Q+mdykk0ntfinw=\");\n_c2 = WeekView;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"TimeSlot\");\n$RefreshReg$(_c1, \"MinuteSegment\");\n$RefreshReg$(_c2, \"WeekView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/WeekView.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/index.tsx":
/*!****************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/index.tsx ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CalendarView: function() { return /* binding */ CalendarView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! opendb-app-db-utils/lib/typings/db */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/typings/db.js\");\n/* harmony import */ var _components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/custom-ui/loader */ \"(app-pages-browser)/./src/components/custom-ui/loader.tsx\");\n/* harmony import */ var _providers_page__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/page */ \"(app-pages-browser)/./src/providers/page.tsx\");\n/* harmony import */ var _providers_shared__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/providers/shared */ \"(app-pages-browser)/./src/providers/shared.tsx\");\n/* harmony import */ var _providers_template__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/providers/template */ \"(app-pages-browser)/./src/providers/template.tsx\");\n/* harmony import */ var _providers_views__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/providers/views */ \"(app-pages-browser)/./src/providers/views.tsx\");\n/* harmony import */ var _components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/workspace/main/views/table */ \"(app-pages-browser)/./src/components/workspace/main/views/table/index.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/icons/FontAwesomeRegular */ \"(app-pages-browser)/./src/components/icons/FontAwesomeRegular.tsx\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/subWeeks/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/subMonths/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addWeeks/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addMonths/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _providers_screenSize__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/providers/screenSize */ \"(app-pages-browser)/./src/providers/screenSize.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _providers_stackedPeek__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/providers/stackedPeek */ \"(app-pages-browser)/./src/providers/stackedPeek.tsx\");\n/* harmony import */ var _components_DayView__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./components/DayView */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/DayView.tsx\");\n/* harmony import */ var _components_WeekView__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./components/WeekView */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/WeekView.tsx\");\n/* harmony import */ var _components_MonthView__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./components/MonthView */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx\");\n/* harmony import */ var _components_CalendarEventItem__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./components/CalendarEventItem */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx\");\n/* harmony import */ var _components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/workspace/main/views/form/components/element/linked */ \"(app-pages-browser)/./src/components/workspace/main/views/form/components/element/linked.tsx\");\n/* harmony import */ var _components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/custom-ui/customSelect */ \"(app-pages-browser)/./src/components/custom-ui/customSelect.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n/* harmony import */ var _components_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./components/CalendarEventSegment */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx\");\n/* harmony import */ var _barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=differenceInDays,differenceInMilliseconds!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/differenceInMilliseconds/index.js\");\n/* harmony import */ var _barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=differenceInDays,differenceInMilliseconds!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/differenceInDays/index.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Custom modifier to restrict dragging to the calendar main content area only\nconst restrictToCalendarContainer = (param)=>{\n    let { transform, draggingNodeRect, windowRect } = param;\n    if (!draggingNodeRect || !windowRect) {\n        return transform;\n    }\n    // Find the calendar main content container (the div that contains the views)\n    const calendarContainer = document.querySelector('[data-calendar-content=\"true\"]');\n    if (!calendarContainer) {\n        return transform;\n    }\n    const containerRect = calendarContainer.getBoundingClientRect();\n    // For month view, we need to account for the side card\n    // The side card is positioned on the right side of the calendar\n    const sideCard = document.querySelector('[data-side-card=\"true\"]');\n    let maxX = containerRect.right - draggingNodeRect.width;\n    if (sideCard) {\n        const sideCardRect = sideCard.getBoundingClientRect();\n        // Restrict dragging to not go past the side card\n        maxX = Math.min(maxX, sideCardRect.left - draggingNodeRect.width);\n    }\n    // Find header areas to prevent dragging over them\n    const timeLabels = document.querySelector('[data-time-labels=\"true\"]');\n    const dayHeaders = document.querySelector('[data-day-headers=\"true\"]');\n    const allDayRow = document.querySelector('[data-all-day-row=\"true\"]');\n    // Calculate the boundaries relative to the window\n    let minX = containerRect.left;\n    let minY = containerRect.top;\n    const maxY = containerRect.bottom - draggingNodeRect.height;\n    // Prevent dragging over time labels (left side in day/week view)\n    if (timeLabels) {\n        const timeLabelsRect = timeLabels.getBoundingClientRect();\n        minX = Math.max(minX, timeLabelsRect.right);\n    }\n    // Prevent dragging over day headers (top of calendar)\n    if (dayHeaders) {\n        const dayHeadersRect = dayHeaders.getBoundingClientRect();\n        minY = Math.max(minY, dayHeadersRect.bottom);\n    }\n    // Prevent dragging over all-day row (if present)\n    if (allDayRow) {\n        const allDayRowRect = allDayRow.getBoundingClientRect();\n        minY = Math.max(minY, allDayRowRect.bottom);\n    }\n    // Get current pointer position\n    const currentX = transform.x + draggingNodeRect.left;\n    const currentY = transform.y + draggingNodeRect.top;\n    // Constrain the position\n    const constrainedX = Math.min(Math.max(currentX, minX), maxX);\n    const constrainedY = Math.min(Math.max(currentY, minY), maxY);\n    return {\n        ...transform,\n        x: constrainedX - draggingNodeRect.left,\n        y: constrainedY - draggingNodeRect.top\n    };\n};\n// Custom hook to track previous value\nconst usePrevious = (value)=>{\n    _s();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        ref.current = value;\n    });\n    return ref.current;\n};\n_s(usePrevious, \"8uVE59eA/r6b92xF80p7sH8rXLk=\");\nconst CalendarView = (props)=>{\n    _s1();\n    const { databaseStore, members, workspace } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace)();\n    const { definition } = props;\n    const { accessLevel } = (0,_providers_page__WEBPACK_IMPORTED_MODULE_5__.usePage)();\n    const { isMobile } = (0,_providers_screenSize__WEBPACK_IMPORTED_MODULE_16__.useScreenSize)();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_17__.useMaybeRecord)();\n    const maybeShared = (0,_providers_shared__WEBPACK_IMPORTED_MODULE_6__.useMaybeShared)();\n    const maybeTemplate = (0,_providers_template__WEBPACK_IMPORTED_MODULE_7__.useMaybeTemplate)();\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [viewType, setViewType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"week\");\n    const [selectedEvent, setSelectedEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showSideCalendar, setShowSideCalendar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!isMobile);\n    const [activeDragData, setActiveDragData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const savedScrollTop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const pointerCoordinates = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        x: 0,\n        y: 0\n    });\n    const isInRecordTab = !!maybeRecord;\n    definition.filter = definition.filter || {\n        conditions: [],\n        match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Match.All\n    };\n    definition.sorts = definition.sorts || [];\n    const databaseId = definition.databaseId;\n    const database = databaseStore[definition.databaseId];\n    const isPublishedView = !!maybeShared;\n    const editable = !definition.lockContent && !isPublishedView && !!accessLevel;\n    let canEditStructure = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    let canEditData = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    const { createRecords, updateRecordValues, setPeekRecordId, peekRecordId, refreshDatabase, deleteRecords } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViews)();\n    const { sorts, filter, search } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewFiltering)();\n    const { selectedIds, setSelectedIds } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewSelection)();\n    const prevPeekRecordId = usePrevious(peekRecordId);\n    const { openRecord } = (0,_providers_stackedPeek__WEBPACK_IMPORTED_MODULE_18__.useStackedPeek)();\n    const sensors = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensors)((0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.PointerSensor, {\n        activationConstraint: {\n            distance: 8\n        }\n    }), (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.TouchSensor, {\n        activationConstraint: {\n            delay: 150,\n            tolerance: 5\n        }\n    }));\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const containerId = viewType === \"day\" ? \"day-view-container\" : \"week-view-container\";\n        const container = document.getElementById(containerId);\n        if (container) {\n            requestAnimationFrame(()=>{\n                container.scrollTop = savedScrollTop.current;\n            });\n        }\n    }, [\n        selectedEvent,\n        viewType\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setShowSideCalendar(!isMobile);\n    }, [\n        isMobile\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only refresh if the peek view was open and is now closed.\n        if (prevPeekRecordId && !peekRecordId) {\n            console.log(\"Peek view was closed, refreshing calendar data\");\n            refreshDatabase(definition.databaseId);\n        }\n    }, [\n        peekRecordId,\n        prevPeekRecordId,\n        definition.databaseId,\n        refreshDatabase\n    ]);\n    if (!database) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_4__.PageLoader, {\n        size: \"full\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n        lineNumber: 211,\n        columnNumber: 25\n    }, undefined);\n    const getEvents = ()=>{\n        var _workspace_workspaceMember, _database_database;\n        if (!database) return [];\n        const { rows } = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__.filterAndSortRecords)(database, members, databaseStore, definition.filter || {\n            match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Match.All,\n            conditions: []\n        }, filter, sorts.length ? sorts : definition.sorts || [], (workspace === null || workspace === void 0 ? void 0 : (_workspace_workspaceMember = workspace.workspaceMember) === null || _workspace_workspaceMember === void 0 ? void 0 : _workspace_workspaceMember.userId) || \"\", (database === null || database === void 0 ? void 0 : (_database_database = database.database) === null || _database_database === void 0 ? void 0 : _database_database.id) || \"\");\n        const filteredRows = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__.searchFilteredRecords)(search || \"\", rows);\n        const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getDatabaseTitleCol)(database.database);\n        return filteredRows.map((row)=>{\n            const startValue = row.processedRecord.processedRecordValues[definition.eventStartColumnId];\n            let startDate;\n            if (startValue && typeof startValue === \"string\") {\n                startDate = new Date(startValue);\n            } else {\n                startDate = new Date();\n            }\n            let endDate;\n            if (definition.eventEndColumnId) {\n                const endValue = row.processedRecord.processedRecordValues[definition.eventEndColumnId];\n                if (endValue && typeof endValue === \"string\") {\n                    endDate = new Date(endValue);\n                } else {\n                    endDate = new Date(startDate.getTime() + (definition.defaultDuration || 30) * 60000);\n                }\n            } else {\n                endDate = new Date(startDate.getTime() + (definition.defaultDuration || 30) * 60000);\n            }\n            const title = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getRecordTitle)(row.record, titleColOpts.titleColId, titleColOpts.defaultTitle, titleColOpts.isContacts);\n            return {\n                id: row.id,\n                title,\n                start: startDate,\n                end: endDate,\n                record: row.record,\n                processedRecord: row.processedRecord\n            };\n        });\n    };\n    const getFilteredEvents = ()=>{\n        const baseEvents = getEvents();\n        if (!searchTerm.trim()) {\n            return baseEvents;\n        }\n        return baseEvents.filter((event)=>{\n            const searchLower = searchTerm.toLowerCase();\n            return event.title.toLowerCase().includes(searchLower);\n        });\n    };\n    const onDragStart = (event)=>{\n        if (event.active.data.current) {\n            var _event_active_rect_current_translated, _event_active_rect_current, _event_active_rect_current_translated1, _event_active_rect_current_translated2;\n            const initialPointerY = pointerCoordinates.current.y;\n            var _event_active_rect_current_translated_top;\n            const initialEventTop = (_event_active_rect_current_translated_top = (_event_active_rect_current = event.active.rect.current) === null || _event_active_rect_current === void 0 ? void 0 : (_event_active_rect_current_translated = _event_active_rect_current.translated) === null || _event_active_rect_current_translated === void 0 ? void 0 : _event_active_rect_current_translated.top) !== null && _event_active_rect_current_translated_top !== void 0 ? _event_active_rect_current_translated_top : 0;\n            const grabOffsetY = initialPointerY - initialEventTop;\n            // Get the exact dimensions from the DOM element\n            // Handle both event and segment types\n            const { payload, type } = event.active.data.current;\n            const eventId = type === \"segment\" ? payload.originalEvent.id : payload.id;\n            const draggedElement = document.getElementById(\"event-\".concat(eventId));\n            const width = draggedElement ? draggedElement.offsetWidth : (_event_active_rect_current_translated1 = event.active.rect.current.translated) === null || _event_active_rect_current_translated1 === void 0 ? void 0 : _event_active_rect_current_translated1.width;\n            const height = draggedElement ? draggedElement.offsetHeight : (_event_active_rect_current_translated2 = event.active.rect.current.translated) === null || _event_active_rect_current_translated2 === void 0 ? void 0 : _event_active_rect_current_translated2.height;\n            setActiveDragData({\n                ...event.active.data.current,\n                grabOffsetY,\n                width,\n                height\n            });\n            document.addEventListener(\"mousemove\", handleMouseMove);\n            document.addEventListener(\"touchmove\", handleTouchMove);\n        }\n    };\n    const onDragEnd = async (param)=>{\n        let { active, over } = param;\n        document.removeEventListener(\"mousemove\", handleMouseMove);\n        document.removeEventListener(\"touchmove\", handleTouchMove);\n        setActiveDragData(null);\n        if (!over || !active || !canEditData || active.id === over.id) {\n            return;\n        }\n        const activeData = active.data.current;\n        const overData = over.data.current;\n        if (!activeData || !overData) {\n            return;\n        }\n        const { payload, type } = activeData;\n        const eventToUpdate = type === \"segment\" ? payload.originalEvent : payload;\n        const originalStart = new Date(eventToUpdate.start);\n        const originalEnd = new Date(eventToUpdate.end);\n        const duration = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_27__[\"default\"])(originalEnd, originalStart);\n        let newStart;\n        if (overData.type.startsWith(\"allday\")) {\n            const dayDifference = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_28__[\"default\"])(overData.date, (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_29__[\"default\"])(originalStart));\n            newStart = (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(originalStart, dayDifference);\n            newStart.setHours(0, 0, 0, 0);\n        } else if (overData.type === \"timeslot-minute\") {\n            // Handle precise minute-based drops\n            newStart = new Date(overData.date);\n            newStart.setHours(overData.hour, overData.minute, 0, 0);\n        } else if (overData.type === \"daycell\") {\n            const dayDifference = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_28__[\"default\"])(overData.date, (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_29__[\"default\"])(originalStart));\n            newStart = (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(originalStart, dayDifference);\n        } else {\n            return;\n        }\n        const newEnd = new Date(newStart.getTime() + duration);\n        const recordId = eventToUpdate.record.id;\n        const newValues = {\n            [definition.eventStartColumnId]: newStart.toISOString(),\n            ...definition.eventEndColumnId && {\n                [definition.eventEndColumnId]: newEnd.toISOString()\n            }\n        };\n        try {\n            await updateRecordValues(definition.databaseId, [\n                recordId\n            ], newValues);\n            sonner__WEBPACK_IMPORTED_MODULE_14__.toast.success('Event \"'.concat(eventToUpdate.title, '\" updated.'));\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Failed to update event.\");\n            console.error(\"Error updating event:\", error);\n        }\n    };\n    const handleMouseMove = (event)=>{\n        pointerCoordinates.current = {\n            x: event.clientX,\n            y: event.clientY\n        };\n    };\n    const handleTouchMove = (event)=>{\n        const touch = event.touches[0];\n        pointerCoordinates.current = {\n            x: touch.clientX,\n            y: touch.clientY\n        };\n    };\n    const events = getFilteredEvents();\n    const goToToday = ()=>setSelectedDate(new Date());\n    const goToPrevious = ()=>{\n        switch(viewType){\n            case \"day\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(prevDate, -1));\n                break;\n            case \"week\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_31__[\"default\"])(prevDate, 1));\n                break;\n            case \"month\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_32__[\"default\"])(prevDate, 1));\n                break;\n        }\n    };\n    const goToNext = ()=>{\n        switch(viewType){\n            case \"day\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(prevDate, 1));\n                break;\n            case \"week\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_33__[\"default\"])(prevDate, 1));\n                break;\n            case \"month\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_34__[\"default\"])(prevDate, 1));\n                break;\n        }\n    };\n    const handleRequestCreateEvent = function(date) {\n        let useSystemTime = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (useSystemTime) {\n            const now = new Date();\n            const newDate = new Date(date);\n            newDate.setHours(now.getHours(), now.getMinutes(), now.getSeconds(), now.getMilliseconds());\n            handleCreateEvent(newDate);\n        } else {\n            handleCreateEvent(date);\n        }\n    };\n    const handleCreateEvent = async function() {\n        let date = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : new Date();\n        if (!canEditData) return;\n        const startTime = new Date(date);\n        const endTime = new Date(startTime.getTime() + (definition.defaultDuration || 30) * 60000);\n        const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getDatabaseTitleCol)(database.database);\n        try {\n            const recordValues = {\n                [definition.eventStartColumnId]: startTime.toISOString(),\n                ...definition.eventEndColumnId && {\n                    [definition.eventEndColumnId]: endTime.toISOString()\n                }\n            };\n            if (titleColOpts.titleColId) {\n                recordValues[titleColOpts.titleColId] = \"New Event\";\n            }\n            const result = await createRecords(definition.databaseId, [\n                recordValues\n            ]);\n            if (result && result.records && result.records.length > 0) {\n                const newRecordId = result.records[0].id;\n                if (newRecordId) {\n                    await refreshDatabase(definition.databaseId);\n                    setPeekRecordId(newRecordId);\n                    sonner__WEBPACK_IMPORTED_MODULE_14__.toast.success(\"New event created\");\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Error accessing the new event\");\n                }\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Failed to create event properly\");\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Failed to create event\");\n        }\n    };\n    const handleEventClick = (event)=>{\n        if (event && event.id) {\n            const containerId = viewType === \"day\" ? \"day-view-container\" : \"week-view-container\";\n            const container = document.getElementById(containerId);\n            if (container) {\n                savedScrollTop.current = container.scrollTop;\n            }\n            openRecord(event.id, event.record.databaseId);\n            setSelectedEvent(event.id);\n        }\n    };\n    const getHeaderDateDisplay = ()=>{\n        switch(viewType){\n            case \"day\":\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(selectedDate, \"MMMM d, yyyy\");\n            case \"week\":\n                return \"\".concat((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(selectedDate, -selectedDate.getDay()), \"MMM d\"), \" - \").concat((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(selectedDate, 6 - selectedDate.getDay()), \"MMM d, yyyy\"));\n            case \"month\":\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(selectedDate, \"MMMM yyyy\");\n            default:\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(selectedDate, \"MMMM d, yyyy\");\n        }\n    };\n    const handleEventDelete = async (event)=>{\n        if (!canEditData) return;\n        try {\n            await deleteRecords(database.database.id, [\n                event.record.id\n            ]);\n        } catch (error) {\n            console.error(\"Error deleting event:\", error);\n        }\n    };\n    const viewTypeOptions = [\n        {\n            id: \"day\",\n            value: \"day\",\n            title: \"Day\",\n            data: \"day\"\n        },\n        {\n            id: \"week\",\n            value: \"week\",\n            title: \"Week\",\n            data: \"week\"\n        },\n        {\n            id: \"month\",\n            value: \"month\",\n            title: \"Month\",\n            data: \"month\"\n        }\n    ];\n    const selectedViewOption = viewType === \"day\" ? [\n        \"day\"\n    ] : viewType === \"week\" ? [\n        \"week\"\n    ] : [\n        \"month\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"border-b border-neutral-300 bg-white\", isInRecordTab && \"py-1\"),\n                children: [\n                    isMobile ? /* Mobile Header Layout */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"p-2\", isInRecordTab && \"py-1\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xs font-semibold text-black truncate flex-1 mr-2\",\n                                        children: getHeaderDateDisplay()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: ()=>setShowSideCalendar(!showSideCalendar),\n                                        className: \"rounded-full h-8 px-3 text-xs text-black hover:bg-gray-50\",\n                                        children: \"Calendar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 509,\n                                columnNumber: 12\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToToday,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs text-black hover:bg-gray-50\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                children: \"Today\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        variant: \"ghost\",\n                                                        onClick: goToPrevious,\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleLeftIcon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 20\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 18\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        variant: \"ghost\",\n                                                        onClick: goToNext,\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleRightIcon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 20\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 18\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 16\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__.CustomSelect, {\n                                                options: viewTypeOptions,\n                                                selectedIds: selectedViewOption,\n                                                onChange: (selected)=>{\n                                                    if (selected.length > 0) {\n                                                        setViewType(selected[0]);\n                                                    }\n                                                },\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"px-3 text-xs border-neutral-300 rounded-full bg-white hover:bg-gray-50 text-black w-20\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                placeholder: \"View\",\n                                                hideSearch: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                onClick: ()=>handleRequestCreateEvent(selectedDate, true),\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs border border-neutral-300 bg-white hover:bg-gray-50 text-black\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.PlusIcon, {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 584,\n                                                    columnNumber: 20\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 577,\n                                                columnNumber: 18\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 558,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 522,\n                                columnNumber: 12\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 508,\n                        columnNumber: 10\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"flex items-center justify-between px-4\", isInRecordTab ? \"py-1\" : \"py-2\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: goToToday,\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs text-black hover:bg-gray-50\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                        children: \"Today\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 596,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToPrevious,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleLeftIcon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 615,\n                                                    columnNumber: 16\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 14\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToNext,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleRightIcon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 625,\n                                                    columnNumber: 16\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 617,\n                                                columnNumber: 14\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 606,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xs font-semibold text-black\",\n                                        children: getHeaderDateDisplay()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 629,\n                                        columnNumber: 12\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 595,\n                                columnNumber: 10\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                                placeholder: \"Search events...\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"w-48 pr-8 h-8 text-xs border-neutral-300 bg-transparent shadow-none\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 637,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.MagnifyingGlassIcon, {\n                                                className: \"h-3 w-3 absolute right-2 top-1/2 transform -translate-y-1/2 text-neutral-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 643,\n                                                columnNumber: 16\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 636,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__.CustomSelect, {\n                                        options: viewTypeOptions,\n                                        selectedIds: selectedViewOption,\n                                        onChange: (selected)=>{\n                                            if (selected.length > 0) {\n                                                setViewType(selected[0]);\n                                            }\n                                        },\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"px-3 text-xs border-neutral-300 rounded-full bg-white hover:bg-gray-50 text-black\", isInRecordTab ? \"h-6 w-20\" : \"h-8 w-28\"),\n                                        placeholder: \"View\",\n                                        hideSearch: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 647,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        onClick: ()=>handleRequestCreateEvent(selectedDate, true),\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs border border-neutral-300 bg-white hover:bg-gray-50 text-black gap-1\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.PlusIcon, {\n                                                className: \"h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 671,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Add Event\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 672,\n                                                columnNumber: 35\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 664,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 634,\n                                columnNumber: 10\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 591,\n                        columnNumber: 8\n                    }, undefined),\n                    isMobile && !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-2 pb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                    placeholder: \"Search events...\",\n                                    value: searchTerm,\n                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                    className: \"w-full pr-8 h-8 text-xs border-neutral-300 bg-transparent shadow-none\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 682,\n                                    columnNumber: 12\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.MagnifyingGlassIcon, {\n                                    className: \"h-3 w-3 absolute right-2 top-1/2 transform -translate-y-1/2 text-neutral-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 688,\n                                    columnNumber: 12\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                            lineNumber: 681,\n                            columnNumber: 10\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 680,\n                        columnNumber: 8\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                lineNumber: 502,\n                columnNumber: 6\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex min-h-0\",\n                children: [\n                    showSideCalendar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"flex-none bg-white\", isMobile ? \"w-full absolute z-50 backdrop-blur-sm h-full shadow-lg\" : \"w-fit border-r border-neutral-300\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center p-2 border-b border-neutral-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xs font-semibold text-black\",\n                                        children: \"Calendar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 702,\n                                        columnNumber: 10\n                                    }, undefined),\n                                    isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: ()=>setShowSideCalendar(false),\n                                        className: \"rounded-full h-8 w-8 p-1 text-black hover:bg-neutral-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Close\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 709,\n                                                columnNumber: 14\n                                            }, undefined),\n                                            \"\\xd7\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 704,\n                                        columnNumber: 12\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 701,\n                                columnNumber: 8\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                mode: \"single\",\n                                selected: selectedDate,\n                                onSelect: (date)=>{\n                                    if (date) {\n                                        setSelectedDate(date);\n                                        if (isMobile) {\n                                            setShowSideCalendar(false);\n                                        }\n                                    }\n                                },\n                                className: \"rounded-md border-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 714,\n                                columnNumber: 8\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 697,\n                        columnNumber: 6\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.DndContext, {\n                        sensors: sensors,\n                        onDragStart: onDragStart,\n                        onDragEnd: onDragEnd,\n                        modifiers: [\n                            restrictToCalendarContainer\n                        ],\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                \"data-calendar-content\": \"true\",\n                                children: [\n                                    viewType === \"day\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DayView__WEBPACK_IMPORTED_MODULE_19__.DayView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        openAddEventForm: handleRequestCreateEvent,\n                                        canEditData: canEditData,\n                                        savedScrollTop: savedScrollTop,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 738,\n                                        columnNumber: 7\n                                    }, undefined),\n                                    viewType === \"week\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_WeekView__WEBPACK_IMPORTED_MODULE_20__.WeekView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        setSelectedDate: setSelectedDate,\n                                        openAddEventForm: handleRequestCreateEvent,\n                                        canEditData: canEditData,\n                                        savedScrollTop: savedScrollTop,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 751,\n                                        columnNumber: 7\n                                    }, undefined),\n                                    viewType === \"month\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MonthView__WEBPACK_IMPORTED_MODULE_21__.MonthView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        setSelectedDate: setSelectedDate,\n                                        openAddEventForm: (date)=>handleRequestCreateEvent(date, true),\n                                        canEditData: canEditData,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 765,\n                                        columnNumber: 7\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 736,\n                                columnNumber: 6\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.DragOverlay, {\n                                dropAnimation: null,\n                                children: activeDragData && activeDragData.type === \"segment\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_26__.CalendarEventSegment, {\n                                    segment: activeDragData.payload,\n                                    view: viewType === \"day\" ? \"day\" : \"week\",\n                                    onClick: ()=>{},\n                                    style: {\n                                        width: activeDragData.width,\n                                        height: activeDragData.height\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 781,\n                                    columnNumber: 11\n                                }, undefined) : activeDragData && activeDragData.type === \"event\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CalendarEventItem__WEBPACK_IMPORTED_MODULE_22__.CalendarEventItem, {\n                                    event: activeDragData.payload,\n                                    view: viewType,\n                                    onClick: ()=>{},\n                                    style: {\n                                        width: activeDragData.width,\n                                        height: activeDragData.height\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 791,\n                                    columnNumber: 11\n                                }, undefined) : null\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 779,\n                                columnNumber: 6\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 730,\n                        columnNumber: 6\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                lineNumber: 695,\n                columnNumber: 4\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n        lineNumber: 501,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(CalendarView, \"gC+3ORgbOSOWj17lR2zBDE1vWrs=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace,\n        _providers_page__WEBPACK_IMPORTED_MODULE_5__.usePage,\n        _providers_screenSize__WEBPACK_IMPORTED_MODULE_16__.useScreenSize,\n        _providers_record__WEBPACK_IMPORTED_MODULE_17__.useMaybeRecord,\n        _providers_shared__WEBPACK_IMPORTED_MODULE_6__.useMaybeShared,\n        _providers_template__WEBPACK_IMPORTED_MODULE_7__.useMaybeTemplate,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViews,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewFiltering,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewSelection,\n        usePrevious,\n        _providers_stackedPeek__WEBPACK_IMPORTED_MODULE_18__.useStackedPeek,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensors\n    ];\n});\n_c = CalendarView;\nvar _c;\n$RefreshReg$(_c, \"CalendarView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/index.tsx\n"));

/***/ })

});