"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[domain]/layout",{

/***/ "(app-pages-browser)/./src/components/workspace/main/common/searchmodal.tsx":
/*!**************************************************************!*\
  !*** ./src/components/workspace/main/common/searchmodal.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SearchModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var _providers_user__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/user */ \"(app-pages-browser)/./src/providers/user.tsx\");\n/* harmony import */ var _api_workspace__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/api/workspace */ \"(app-pages-browser)/./src/api/workspace.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/formatDistanceToNow/index.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! lodash/debounce */ \"(app-pages-browser)/./node_modules/lodash/debounce.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/icons/FontAwesomeRegular */ \"(app-pages-browser)/./src/components/icons/FontAwesomeRegular.tsx\");\n/* harmony import */ var _components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/custom-ui/loader */ \"(app-pages-browser)/./src/components/custom-ui/loader.tsx\");\n/* harmony import */ var _views_viewIcon__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../views/viewIcon */ \"(app-pages-browser)/./src/components/workspace/main/views/viewIcon.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! opendb-app-db-utils/lib/typings/view */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/typings/view.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst HighlightedContent = (param)=>{\n    let { content, highlight, query } = param;\n    if (!content) return null;\n    if (query && query.trim()) {\n        const searchTerm = query.trim().toLowerCase();\n        const contentLower = content.toLowerCase();\n        const parts = [];\n        let lastIndex = 0;\n        let currentIndex = contentLower.indexOf(searchTerm, lastIndex);\n        if (currentIndex === -1) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: content\n        }, void 0, false);\n        while(currentIndex !== -1){\n            parts.push(content.substring(lastIndex, currentIndex));\n            parts.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mark\", {\n                className: \"bg-yellow-200\",\n                children: content.substring(currentIndex, currentIndex + searchTerm.length)\n            }, currentIndex, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                lineNumber: 34,\n                columnNumber: 9\n            }, undefined));\n            lastIndex = currentIndex + searchTerm.length;\n            currentIndex = contentLower.indexOf(searchTerm, lastIndex);\n        }\n        if (lastIndex < content.length) {\n            parts.push(content.substring(lastIndex));\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: parts\n        }, void 0, false);\n    }\n    if (highlight && highlight.start >= 0) {\n        const start = Math.max(0, highlight.start);\n        const end = Math.min(content.length, highlight.end);\n        if (start < end && start < content.length) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    content.substring(0, start),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mark\", {\n                        className: \"bg-yellow-200\",\n                        children: content.substring(start, end)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, undefined),\n                    content.substring(end)\n                ]\n            }, void 0, true);\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: content\n    }, void 0, false);\n};\n_c = HighlightedContent;\nconst groupResults = (results)=>{\n    const grouped = {\n        databases: [],\n        pages: [],\n        views: [],\n        documents: [],\n        members: []\n    };\n    results.forEach((result)=>{\n        if (result.image || !result.source) {\n            grouped.members.push(result);\n        } else if (result.source) {\n            if (result.source.databaseId && !result.source.viewId) grouped.databases.push(result);\n            else if (result.source.viewId) grouped.views.push(result);\n            else if (result.source.documentId) grouped.documents.push(result);\n            else if (result.source.pageId) grouped.pages.push(result);\n        }\n    });\n    return grouped;\n};\nfunction SearchModal(param) {\n    let { onClose, debounceTimeoutMS = 2500 } = param;\n    var _workspace_workspace, _workspace_workspace1;\n    _s();\n    const [query, setQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"), [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [recentSearches, setRecentSearches] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1), [hasMore, setHasMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [totalItems, setTotalItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0), [currentQuery, setCurrentQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const { workspace, url } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_4__.useWorkspace)(), { token } = (0,_providers_user__WEBPACK_IMPORTED_MODULE_5__.useAuth)(), router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const workspaceId = workspace === null || workspace === void 0 ? void 0 : (_workspace_workspace = workspace.workspace) === null || _workspace_workspace === void 0 ? void 0 : _workspace_workspace.id;\n    const resultsContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedSearches = localStorage.getItem(\"recentSearches\");\n        if (savedSearches) setRecentSearches(JSON.parse(savedSearches));\n    }, []);\n    const getErrorMessage = (error)=>{\n        if (error instanceof Error) {\n            if (\"response\" in error) {\n                var _apiError_response_data, _apiError_response;\n                const apiError = error;\n                return ((_apiError_response = apiError.response) === null || _apiError_response === void 0 ? void 0 : (_apiError_response_data = _apiError_response.data) === null || _apiError_response_data === void 0 ? void 0 : _apiError_response_data.message) || apiError.message;\n            }\n            return error.message;\n        }\n        return \"An unexpected error occurred\";\n    };\n    const performSearch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async function(searchQuery) {\n        let page = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1, append = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        if (!searchQuery.trim() || !workspaceId || !token) {\n            setResults([]);\n            setError(null);\n            setIsLoading(false);\n            setHasMore(false);\n            setTotalItems(0);\n            setCurrentPage(1);\n            return;\n        }\n        const currentSearchQuery = searchQuery;\n        if (page === 1) setHasMore(true);\n        setIsLoading(true);\n        setError(null);\n        try {\n            const response = await (0,_api_workspace__WEBPACK_IMPORTED_MODULE_6__.searchWorkspaces)(token.token, workspaceId, currentSearchQuery, page, 25);\n            if (!response.isSuccess) throw new Error(response.error || \"Search failed\");\n            const newResults = response.data.data.results.results || [];\n            const pagination = response.data.data.results.pagination;\n            if (currentSearchQuery === query) {\n                setTotalItems(pagination.totalItems);\n                setHasMore(page < pagination.totalPages);\n                setResults(append ? (prev)=>[\n                        ...prev,\n                        ...newResults\n                    ] : newResults);\n                setCurrentPage(page);\n                setCurrentQuery(currentSearchQuery);\n            }\n        } catch (error) {\n            console.error(\"Search error:\", error);\n            if (!append && currentSearchQuery === query) {\n                setError(getErrorMessage(error));\n            }\n        } finally{\n            if (currentSearchQuery === query) {\n                setIsLoading(false);\n            }\n        }\n    }, [\n        workspaceId,\n        token,\n        query\n    ]);\n    const debouncedSearch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(lodash_debounce__WEBPACK_IMPORTED_MODULE_8___default()((searchQuery)=>{\n        if (searchQuery.trim()) {\n            setCurrentPage(1);\n            performSearch(searchQuery, 1, false);\n        }\n    }, debounceTimeoutMS), [\n        performSearch,\n        debounceTimeoutMS,\n        setCurrentPage\n    ]);\n    const loadMore = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!isLoading && hasMore && currentQuery) {\n            performSearch(currentQuery, currentPage + 1, true);\n        }\n    }, [\n        isLoading,\n        hasMore,\n        currentQuery,\n        currentPage,\n        performSearch\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!resultsContainerRef.current) return;\n        const observer = new IntersectionObserver((entries)=>{\n            const [entry] = entries;\n            if (entry.isIntersecting && hasMore && !isLoading && results.length > 0) {\n                loadMore();\n            }\n        }, {\n            root: resultsContainerRef.current,\n            rootMargin: \"0px 0px 200px 0px\",\n            threshold: 0.1\n        });\n        const sentinel = document.getElementById(\"search-results-sentinel\");\n        if (sentinel) {\n            observer.observe(sentinel);\n        }\n        return ()=>{\n            if (sentinel) {\n                observer.unobserve(sentinel);\n            }\n            observer.disconnect();\n        };\n    }, [\n        loadMore,\n        hasMore,\n        isLoading,\n        results.length\n    ]);\n    // Handle search input changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        debouncedSearch.cancel();\n        setResults([]);\n        setCurrentPage(1);\n        setHasMore(false);\n        setTotalItems(0);\n        setCurrentQuery(\"\");\n        if (query.trim()) {\n            setIsLoading(true);\n            debouncedSearch(query);\n        } else {\n            setIsLoading(false);\n        }\n        return ()=>debouncedSearch.cancel();\n    }, [\n        query,\n        debouncedSearch\n    ]);\n    const handleResultClick = (result)=>{\n        router.push(url(result.path));\n        onClose();\n        saveRecentSearch(query);\n    };\n    const saveRecentSearch = (search)=>{\n        if (search.trim()) {\n            const updatedSearches = [\n                search,\n                ...recentSearches.filter((s)=>s !== search)\n            ].slice(0, 5);\n            setRecentSearches(updatedSearches);\n            localStorage.setItem(\"recentSearches\", JSON.stringify(updatedSearches));\n        }\n    };\n    const deleteRecentSearch = (search, e)=>{\n        e.stopPropagation();\n        const updatedSearches = recentSearches.filter((s)=>s !== search);\n        setRecentSearches(updatedSearches);\n        localStorage.setItem(\"recentSearches\", JSON.stringify(updatedSearches));\n    };\n    const getIconForSource = (result)=>{\n        var _result_path, _result_path1, _result_source, _result_source1, _result_source2, _result_source3;\n        if ((_result_path = result.path) === null || _result_path === void 0 ? void 0 : _result_path.includes(\"?tab=reminders\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.ClockThreeIcon, {\n            className: \"h-4 w-4 text-muted-foreground\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n            lineNumber: 243,\n            columnNumber: 57\n        }, this);\n        if ((_result_path1 = result.path) === null || _result_path1 === void 0 ? void 0 : _result_path1.includes(\"?tab=notes\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.NoteIcon, {\n            className: \"h-4 w-4 text-muted-foreground\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n            lineNumber: 244,\n            columnNumber: 53\n        }, this);\n        if (result.viewType) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_views_viewIcon__WEBPACK_IMPORTED_MODULE_11__.ViewIcon, {\n            type: result.viewType,\n            className: \"h-4 w-4 text-muted-foreground\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n            lineNumber: 246,\n            columnNumber: 33\n        }, this);\n        if (((_result_source = result.source) === null || _result_source === void 0 ? void 0 : _result_source.databaseId) && result.source.recordId) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.SquareListIcon, {\n            className: \"h-4 w-4 text-primary\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n            lineNumber: 248,\n            columnNumber: 69\n        }, this);\n        if ((_result_source1 = result.source) === null || _result_source1 === void 0 ? void 0 : _result_source1.databaseId) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.TableIcon, {\n            className: \"h-4 w-4 text-primary\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n            lineNumber: 249,\n            columnNumber: 43\n        }, this);\n        if ((_result_source2 = result.source) === null || _result_source2 === void 0 ? void 0 : _result_source2.documentId) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.BookIcon, {\n            className: \"h-4 w-4 text-accent-foreground\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n            lineNumber: 250,\n            columnNumber: 43\n        }, this);\n        if ((_result_source3 = result.source) === null || _result_source3 === void 0 ? void 0 : _result_source3.pageId) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.BookIcon, {\n            className: \"h-4 w-4 text-secondary-foreground\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n            lineNumber: 251,\n            columnNumber: 39\n        }, this);\n        if (result.image !== undefined || !result.source) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.UserGroupIcon, {\n            className: \"h-4 w-4 text-muted-foreground\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n            lineNumber: 253,\n            columnNumber: 62\n        }, this);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.BookIcon, {\n            className: \"h-4 w-4 text-muted-foreground\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n            lineNumber: 254,\n            columnNumber: 12\n        }, this);\n    };\n    const getResultType = (result)=>{\n        var _result_path, _result_path1;\n        if ((_result_path = result.path) === null || _result_path === void 0 ? void 0 : _result_path.includes(\"?tab=reminders\")) return \"Reminder\";\n        if ((_result_path1 = result.path) === null || _result_path1 === void 0 ? void 0 : _result_path1.includes(\"?tab=notes\")) return \"Note\";\n        if (result.image) return \"Member\";\n        if (!result.source) return \"Member\";\n        if (result.source.databaseId && result.viewType) {\n            switch(result.viewType){\n                case opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_12__.ViewType.Table:\n                    return \"Table View\";\n                case opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_12__.ViewType.Board:\n                    return \"Board View\";\n                case opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_12__.ViewType.Dashboard:\n                    return \"Dashboard\";\n                case opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_12__.ViewType.Document:\n                    return \"Document View\";\n                case opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_12__.ViewType.Form:\n                    return \"Form View\";\n                case opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_12__.ViewType.SummaryTable:\n                    return \"Summary Table\";\n                case opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_12__.ViewType.ListView:\n                    return \"List View\";\n                default:\n                    return \"Database View\";\n            }\n        }\n        if (result.source.databaseId && result.source.recordId) return \"Record\";\n        if (result.source.databaseId) return \"Database\";\n        if (result.source.documentId) return \"Document\";\n        if (result.source.pageId) return \"Page\";\n        return \"Document\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        open: true,\n        onOpenChange: onClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n            className: \"p-0 gap-0 w-[95vw] max-w-xl rounded-lg shadow-xl border border-gray-200 overflow-hidden sm:w-full max-h-[90vh]\",\n            hideCloseBtn: true,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center border-b px-2 sm:px-3 relative bg-gray-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.MagnifyingGlassIcon, {\n                            className: \"mr-2 h-4 w-4 shrink-0 text-gray-500\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                            className: \"flex h-10 sm:h-12 rounded-md border-0 bg-transparent py-1.5 sm:py-2 text-xs outline-none placeholder:text-gray-500 focus-visible:ring-0 disabled:cursor-not-allowed disabled:opacity-50\",\n                            placeholder: \"Search \".concat((workspace === null || workspace === void 0 ? void 0 : (_workspace_workspace1 = workspace.workspace) === null || _workspace_workspace1 === void 0 ? void 0 : _workspace_workspace1.name) || \"workspace\", \"...\"),\n                            value: query,\n                            onChange: (e)=>setQuery(e.target.value),\n                            autoFocus: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-h-[60vh] sm:max-h-[65vh] overflow-y-auto pr-1\",\n                    ref: resultsContainerRef,\n                    children: [\n                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed top-0 left-0 right-0 h-0.5 z-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-primary-500/30 h-full animate-pulse\",\n                                style: {\n                                    width: \"100%\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 13\n                        }, this),\n                        isLoading && currentPage === 1 && results.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 sm:px-6 py-6 sm:py-8 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_10__.Loader, {\n                                    className: \"inline-block w-5 h-5 sm:w-6 sm:h-6 text-gray-600 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-2 sm:mt-3 text-xs text-gray-600\",\n                                    children: \"Searching workspace...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 13\n                        }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 sm:px-6 py-6 sm:py-8 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mx-auto flex h-10 w-10 sm:h-12 sm:w-12 items-center justify-center rounded-full bg-red-100 mb-2 sm:mb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.XmarkIcon, {\n                                        className: \"h-4 w-4 sm:h-5 sm:w-5 text-red-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xs font-medium text-gray-900\",\n                                    children: \"Search Error\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-1 text-xs text-red-500\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-3 sm:mt-4 flex justify-center gap-2 sm:gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setError(null);\n                                                debouncedSearch(query);\n                                            },\n                                            className: \"inline-flex items-center px-2 sm:px-3 py-1.5 sm:py-2 text-xs font-medium text-white bg-primary-600 rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n                                            children: \"Try again\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: onClose,\n                                            className: \"inline-flex items-center px-2 sm:px-3 py-1.5 sm:py-2 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 13\n                        }, this) : !query.trim() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-[9px] font-medium text-gray-400 uppercase tracking-tight px-2 py-0.5 bg-gray-50\",\n                                    children: \"Recent Searches\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 15\n                                }, this),\n                                recentSearches.length > 0 ? recentSearches.map((search, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between px-2 py-2 cursor-pointer hover:bg-gray-50 rounded transition-colors\",\n                                        onClick: ()=>setQuery(search),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.TimerIcon, {\n                                                        className: \"h-4 w-4 text-gray-400 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-700\",\n                                                        children: search\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>deleteRecentSearch(search, e),\n                                                className: \"p-1 rounded-full hover:bg-gray-100 transition-colors\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.XmarkIcon, {\n                                                    className: \"h-3 w-3 text-gray-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 19\n                                    }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-2 py-3 text-xs text-gray-500\",\n                                    children: \"No recent searches\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 13\n                        }, this) : results.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 sm:px-6 py-6 sm:py-8 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mx-auto flex h-10 w-10 sm:h-12 sm:w-12 items-center justify-center rounded-full bg-gray-100 mb-2 sm:mb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.MagnifyingGlassIcon, {\n                                        className: \"h-4 w-4 sm:h-5 sm:w-5 text-gray-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xs font-medium text-gray-900\",\n                                    children: \"No results found\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-1 text-xs text-gray-500\",\n                                    children: [\n                                        \"We couldn't find anything matching \\\"\",\n                                        query,\n                                        '\"'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                            lineNumber: 341,\n                            columnNumber: 13\n                        }, this) : Object.entries(groupResults(results)).map((param)=>{\n                            let [category, items] = param;\n                            return items.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-[9px] font-medium text-gray-400 uppercase tracking-tight px-2 sm:px-3 py-0.5 sm:py-1 bg-gray-50\",\n                                        children: category\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 19\n                                    }, this),\n                                    items.map((result)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-2 sm:px-3 py-1.5 sm:py-2 cursor-pointer hover:bg-gray-50 transition-colors flex items-start gap-1.5 sm:gap-3\",\n                                            onClick: ()=>handleResultClick(result),\n                                            children: [\n                                                result.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: result.image,\n                                                    alt: result.name || \"\",\n                                                    className: \"h-6 w-6 sm:h-7 sm:w-7 rounded-full object-cover mt-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 25\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 mt-0.5 p-1 sm:p-1.5 rounded-md sm:rounded-lg bg-gray-100 text-gray-600\",\n                                                    children: getIconForSource(result)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"min-w-0 flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-baseline\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xs font-medium text-gray-900 truncate\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HighlightedContent, {\n                                                                        content: result.title || result.name,\n                                                                        highlight: result.highlight,\n                                                                        query: query\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                                        lineNumber: 366,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                                    lineNumber: 365,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                result.publishedAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-500 ml-2 whitespace-nowrap hidden sm:inline\",\n                                                                    children: (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(new Date(result.publishedAt), {\n                                                                        addSuffix: true\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                                    lineNumber: 373,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                            lineNumber: 364,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        result.content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mt-0.5 sm:mt-1 text-xs text-gray-500 line-clamp-1 sm:line-clamp-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HighlightedContent, {\n                                                                content: result.content,\n                                                                highlight: result.highlight,\n                                                                query: query\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                                lineNumber: 380,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                            lineNumber: 379,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        result.source && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-1 sm:mt-1.5\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center px-1 py-0.5 rounded text-[9px] font-medium bg-gray-100 text-gray-800\",\n                                                                children: getResultType(result)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                                lineNumber: 389,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, result.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 21\n                                        }, this))\n                                ]\n                            }, category, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 17\n                            }, this);\n                        }),\n                        isLoading && currentPage > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-2 sm:py-3 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_10__.Loader, {\n                                className: \"inline-block w-3 h-3 sm:w-4 sm:h-4 text-gray-400 animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                            lineNumber: 403,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            id: \"search-results-sentinel\",\n                            className: \"h-4 w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                            lineNumber: 409,\n                            columnNumber: 11\n                        }, this),\n                        !isLoading && results.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-2 sm:py-3 text-center\",\n                            children: hasMore ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-500\",\n                                children: \"Scroll for more results\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                lineNumber: 414,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-500\",\n                                children: totalItems > 0 ? \"Showing all \".concat(totalItems, \" results\") : \"No more results\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                lineNumber: 418,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                            lineNumber: 412,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n            lineNumber: 286,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n        lineNumber: 285,\n        columnNumber: 5\n    }, this);\n}\n_s(SearchModal, \"VlhYsU+EfLYvri7d/SGldOdcNNU=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_4__.useWorkspace,\n        _providers_user__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter\n    ];\n});\n_c1 = SearchModal;\nvar _c, _c1;\n$RefreshReg$(_c, \"HighlightedContent\");\n$RefreshReg$(_c1, \"SearchModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/common/searchmodal.tsx\n"));

/***/ })

});