"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/utils/eventCollisionUtils.ts":
/*!******************************************!*\
  !*** ./src/utils/eventCollisionUtils.ts ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateAllDayLayout: function() { return /* binding */ calculateAllDayLayout; },\n/* harmony export */   calculateLayout: function() { return /* binding */ calculateLayout; },\n/* harmony export */   eventsOverlap: function() { return /* binding */ eventsOverlap; }\n/* harmony export */ });\n/* harmony import */ var _barrel_optimize_names_isSameDay_date_fns__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! __barrel_optimize__?names=isSameDay!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n\n// --- CONSTANTS ---\nconst GUTTER_WIDTH_PERCENT = 10; // Space on the right for creating new events\nconst MIN_EVENT_WIDTH_PERCENT = 50; // Minimum width for events to remain readable\n/**\n * Checks if two event segments overlap in time on the same day.\n */ const segmentsOverlap = (segment1, segment2)=>{\n    // Must be on the same day\n    if (!(0,_barrel_optimize_names_isSameDay_date_fns__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(segment1.date, segment2.date)) {\n        return false;\n    }\n    return segment1.startTime < segment2.endTime && segment2.startTime < segment1.endTime;\n};\n/**\n * The primary function to calculate layout for event segments, incorporating a \"+N more\" button\n * and a gutter for creating new events.\n */ const calculateLayout = (segments)=>{\n    const finalLayout = {\n        segmentLayouts: []\n    };\n    if (!segments.length) {\n        return finalLayout;\n    }\n    // Sort segments by start time only - this preserves the natural order\n    const sortedSegments = [\n        ...segments\n    ].sort((a, b)=>a.startTime.getTime() - b.startTime.getTime());\n    const processedSegments = new Set();\n    for (const segment of sortedSegments){\n        if (processedSegments.has(segment.id)) {\n            continue;\n        }\n        // Find all segments that overlap with the current one\n        const overlappingGroup = sortedSegments.filter((s)=>!processedSegments.has(s.id) && segmentsOverlap(segment, s));\n        if (overlappingGroup.length === 1) {\n            // Single event - no overlap handling needed\n            finalLayout.segmentLayouts.push({\n                segment: overlappingGroup[0],\n                left: 0,\n                width: 100 - GUTTER_WIDTH_PERCENT,\n                zIndex: 10,\n                hasOverlap: false\n            });\n            processedSegments.add(overlappingGroup[0].id);\n            continue;\n        }\n        // For overlapping events, calculate their widths and positions\n        const totalWidth = 100 - GUTTER_WIDTH_PERCENT;\n        const eventWidth = Math.max(MIN_EVENT_WIDTH_PERCENT, totalWidth / overlappingGroup.length);\n        // Sort overlapping events by start time to maintain order\n        overlappingGroup.sort((a, b)=>a.startTime.getTime() - b.startTime.getTime());\n        // Distribute events horizontally\n        overlappingGroup.forEach((groupSegment, index)=>{\n            const left = index * (totalWidth / overlappingGroup.length);\n            finalLayout.segmentLayouts.push({\n                segment: groupSegment,\n                left,\n                width: eventWidth,\n                zIndex: 10 + index,\n                hasOverlap: true\n            });\n            processedSegments.add(groupSegment.id);\n        });\n    }\n    return finalLayout;\n};\n/**\n * Checks if two events overlap in time\n */ const eventsOverlap = (event1, event2)=>{\n    const start1 = new Date(event1.start);\n    const end1 = new Date(event1.end);\n    const start2 = new Date(event2.start);\n    const end2 = new Date(event2.end);\n    return start1 < end2 && start2 < end1;\n};\n/**\n * A dedicated layout calculator for all-day events, which are laid out horizontally.\n */ const calculateAllDayLayout = function(segments) {\n    let maxVisible = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3;\n    if (segments.length <= maxVisible) {\n        return {\n            visibleSegments: segments,\n            moreCount: 0\n        };\n    }\n    // Sort by start time only to maintain natural order\n    const sorted = [\n        ...segments\n    ].sort((a, b)=>a.startTime.getTime() - b.startTime.getTime());\n    const visibleSegments = sorted.slice(0, maxVisible - 1);\n    const moreCount = segments.length - visibleSegments.length;\n    return {\n        visibleSegments,\n        moreCount\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/eventCollisionUtils.ts\n"));

/***/ })

});