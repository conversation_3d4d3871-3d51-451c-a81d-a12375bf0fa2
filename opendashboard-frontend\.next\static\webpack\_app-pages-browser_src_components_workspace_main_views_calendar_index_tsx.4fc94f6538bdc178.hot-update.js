"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx":
/*!*******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/MonthView.tsx ***!
  \*******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MonthView: function() { return /* binding */ MonthView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CalendarEventItem */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx\");\n/* harmony import */ var _NoEvents__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./NoEvents */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/NoEvents.tsx\");\n/* harmony import */ var _CalendarSideCard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./CalendarSideCard */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarSideCard.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Helper function to count events in a cell\nconst getEventsForCell = (date, events, spanningEvents)=>{\n    const cellEvents = [];\n    // Add spanning (multi-day) events that pass through this cell\n    spanningEvents.forEach((spanningEvent)=>{\n        const eventStart = new Date(spanningEvent.segment.originalEvent.start);\n        const eventEnd = new Date(spanningEvent.segment.originalEvent.end);\n        if (date >= (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(eventStart) && date <= (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(eventEnd)) {\n            cellEvents.push({\n                event: spanningEvent.segment.originalEvent,\n                isMultiDay: true,\n                row: spanningEvent.row,\n                colSpan: spanningEvent.colSpan\n            });\n        }\n    });\n    // Add single-day events for this cell\n    events.forEach((event)=>{\n        const eventStart = new Date(event.start);\n        if ((0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(date, eventStart)) {\n            cellEvents.push({\n                event,\n                isMultiDay: false\n            });\n        }\n    });\n    return {\n        visibleEvents: cellEvents.slice(0, 4),\n        hiddenCount: Math.max(0, cellEvents.length - 4)\n    };\n};\n// New helper function to process events for the entire month\nconst useMonthEvents = (weeks, events)=>{\n    _s();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const positionedEventsByWeek = new Map();\n        weeks.forEach((week, weekIndex)=>{\n            const weekStart = week[0];\n            const weekEnd = week[6];\n            const weekEvents = events.filter((event)=>{\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                return eventStart <= weekEnd && eventEnd >= weekStart;\n            });\n            const spanningEvents = [];\n            weekEvents.forEach((event)=>{\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                const startDayIndex = week.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(day, eventStart));\n                const endDayIndex = week.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(day, eventEnd));\n                const eventSpansWeek = startDayIndex !== -1 || endDayIndex !== -1 || eventStart < weekStart && eventEnd > weekEnd;\n                if (eventSpansWeek) {\n                    const start = startDayIndex !== -1 ? startDayIndex : 0;\n                    const end = endDayIndex !== -1 ? endDayIndex : 6;\n                    spanningEvents.push({\n                        event,\n                        startDayIndex: start,\n                        endDayIndex: end,\n                        colSpan: end - start + 1\n                    });\n                }\n            });\n            const positioned = [];\n            const rows = [];\n            const sortedEvents = spanningEvents.sort((a, b)=>a.startDayIndex - b.startDayIndex || b.colSpan - a.colSpan);\n            sortedEvents.forEach((event)=>{\n                let assigned = false;\n                for(let i = 0; i < rows.length; i++){\n                    const row = rows[i];\n                    if (!row.some((e)=>event.startDayIndex <= e.endDayIndex && event.endDayIndex >= e.startDayIndex)) {\n                        row.push(event);\n                        positioned.push({\n                            ...event,\n                            row: i\n                        });\n                        assigned = true;\n                        break;\n                    }\n                }\n                if (!assigned) {\n                    rows.push([\n                        event\n                    ]);\n                    positioned.push({\n                        ...event,\n                        row: rows.length - 1\n                    });\n                }\n            });\n            positionedEventsByWeek.set(weekIndex, positioned);\n        });\n        return positionedEventsByWeek;\n    }, [\n        weeks,\n        events\n    ]);\n};\n_s(useMonthEvents, \"nwk+m61qLgjDVUp4IGV/072DDN4=\");\n// Helper function to check if two events overlap\nconst hasOverlap = (a, b)=>{\n    const aStart = new Date(a.segment.originalEvent.start);\n    const aEnd = new Date(a.segment.originalEvent.end);\n    const bStart = new Date(b.segment.originalEvent.start);\n    const bEnd = new Date(b.segment.originalEvent.end);\n    return aStart <= bEnd && aEnd >= bStart;\n};\n// Helper function to position spanning events\nconst positionSpanningEvents = (events)=>{\n    const positioned = [];\n    const rows = [];\n    // Sort events by their span length (longer events first)\n    const sortedEvents = [\n        ...events\n    ].sort((a, b)=>a.colSpan === b.colSpan ? 0 : b.colSpan - a.colSpan);\n    sortedEvents.forEach((event)=>{\n        let assigned = false;\n        // Try to fit in existing rows\n        for(let i = 0; i < rows.length; i++){\n            const row = rows[i];\n            if (!row.some((e)=>hasOverlap(event, e))) {\n                row.push(event);\n                positioned.push({\n                    ...event,\n                    row: i\n                });\n                assigned = true;\n                break;\n            }\n        }\n        // If no existing row works, create a new row\n        if (!assigned) {\n            rows.push([\n                event\n            ]);\n            positioned.push({\n                ...event,\n                row: rows.length - 1\n            });\n        }\n    });\n    return positioned;\n};\nconst DayCell = (param)=>{\n    let { date, isCurrentMonth, onClick, events, spanningEvents, onEventClick, selectedEvent } = param;\n    _s1();\n    const { setNodeRef, isOver } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__.useDroppable)({\n        id: \"daycell-\".concat((0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(date, \"yyyy-MM-dd\")),\n        data: {\n            type: \"daycell\",\n            date: date\n        }\n    });\n    const { visibleEvents, hiddenCount } = getEventsForCell(date, events, spanningEvents);\n    const isCurrentDay = (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(date);\n    const MAX_VISIBLE_EVENTS = 4;\n    const ROW_HEIGHT = 28;\n    // Calculate container height\n    const maxRow = visibleEvents.reduce((max, event)=>{\n        const row = \"row\" in event ? event.row || 0 : 0;\n        return Math.max(max, row);\n    }, -1);\n    const containerHeight = hiddenCount > 0 ? MAX_VISIBLE_EVENTS * ROW_HEIGHT + 20 : (maxRow + 1) * ROW_HEIGHT;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: setNodeRef,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"group min-h-[120px] p-2 border-b border-r border-neutral-300\", \"cursor-pointer hover:bg-neutral-50 transition-colors\", !isCurrentMonth && \"bg-neutral-50\", isOver && \"bg-blue-50\"),\n        onClick: onClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center rounded-full font-semibold text-xs w-6 h-6\", isCurrentDay ? \"bg-black text-white\" : isCurrentMonth ? \"text-black hover:bg-neutral-100\" : \"text-neutral-400\"),\n                    children: (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(date, \"d\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 238,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                style: {\n                    height: \"\".concat(containerHeight, \"px\")\n                },\n                children: [\n                    visibleEvents.map((eventInfo)=>{\n                        const row = \"row\" in eventInfo ? eventInfo.row || 0 : 0;\n                        const colSpan = \"colSpan\" in eventInfo ? eventInfo.colSpan || 1 : 1;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute\",\n                            style: {\n                                top: \"\".concat(row * ROW_HEIGHT, \"px\"),\n                                left: \"2px\",\n                                width: colSpan > 1 ? \"calc(\".concat(colSpan * 100, \"% + \").concat((colSpan - 1) * 19, \"px)\") : \"calc(100% - 4px)\",\n                                zIndex: 10 + row\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__.CalendarEventItem, {\n                                event: eventInfo.event,\n                                view: \"month\",\n                                onClick: (e)=>{\n                                    e.stopPropagation();\n                                    onEventClick(eventInfo.event);\n                                },\n                                isDragging: false\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 15\n                            }, undefined)\n                        }, eventInfo.event.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 13\n                        }, undefined);\n                    }),\n                    hiddenCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-black hover:text-black font-medium text-xs cursor-pointer\",\n                        style: {\n                            position: \"absolute\",\n                            top: \"\".concat(MAX_VISIBLE_EVENTS * ROW_HEIGHT, \"px\"),\n                            left: \"2px\"\n                        },\n                        onClick: (e)=>{\n                            e.stopPropagation();\n                            onClick();\n                        },\n                        children: [\n                            \"+ \",\n                            hiddenCount,\n                            \" more\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 247,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 228,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(DayCell, \"DmJTTt6A5xWIX/faBiFge3FOLrw=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__.useDroppable\n    ];\n});\n_c = DayCell;\nconst MonthView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, setSelectedDate, openAddEventForm, canEditData, handleEventClick, activeDragData } = param;\n    _s2();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_5__.useMaybeRecord)();\n    const isInRecordTab = !!maybeRecord;\n    // Memoize month calculations\n    const monthCalculations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const monthStart = (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(selectedDate);\n        const monthEnd = (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(selectedDate);\n        const startDay = (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(monthStart, {\n            weekStartsOn: 0\n        });\n        const endDay = (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_18__[\"default\"])(monthEnd, {\n            weekStartsOn: 0\n        });\n        const days = [];\n        let day = startDay;\n        while(day <= endDay){\n            days.push(day);\n            day = (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_19__[\"default\"])(day, 1);\n        }\n        const weeks = [];\n        for(let i = 0; i < days.length; i += 7){\n            weeks.push(days.slice(i, i + 7));\n        }\n        return {\n            monthStart,\n            monthEnd,\n            startDay,\n            endDay,\n            days,\n            weeks\n        };\n    }, [\n        selectedDate\n    ]);\n    // Get all events for the visible month range\n    const monthEvents = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>events.filter((event)=>{\n            const eventStart = new Date(event.start);\n            const eventEnd = new Date(event.end);\n            return eventStart <= monthCalculations.endDay && eventEnd >= monthCalculations.startDay;\n        }), [\n        events,\n        monthCalculations.startDay,\n        monthCalculations.endDay\n    ]);\n    // Calculate spanning events for each week\n    const weeklySpanningEvents = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const spanningEventsByWeek = new Map();\n        monthCalculations.weeks.forEach((week, weekIndex)=>{\n            const weekStart = week[0];\n            const weekEnd = week[6];\n            // Filter events that span this week\n            const weekEvents = monthEvents.filter((event)=>{\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                return eventStart <= weekEnd && eventEnd >= weekStart;\n            });\n            // Process spanning events\n            const spanningEvents = [];\n            weekEvents.forEach((event)=>{\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                const startDayIndex = week.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(day, eventStart) || day > eventStart ? day : null);\n                const endDayIndex = week.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(day, eventEnd) || day > eventEnd ? day : null);\n                const start = startDayIndex !== -1 ? startDayIndex : 0;\n                const end = endDayIndex !== -1 ? endDayIndex : 6;\n                spanningEvents.push({\n                    segment: {\n                        originalEvent: event\n                    },\n                    row: 0,\n                    colSpan: end - start + 1\n                });\n            });\n            // Position spanning events in rows\n            const positioned = positionSpanningEvents(spanningEvents);\n            spanningEventsByWeek.set(weekIndex, positioned);\n        });\n        return spanningEventsByWeek;\n    }, [\n        monthEvents,\n        monthCalculations.weeks\n    ]);\n    // Render empty state when no events\n    const renderEmptyState = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-7 border-b border-neutral-300 bg-white\",\n                    children: [\n                        \"Sunday\",\n                        \"Monday\",\n                        \"Tuesday\",\n                        \"Wednesday\",\n                        \"Thursday\",\n                        \"Friday\",\n                        \"Saturday\"\n                    ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-black\", \"py-2 text-xs\"),\n                            children: dayName.substring(0, 3)\n                        }, dayName, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 397,\n                            columnNumber: 11\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 395,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NoEvents__WEBPACK_IMPORTED_MODULE_7__.NoEvents, {\n                    title: \"No events this month\",\n                    message: \"\".concat((0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(selectedDate, \"MMMM yyyy\"), \" is completely free. Start planning your month!\"),\n                    showCreateButton: canEditData,\n                    onCreate: ()=>openAddEventForm(selectedDate)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 406,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n            lineNumber: 394,\n            columnNumber: 5\n        }, undefined);\n    // Render day cell content\n    const renderDayCellContent = (day, dayEvents)=>{\n        const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n        const isCurrentDay = (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(day);\n        const MAX_VISIBLE_EVENTS = 4;\n        const ROW_HEIGHT = 28;\n        const sortedEvents = dayEvents.sort((a, b)=>a.row - b.row);\n        const visibleEvents = sortedEvents.slice(0, MAX_VISIBLE_EVENTS);\n        const hasMore = sortedEvents.length > MAX_VISIBLE_EVENTS;\n        // To fix the gap after the \"+more\" link, we must calculate the container's height\n        // instead of letting it expand. The link is placed at the start of the 5th row,\n        // and we'll give it 20px of height.\n        const maxRow = visibleEvents.reduce((max, event)=>Math.max(max, event.row), -1);\n        const containerHeight = hasMore ? MAX_VISIBLE_EVENTS * ROW_HEIGHT + 20 : (maxRow + 1) * ROW_HEIGHT;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center rounded-full font-semibold text-xs w-6 h-6\", isCurrentDay ? \"bg-black text-white\" : isCurrentMonth ? \"text-black hover:bg-neutral-100\" : \"text-neutral-400\"),\n                            children: (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(day, \"d\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 437,\n                            columnNumber: 11\n                        }, undefined),\n                        canEditData && isCurrentMonth && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            className: \"rounded-full h-6 w-6 p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-neutral-100 hidden lg:inline-flex\",\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                setTimeout(()=>openAddEventForm(day), 150);\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                className: \"h-3 w-3 text-black\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 444,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 436,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    style: {\n                        height: \"\".concat(containerHeight, \"px\")\n                    },\n                    children: [\n                        visibleEvents.map((pe)=>{\n                            var _activeDragData_payload;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute\",\n                                style: {\n                                    top: \"\".concat(pe.row * ROW_HEIGHT, \"px\"),\n                                    left: \"2px\",\n                                    width: pe.colSpan > 1 ? \"calc(\".concat(pe.colSpan * 100, \"% + \").concat((pe.colSpan - 1) * 19, \"px)\") : \"calc(100% - 4px)\",\n                                    // zIndex: pe.colSpan > 1 ? 10 : 1,\n                                    zIndex: 10 + pe.row\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__.CalendarEventItem, {\n                                    event: pe.event,\n                                    view: \"month\",\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        setSelectedEvent(pe.event.id);\n                                        handleEventClick(pe.event);\n                                    },\n                                    isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === pe.event.id\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, pe.event.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 459,\n                                columnNumber: 13\n                            }, undefined);\n                        }),\n                        hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-black hover:text-black font-medium text-xs cursor-pointer\",\n                            style: {\n                                position: \"absolute\",\n                                top: \"\".concat(MAX_VISIBLE_EVENTS * ROW_HEIGHT, \"px\"),\n                                left: \"2px\"\n                            },\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                setSelectedDate(day);\n                            },\n                            children: [\n                                \"+ \",\n                                sortedEvents.length - MAX_VISIBLE_EVENTS,\n                                \" more\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 486,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 457,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true);\n    };\n    // Render main view\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-background flex flex-col lg:flex-row\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col min-h-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        \"data-day-headers\": \"true\",\n                        className: \"grid grid-cols-7 border-b border-neutral-300 sticky top-0 z-10 bg-white\",\n                        children: [\n                            \"Sunday\",\n                            \"Monday\",\n                            \"Tuesday\",\n                            \"Wednesday\",\n                            \"Thursday\",\n                            \"Friday\",\n                            \"Saturday\"\n                        ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-black\", \"py-2 text-xs\"),\n                                children: dayName.substring(0, 3)\n                            }, dayName, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 516,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 511,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n                        className: \"flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-7 border-neutral-300 border-b\",\n                            children: monthCalculations.weeks.map((week, weekIndex)=>week.map((day, dayIndex)=>{\n                                    const weekEvents = weeklySpanningEvents.get(weekIndex) || [];\n                                    const dayEvents = weekEvents.filter((pe)=>pe.segment.originalEvent.startDayIndex === dayIndex);\n                                    const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DayCell, {\n                                        date: day,\n                                        isCurrentMonth: isCurrentMonth,\n                                        onClick: ()=>setSelectedDate(day),\n                                        events: monthEvents,\n                                        spanningEvents: weekEvents,\n                                        onEventClick: handleEventClick,\n                                        selectedEvent: selectedEvent\n                                    }, \"\".concat(weekIndex, \"-\").concat(dayIndex), false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                        lineNumber: 535,\n                                        columnNumber: 19\n                                    }, undefined);\n                                }))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 527,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 526,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 509,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarSideCard__WEBPACK_IMPORTED_MODULE_8__.CalendarSideCard, {\n                selectedDate: selectedDate,\n                events: events,\n                selectedEvent: selectedEvent,\n                setSelectedEvent: setSelectedEvent,\n                handleEventClick: handleEventClick\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 552,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 508,\n        columnNumber: 5\n    }, undefined);\n};\n_s2(MonthView, \"kPDDlaHW0EW5WI3Ne0bDTWgAARs=\", false, function() {\n    return [\n        _providers_record__WEBPACK_IMPORTED_MODULE_5__.useMaybeRecord\n    ];\n});\n_c1 = MonthView;\nfunction isMultiDay(event) {\n    return !(0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(new Date(event.start), new Date(event.end));\n}\nvar _c, _c1;\n$RefreshReg$(_c, \"DayCell\");\n$RefreshReg$(_c1, \"MonthView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx\n"));

/***/ })

});