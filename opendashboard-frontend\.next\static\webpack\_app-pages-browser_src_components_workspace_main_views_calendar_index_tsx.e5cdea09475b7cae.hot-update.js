"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx":
/*!******************************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx ***!
  \******************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CalendarEventSegment: function() { return /* binding */ CalendarEventSegment; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _utils_color__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/color */ \"(app-pages-browser)/./src/utils/color.ts\");\n/* harmony import */ var _utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/dateUtils */ \"(app-pages-browser)/./src/utils/dateUtils.ts\");\n/* harmony import */ var _utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/multiDayEventUtils */ \"(app-pages-browser)/./src/utils/multiDayEventUtils.ts\");\n/* harmony import */ var _MultiDayEventBadge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./MultiDayEventBadge */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MultiDayEventBadge.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst CalendarEventSegment = (param)=>{\n    let { segment, style, onClick, onContextMenu, view = \"month\", isEndOfEvent, isDragging } = param;\n    _s();\n    const dragRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { attributes, listeners, setNodeRef, isDragging: dndIsDragging } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_7__.useDraggable)({\n        id: \"segment-\".concat(segment.id),\n        data: {\n            type: \"segment\",\n            payload: segment\n        }\n    });\n    const combinedIsDragging = isDragging || dndIsDragging;\n    // Memoize event calculations\n    const eventDetails = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const eventHeight = (style === null || style === void 0 ? void 0 : style.height) ? parseInt(style.height.toString().replace(\"px\", \"\")) : null;\n        const showTime = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.shouldShowTimeInSegment)(segment, view);\n        const continuationText = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.getSegmentContinuationText)(segment);\n        // let formattedTime = null;\n        // if (segment.isAllDay) {\n        //   formattedTime = 'All day';\n        // } else if (showTime) {\n        //   formattedTime = formatEventTime(segment.startTime, view, { shortFormat: true });\n        // }\n        return {\n            eventSize: (0,_utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__.getEventSize)(eventHeight),\n            showTime,\n            continuationText,\n            formattedTime: showTime ? (0,_utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__.formatEventTime)(segment.startTime, view, {\n                shortFormat: true\n            }) : null\n        };\n    }, [\n        segment,\n        style,\n        view\n    ]);\n    // Memoize styling\n    const eventStyles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const denimColorInfo = (0,_utils_color__WEBPACK_IMPORTED_MODULE_3__.ColorInfo)(\"Denim\");\n        const stylingClasses = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.getSegmentStylingClasses)(segment);\n        // Extract RGB values from the rgba string and make it fully opaque\n        const rgbMatch = denimColorInfo.bg.match(/rgba?\\((\\d+),\\s*(\\d+),\\s*(\\d+)/);\n        const opaqueBackground = rgbMatch ? \"rgb(\".concat(rgbMatch[1], \", \").concat(rgbMatch[2], \", \").concat(rgbMatch[3], \")\") : denimColorInfo.bg;\n        return {\n            style: {\n                ...style,\n                backgroundColor: opaqueBackground,\n                minHeight: \"24px\",\n                // Add subtle shadow for better visual depth\n                boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)\",\n                opacity: combinedIsDragging ? 0.5 : 1\n            },\n            classes: stylingClasses\n        };\n    }, [\n        style,\n        segment,\n        combinedIsDragging\n    ]);\n    // Memoize classes\n    const eventClasses = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const baseClasses = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"select-none text-black text-xs overflow-hidden relative\", !combinedIsDragging && \"cursor-pointer\", eventStyles.classes.roundedCorners, eventStyles.classes.continuationIndicator, eventStyles.classes.opacity, \"p-1\");\n        // Month view or small events\n        if (view === \"month\" || eventDetails.eventSize === \"small\") {\n            return {\n                baseClasses,\n                containerClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center space-x-1 flex-nowrap\"),\n                titleClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-medium truncate leading-tight text-xs overflow-hidden\", segment.isMultiDay ? \"max-w-[60%]\" : \"max-w-[70%]\"),\n                timeClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"opacity-75 text-xs flex-shrink-0 text-[0.65rem]\"),\n                continuationClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-xs opacity-60 flex-shrink-0 text-[0.6rem]\")\n            };\n        }\n        // Day and Week views for medium/large events\n        return {\n            baseClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(baseClasses, \"p-2\"),\n            containerClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col\", \"space-y-0.5\"),\n            titleClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-medium truncate leading-tight text-xs overflow-hidden\"),\n            timeClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"opacity-75 text-xs\"),\n            continuationClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-xs opacity-60\")\n        };\n    }, [\n        eventDetails,\n        view,\n        segment.isMultiDay,\n        eventStyles.classes,\n        combinedIsDragging\n    ]);\n    // Render event content based on view and size\n    const renderEventContent = ()=>{\n        const event = segment.originalEvent;\n        // Month view or small events - horizontal layout\n        if (view === \"month\" || eventDetails.eventSize === \"small\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: eventClasses.containerClasses,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: eventClasses.titleClasses,\n                        children: event.title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, undefined),\n                    eventDetails.showTime && eventDetails.formattedTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: eventClasses.timeClasses,\n                        children: eventDetails.formattedTime\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 13\n                    }, undefined),\n                    segment.isMultiDay && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MultiDayEventBadge__WEBPACK_IMPORTED_MODULE_6__.MultiDayEventBadge, {\n                        segment: segment,\n                        view: view,\n                        size: \"small\",\n                        className: eventClasses.continuationClasses,\n                        isEndOfEvent: isEndOfEvent\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                lineNumber: 148,\n                columnNumber: 9\n            }, undefined);\n        }\n        // Day and Week views - vertical layout\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: eventClasses.containerClasses,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: eventClasses.titleClasses,\n                    children: [\n                        event.title,\n                        segment.isMultiDay && !eventDetails.showTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MultiDayEventBadge__WEBPACK_IMPORTED_MODULE_6__.ContinuationArrow, {\n                            direction: segment.isFirstSegment ? \"right\" : segment.isLastSegment ? \"left\" : \"both\",\n                            className: \"ml-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 9\n                }, undefined),\n                (eventDetails.showTime || segment.isAllDay) && eventDetails.formattedTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: eventClasses.timeClasses,\n                    children: [\n                        eventDetails.formattedTime,\n                        segment.isMultiDay && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MultiDayEventBadge__WEBPACK_IMPORTED_MODULE_6__.ContinuationArrow, {\n                            direction: segment.isFirstSegment ? \"right\" : segment.isLastSegment ? \"left\" : \"both\",\n                            className: \"ml-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 11\n                }, undefined),\n                segment.isMultiDay && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: eventClasses.continuationClasses,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MultiDayEventBadge__WEBPACK_IMPORTED_MODULE_6__.MultiDayEventBadge, {\n                        segment: segment,\n                        view: view,\n                        size: eventDetails.eventSize === \"large\" ? \"medium\" : \"small\",\n                        isEndOfEvent: isEndOfEvent\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n            lineNumber: 170,\n            columnNumber: 7\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"event-\".concat(segment.originalEvent.id),\n        ref: (node)=>{\n            setNodeRef(node);\n            dragRef.current = node;\n        },\n        style: eventStyles.style,\n        className: eventClasses.baseClasses,\n        onClick: onClick,\n        onContextMenu: onContextMenu,\n        ...listeners,\n        ...attributes,\n        children: renderEventContent()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n        lineNumber: 206,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CalendarEventSegment, \"kiz8nz2pAp5RwnBQHmiQwwayVv8=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_7__.useDraggable\n    ];\n});\n_c = CalendarEventSegment;\nvar _c;\n$RefreshReg$(_c, \"CalendarEventSegment\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3dvcmtzcGFjZS9tYWluL3ZpZXdzL2NhbGVuZGFyL2NvbXBvbmVudHMvQ2FsZW5kYXJFdmVudFNlZ21lbnQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUErQztBQUNkO0FBRVM7QUFJZjtBQU1TO0FBQ3lDO0FBQ2hDO0FBRXRDLE1BQU1hLHVCQUF1QjtRQUFDLEVBQ25DQyxPQUFPLEVBQ1BDLEtBQUssRUFDTEMsT0FBTyxFQUNQQyxhQUFhLEVBQ2JDLE9BQU8sT0FBTyxFQUNkQyxZQUFZLEVBQ1pDLFVBQVUsRUFTWDs7SUFDQyxNQUFNQyxVQUFVbkIsNkNBQU1BLENBQWlCO0lBQ3ZDLE1BQU0sRUFBRW9CLFVBQVUsRUFBRUMsU0FBUyxFQUFFQyxVQUFVLEVBQUVKLFlBQVlLLGFBQWEsRUFBRSxHQUFHYiwyREFBWUEsQ0FBQztRQUNwRmMsSUFBSSxXQUFzQixPQUFYWixRQUFRWSxFQUFFO1FBQ3pCQyxNQUFNO1lBQ0pDLE1BQU07WUFDTkMsU0FBU2Y7UUFDWDtJQUNGO0lBRUEsTUFBTWdCLHFCQUFxQlYsY0FBY0s7SUFFekMsNkJBQTZCO0lBQzdCLE1BQU1NLGVBQWU5Qiw4Q0FBT0EsQ0FBQztRQUMzQixNQUFNK0IsY0FBY2pCLENBQUFBLGtCQUFBQSw0QkFBQUEsTUFBT2tCLE1BQU0sSUFBR0MsU0FBU25CLE1BQU1rQixNQUFNLENBQUNFLFFBQVEsR0FBR0MsT0FBTyxDQUFDLE1BQU0sT0FBTztRQUMxRixNQUFNQyxXQUFXN0Isa0ZBQXVCQSxDQUFDTSxTQUFTSTtRQUNsRCxNQUFNb0IsbUJBQW1CN0IscUZBQTBCQSxDQUFDSztRQUVwRCw0QkFBNEI7UUFDNUIsMEJBQTBCO1FBQzFCLCtCQUErQjtRQUMvQix5QkFBeUI7UUFDekIscUZBQXFGO1FBQ3JGLElBQUk7UUFFSixPQUFPO1lBQ0x5QixXQUFXakMsOERBQVlBLENBQUMwQjtZQUN4Qks7WUFDQUM7WUFDQUUsZUFBZUgsV0FBV2hDLGlFQUFlQSxDQUFDUyxRQUFRMkIsU0FBUyxFQUFFdkIsTUFBTTtnQkFBRXdCLGFBQWE7WUFBSyxLQUFLO1FBQzlGO0lBQ0YsR0FBRztRQUFDNUI7UUFBU0M7UUFBT0c7S0FBSztJQUV6QixrQkFBa0I7SUFDbEIsTUFBTXlCLGNBQWMxQyw4Q0FBT0EsQ0FBQztRQUMxQixNQUFNMkMsaUJBQWlCeEMsdURBQVNBLENBQUM7UUFDakMsTUFBTXlDLGlCQUFpQnRDLG1GQUF3QkEsQ0FBQ087UUFFaEQsbUVBQW1FO1FBQ25FLE1BQU1nQyxXQUFXRixlQUFlRyxFQUFFLENBQUNDLEtBQUssQ0FBQztRQUN6QyxNQUFNQyxtQkFBbUJILFdBQ3JCLE9BQXVCQSxPQUFoQkEsUUFBUSxDQUFDLEVBQUUsRUFBQyxNQUFvQkEsT0FBaEJBLFFBQVEsQ0FBQyxFQUFFLEVBQUMsTUFBZ0IsT0FBWkEsUUFBUSxDQUFDLEVBQUUsRUFBQyxPQUNuREYsZUFBZUcsRUFBRTtRQUVyQixPQUFPO1lBQ0xoQyxPQUFPO2dCQUNMLEdBQUdBLEtBQUs7Z0JBQ1JtQyxpQkFBaUJEO2dCQUNqQkUsV0FBVztnQkFDWCw0Q0FBNEM7Z0JBQzVDQyxXQUFXO2dCQUNYQyxTQUFTdkIscUJBQXFCLE1BQU07WUFDdEM7WUFDQXdCLFNBQVNUO1FBQ1g7SUFDRixHQUFHO1FBQUM5QjtRQUFPRDtRQUFTZ0I7S0FBbUI7SUFFdkMsa0JBQWtCO0lBQ2xCLE1BQU15QixlQUFldEQsOENBQU9BLENBQUM7UUFDM0IsTUFBTXVELGNBQWNyRCw4Q0FBRUEsQ0FDcEIsMkRBQ0EsQ0FBQzJCLHNCQUFzQixrQkFDdkJhLFlBQVlXLE9BQU8sQ0FBQ0csY0FBYyxFQUNsQ2QsWUFBWVcsT0FBTyxDQUFDSSxxQkFBcUIsRUFDekNmLFlBQVlXLE9BQU8sQ0FBQ0QsT0FBTyxFQUMzQjtRQUdGLDZCQUE2QjtRQUM3QixJQUFJbkMsU0FBUyxXQUFXYSxhQUFhUSxTQUFTLEtBQUssU0FBUztZQUMxRCxPQUFPO2dCQUNMaUI7Z0JBQ0FHLGtCQUFrQnhELDhDQUFFQSxDQUNsQjtnQkFFRnlELGNBQWN6RCw4Q0FBRUEsQ0FDZCw4REFDQVcsUUFBUStDLFVBQVUsR0FBRyxnQkFBZ0I7Z0JBRXZDQyxhQUFhM0QsOENBQUVBLENBQ2I7Z0JBRUY0RCxxQkFBcUI1RCw4Q0FBRUEsQ0FDckI7WUFFSjtRQUNGO1FBRUEsNkNBQTZDO1FBQzdDLE9BQU87WUFDTHFELGFBQWFyRCw4Q0FBRUEsQ0FBQ3FELGFBQWE7WUFDN0JHLGtCQUFrQnhELDhDQUFFQSxDQUNsQixpQkFDQTtZQUVGeUQsY0FBY3pELDhDQUFFQSxDQUNkO1lBRUYyRCxhQUFhM0QsOENBQUVBLENBQ2I7WUFFRjRELHFCQUFxQjVELDhDQUFFQSxDQUNyQjtRQUVKO0lBQ0YsR0FBRztRQUFDNEI7UUFBY2I7UUFBTUosUUFBUStDLFVBQVU7UUFBRWxCLFlBQVlXLE9BQU87UUFBRXhCO0tBQW1CO0lBRXBGLDhDQUE4QztJQUM5QyxNQUFNa0MscUJBQXFCO1FBQ3pCLE1BQU1DLFFBQVFuRCxRQUFRb0QsYUFBYTtRQUVuQyxpREFBaUQ7UUFDakQsSUFBSWhELFNBQVMsV0FBV2EsYUFBYVEsU0FBUyxLQUFLLFNBQVM7WUFDMUQscUJBQ0UsOERBQUM0QjtnQkFBSUMsV0FBV2IsYUFBYUksZ0JBQWdCOztrQ0FDM0MsOERBQUNVO3dCQUFLRCxXQUFXYixhQUFhSyxZQUFZO2tDQUFHSyxNQUFNSyxLQUFLOzs7Ozs7b0JBQ3ZEdkMsYUFBYU0sUUFBUSxJQUFJTixhQUFhUyxhQUFhLGtCQUNsRCw4REFBQzZCO3dCQUFLRCxXQUFXYixhQUFhTyxXQUFXO2tDQUN0Qy9CLGFBQWFTLGFBQWE7Ozs7OztvQkFHOUIxQixRQUFRK0MsVUFBVSxrQkFDakIsOERBQUNuRCxtRUFBa0JBO3dCQUNqQkksU0FBU0E7d0JBQ1RJLE1BQU1BO3dCQUNOcUQsTUFBSzt3QkFDTEgsV0FBV2IsYUFBYVEsbUJBQW1CO3dCQUMzQzVDLGNBQWNBOzs7Ozs7Ozs7Ozs7UUFLeEI7UUFFQSx1Q0FBdUM7UUFDdkMscUJBQ0UsOERBQUNnRDtZQUFJQyxXQUFXYixhQUFhSSxnQkFBZ0I7OzhCQUMzQyw4REFBQ1E7b0JBQUlDLFdBQVdiLGFBQWFLLFlBQVk7O3dCQUN0Q0ssTUFBTUssS0FBSzt3QkFDWHhELFFBQVErQyxVQUFVLElBQUksQ0FBQzlCLGFBQWFNLFFBQVEsa0JBQzNDLDhEQUFDMUIsa0VBQWlCQTs0QkFDaEI2RCxXQUFXMUQsUUFBUTJELGNBQWMsR0FBRyxVQUFVM0QsUUFBUTRELGFBQWEsR0FBRyxTQUFTOzRCQUMvRU4sV0FBVTs7Ozs7Ozs7Ozs7O2dCQUlkckMsQ0FBQUEsYUFBYU0sUUFBUSxJQUFJdkIsUUFBUTZELFFBQVEsS0FBSzVDLGFBQWFTLGFBQWEsa0JBQ3hFLDhEQUFDMkI7b0JBQUlDLFdBQVdiLGFBQWFPLFdBQVc7O3dCQUNyQy9CLGFBQWFTLGFBQWE7d0JBQzFCMUIsUUFBUStDLFVBQVUsa0JBQ2pCLDhEQUFDbEQsa0VBQWlCQTs0QkFDaEI2RCxXQUFXMUQsUUFBUTJELGNBQWMsR0FBRyxVQUFVM0QsUUFBUTRELGFBQWEsR0FBRyxTQUFTOzRCQUMvRU4sV0FBVTs7Ozs7Ozs7Ozs7O2dCQUtqQnRELFFBQVErQyxVQUFVLGtCQUNqQiw4REFBQ007b0JBQUlDLFdBQVdiLGFBQWFRLG1CQUFtQjs4QkFDOUMsNEVBQUNyRCxtRUFBa0JBO3dCQUNqQkksU0FBU0E7d0JBQ1RJLE1BQU1BO3dCQUNOcUQsTUFBTXhDLGFBQWFRLFNBQVMsS0FBSyxVQUFVLFdBQVc7d0JBQ3REcEIsY0FBY0E7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBTTFCO0lBRUEscUJBQ0UsOERBQUNnRDtRQUNDekMsSUFBSSxTQUFrQyxPQUF6QlosUUFBUW9ELGFBQWEsQ0FBQ3hDLEVBQUU7UUFDckNrRCxLQUFLLENBQUNDO1lBQ0pyRCxXQUFXcUQ7WUFDVnhELFFBQTBEeUQsT0FBTyxHQUFHRDtRQUN2RTtRQUNBOUQsT0FBTzRCLFlBQVk1QixLQUFLO1FBQ3hCcUQsV0FBV2IsYUFBYUMsV0FBVztRQUNuQ3hDLFNBQVNBO1FBQ1RDLGVBQWVBO1FBQ2QsR0FBR00sU0FBUztRQUNaLEdBQUdELFVBQVU7a0JBRWIwQzs7Ozs7O0FBR1AsRUFBRTtHQTVNV25EOztRQWtCOERELHVEQUFZQTs7O0tBbEIxRUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvd29ya3NwYWNlL21haW4vdmlld3MvY2FsZW5kYXIvY29tcG9uZW50cy9DYWxlbmRhckV2ZW50U2VnbWVudC50c3g/YzgxNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QsIHsgdXNlTWVtbywgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgY24gfSBmcm9tICdAL2xpYi91dGlscyc7XG5pbXBvcnQgeyBDYWxlbmRhckV2ZW50IH0gZnJvbSAnQC90eXBpbmdzL3BhZ2UnO1xuaW1wb3J0IHsgQ29sb3JJbmZvIH0gZnJvbSAnQC91dGlscy9jb2xvcic7XG5pbXBvcnQgeyBcbiAgZm9ybWF0RXZlbnRUaW1lLCBcbiAgZ2V0RXZlbnRTaXplIFxufSBmcm9tICdAL3V0aWxzL2RhdGVVdGlscyc7XG5pbXBvcnQgeyBcbiAgRXZlbnRTZWdtZW50LCBcbiAgZ2V0U2VnbWVudFN0eWxpbmdDbGFzc2VzLCBcbiAgc2hvdWxkU2hvd1RpbWVJblNlZ21lbnQsIFxuICBnZXRTZWdtZW50Q29udGludWF0aW9uVGV4dCBcbn0gZnJvbSAnQC91dGlscy9tdWx0aURheUV2ZW50VXRpbHMnO1xuaW1wb3J0IHsgTXVsdGlEYXlFdmVudEJhZGdlLCBDb250aW51YXRpb25BcnJvdyB9IGZyb20gJy4vTXVsdGlEYXlFdmVudEJhZGdlJztcbmltcG9ydCB7IHVzZURyYWdnYWJsZSB9IGZyb20gJ0BkbmQta2l0L2NvcmUnO1xuXG5leHBvcnQgY29uc3QgQ2FsZW5kYXJFdmVudFNlZ21lbnQgPSAoe1xuICBzZWdtZW50LFxuICBzdHlsZSxcbiAgb25DbGljayxcbiAgb25Db250ZXh0TWVudSxcbiAgdmlldyA9ICdtb250aCcsXG4gIGlzRW5kT2ZFdmVudCxcbiAgaXNEcmFnZ2luZ1xufToge1xuICBzZWdtZW50OiBFdmVudFNlZ21lbnQ7XG4gIHN0eWxlPzogUmVhY3QuQ1NTUHJvcGVydGllcztcbiAgb25DbGljazogKGU6IFJlYWN0Lk1vdXNlRXZlbnQpID0+IHZvaWQ7XG4gIG9uQ29udGV4dE1lbnU/OiAoZTogUmVhY3QuTW91c2VFdmVudCkgPT4gdm9pZDtcbiAgdmlldz86ICdkYXknIHwgJ3dlZWsnIHwgJ21vbnRoJztcbiAgaXNFbmRPZkV2ZW50PzogYm9vbGVhbjtcbiAgaXNEcmFnZ2luZz86IGJvb2xlYW47XG59KSA9PiB7XG4gIGNvbnN0IGRyYWdSZWYgPSB1c2VSZWY8SFRNTERpdkVsZW1lbnQ+KG51bGwpO1xuICBjb25zdCB7IGF0dHJpYnV0ZXMsIGxpc3RlbmVycywgc2V0Tm9kZVJlZiwgaXNEcmFnZ2luZzogZG5kSXNEcmFnZ2luZyB9ID0gdXNlRHJhZ2dhYmxlKHtcbiAgICBpZDogYHNlZ21lbnQtJHtzZWdtZW50LmlkfWAsXG4gICAgZGF0YToge1xuICAgICAgdHlwZTogJ3NlZ21lbnQnLFxuICAgICAgcGF5bG9hZDogc2VnbWVudCxcbiAgICB9LFxuICB9KTtcblxuICBjb25zdCBjb21iaW5lZElzRHJhZ2dpbmcgPSBpc0RyYWdnaW5nIHx8IGRuZElzRHJhZ2dpbmc7XG5cbiAgLy8gTWVtb2l6ZSBldmVudCBjYWxjdWxhdGlvbnNcbiAgY29uc3QgZXZlbnREZXRhaWxzID0gdXNlTWVtbygoKSA9PiB7XG4gICAgY29uc3QgZXZlbnRIZWlnaHQgPSBzdHlsZT8uaGVpZ2h0ID8gcGFyc2VJbnQoc3R5bGUuaGVpZ2h0LnRvU3RyaW5nKCkucmVwbGFjZSgncHgnLCAnJykpIDogbnVsbDtcbiAgICBjb25zdCBzaG93VGltZSA9IHNob3VsZFNob3dUaW1lSW5TZWdtZW50KHNlZ21lbnQsIHZpZXcpO1xuICAgIGNvbnN0IGNvbnRpbnVhdGlvblRleHQgPSBnZXRTZWdtZW50Q29udGludWF0aW9uVGV4dChzZWdtZW50KTtcbiAgICBcbiAgICAvLyBsZXQgZm9ybWF0dGVkVGltZSA9IG51bGw7XG4gICAgLy8gaWYgKHNlZ21lbnQuaXNBbGxEYXkpIHtcbiAgICAvLyAgIGZvcm1hdHRlZFRpbWUgPSAnQWxsIGRheSc7XG4gICAgLy8gfSBlbHNlIGlmIChzaG93VGltZSkge1xuICAgIC8vICAgZm9ybWF0dGVkVGltZSA9IGZvcm1hdEV2ZW50VGltZShzZWdtZW50LnN0YXJ0VGltZSwgdmlldywgeyBzaG9ydEZvcm1hdDogdHJ1ZSB9KTtcbiAgICAvLyB9XG5cbiAgICByZXR1cm4geyBcbiAgICAgIGV2ZW50U2l6ZTogZ2V0RXZlbnRTaXplKGV2ZW50SGVpZ2h0KSxcbiAgICAgIHNob3dUaW1lLFxuICAgICAgY29udGludWF0aW9uVGV4dCxcbiAgICAgIGZvcm1hdHRlZFRpbWU6IHNob3dUaW1lID8gZm9ybWF0RXZlbnRUaW1lKHNlZ21lbnQuc3RhcnRUaW1lLCB2aWV3LCB7IHNob3J0Rm9ybWF0OiB0cnVlIH0pIDogbnVsbFxuICAgIH07XG4gIH0sIFtzZWdtZW50LCBzdHlsZSwgdmlld10pO1xuXG4gIC8vIE1lbW9pemUgc3R5bGluZ1xuICBjb25zdCBldmVudFN0eWxlcyA9IHVzZU1lbW8oKCkgPT4ge1xuICAgIGNvbnN0IGRlbmltQ29sb3JJbmZvID0gQ29sb3JJbmZvKCdEZW5pbScpO1xuICAgIGNvbnN0IHN0eWxpbmdDbGFzc2VzID0gZ2V0U2VnbWVudFN0eWxpbmdDbGFzc2VzKHNlZ21lbnQpO1xuICAgIFxuICAgIC8vIEV4dHJhY3QgUkdCIHZhbHVlcyBmcm9tIHRoZSByZ2JhIHN0cmluZyBhbmQgbWFrZSBpdCBmdWxseSBvcGFxdWVcbiAgICBjb25zdCByZ2JNYXRjaCA9IGRlbmltQ29sb3JJbmZvLmJnLm1hdGNoKC9yZ2JhP1xcKChcXGQrKSxcXHMqKFxcZCspLFxccyooXFxkKykvKTtcbiAgICBjb25zdCBvcGFxdWVCYWNrZ3JvdW5kID0gcmdiTWF0Y2ggXG4gICAgICA/IGByZ2IoJHtyZ2JNYXRjaFsxXX0sICR7cmdiTWF0Y2hbMl19LCAke3JnYk1hdGNoWzNdfSlgXG4gICAgICA6IGRlbmltQ29sb3JJbmZvLmJnO1xuICAgIFxuICAgIHJldHVybiB7XG4gICAgICBzdHlsZToge1xuICAgICAgICAuLi5zdHlsZSxcbiAgICAgICAgYmFja2dyb3VuZENvbG9yOiBvcGFxdWVCYWNrZ3JvdW5kLFxuICAgICAgICBtaW5IZWlnaHQ6ICcyNHB4JywgLy8gQWRqdXN0ZWQgZm9yIGNvbnNpc3RlbmN5XG4gICAgICAgIC8vIEFkZCBzdWJ0bGUgc2hhZG93IGZvciBiZXR0ZXIgdmlzdWFsIGRlcHRoXG4gICAgICAgIGJveFNoYWRvdzogJzAgMXB4IDNweCByZ2JhKDAsIDAsIDAsIDAuMTIpLCAwIDFweCAycHggcmdiYSgwLCAwLCAwLCAwLjI0KScsXG4gICAgICAgIG9wYWNpdHk6IGNvbWJpbmVkSXNEcmFnZ2luZyA/IDAuNSA6IDEsXG4gICAgICB9LFxuICAgICAgY2xhc3Nlczogc3R5bGluZ0NsYXNzZXNcbiAgICB9O1xuICB9LCBbc3R5bGUsIHNlZ21lbnQsIGNvbWJpbmVkSXNEcmFnZ2luZ10pO1xuXG4gIC8vIE1lbW9pemUgY2xhc3Nlc1xuICBjb25zdCBldmVudENsYXNzZXMgPSB1c2VNZW1vKCgpID0+IHtcbiAgICBjb25zdCBiYXNlQ2xhc3NlcyA9IGNuKFxuICAgICAgXCJzZWxlY3Qtbm9uZSB0ZXh0LWJsYWNrIHRleHQteHMgb3ZlcmZsb3ctaGlkZGVuIHJlbGF0aXZlXCIsXG4gICAgICAhY29tYmluZWRJc0RyYWdnaW5nICYmIFwiY3Vyc29yLXBvaW50ZXJcIixcbiAgICAgIGV2ZW50U3R5bGVzLmNsYXNzZXMucm91bmRlZENvcm5lcnMsXG4gICAgICBldmVudFN0eWxlcy5jbGFzc2VzLmNvbnRpbnVhdGlvbkluZGljYXRvcixcbiAgICAgIGV2ZW50U3R5bGVzLmNsYXNzZXMub3BhY2l0eSxcbiAgICAgIFwicC0xXCIsXG4gICAgKTtcblxuICAgIC8vIE1vbnRoIHZpZXcgb3Igc21hbGwgZXZlbnRzXG4gICAgaWYgKHZpZXcgPT09ICdtb250aCcgfHwgZXZlbnREZXRhaWxzLmV2ZW50U2l6ZSA9PT0gJ3NtYWxsJykge1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgYmFzZUNsYXNzZXMsXG4gICAgICAgIGNvbnRhaW5lckNsYXNzZXM6IGNuKFxuICAgICAgICAgIFwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0xIGZsZXgtbm93cmFwXCJcbiAgICAgICAgKSxcbiAgICAgICAgdGl0bGVDbGFzc2VzOiBjbihcbiAgICAgICAgICBcImZvbnQtbWVkaXVtIHRydW5jYXRlIGxlYWRpbmctdGlnaHQgdGV4dC14cyBvdmVyZmxvdy1oaWRkZW5cIixcbiAgICAgICAgICBzZWdtZW50LmlzTXVsdGlEYXkgPyBcIm1heC13LVs2MCVdXCIgOiBcIm1heC13LVs3MCVdXCJcbiAgICAgICAgKSxcbiAgICAgICAgdGltZUNsYXNzZXM6IGNuKFxuICAgICAgICAgIFwib3BhY2l0eS03NSB0ZXh0LXhzIGZsZXgtc2hyaW5rLTAgdGV4dC1bMC42NXJlbV1cIlxuICAgICAgICApLFxuICAgICAgICBjb250aW51YXRpb25DbGFzc2VzOiBjbihcbiAgICAgICAgICBcInRleHQteHMgb3BhY2l0eS02MCBmbGV4LXNocmluay0wIHRleHQtWzAuNnJlbV1cIlxuICAgICAgICApXG4gICAgICB9O1xuICAgIH1cblxuICAgIC8vIERheSBhbmQgV2VlayB2aWV3cyBmb3IgbWVkaXVtL2xhcmdlIGV2ZW50c1xuICAgIHJldHVybiB7XG4gICAgICBiYXNlQ2xhc3NlczogY24oYmFzZUNsYXNzZXMsIFwicC0yXCIpLFxuICAgICAgY29udGFpbmVyQ2xhc3NlczogY24oXG4gICAgICAgIFwiZmxleCBmbGV4LWNvbFwiLFxuICAgICAgICBcInNwYWNlLXktMC41XCJcbiAgICAgICksXG4gICAgICB0aXRsZUNsYXNzZXM6IGNuKFxuICAgICAgICBcImZvbnQtbWVkaXVtIHRydW5jYXRlIGxlYWRpbmctdGlnaHQgdGV4dC14cyBvdmVyZmxvdy1oaWRkZW5cIlxuICAgICAgKSxcbiAgICAgIHRpbWVDbGFzc2VzOiBjbihcbiAgICAgICAgXCJvcGFjaXR5LTc1IHRleHQteHNcIlxuICAgICAgKSxcbiAgICAgIGNvbnRpbnVhdGlvbkNsYXNzZXM6IGNuKFxuICAgICAgICBcInRleHQteHMgb3BhY2l0eS02MFwiXG4gICAgICApXG4gICAgfTtcbiAgfSwgW2V2ZW50RGV0YWlscywgdmlldywgc2VnbWVudC5pc011bHRpRGF5LCBldmVudFN0eWxlcy5jbGFzc2VzLCBjb21iaW5lZElzRHJhZ2dpbmddKTtcblxuICAvLyBSZW5kZXIgZXZlbnQgY29udGVudCBiYXNlZCBvbiB2aWV3IGFuZCBzaXplXG4gIGNvbnN0IHJlbmRlckV2ZW50Q29udGVudCA9ICgpID0+IHtcbiAgICBjb25zdCBldmVudCA9IHNlZ21lbnQub3JpZ2luYWxFdmVudDtcbiAgICBcbiAgICAvLyBNb250aCB2aWV3IG9yIHNtYWxsIGV2ZW50cyAtIGhvcml6b250YWwgbGF5b3V0XG4gICAgaWYgKHZpZXcgPT09ICdtb250aCcgfHwgZXZlbnREZXRhaWxzLmV2ZW50U2l6ZSA9PT0gJ3NtYWxsJykge1xuICAgICAgcmV0dXJuIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9e2V2ZW50Q2xhc3Nlcy5jb250YWluZXJDbGFzc2VzfT5cbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2V2ZW50Q2xhc3Nlcy50aXRsZUNsYXNzZXN9PntldmVudC50aXRsZX08L3NwYW4+XG4gICAgICAgICAge2V2ZW50RGV0YWlscy5zaG93VGltZSAmJiBldmVudERldGFpbHMuZm9ybWF0dGVkVGltZSAmJiAoXG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2V2ZW50Q2xhc3Nlcy50aW1lQ2xhc3Nlc30+XG4gICAgICAgICAgICAgIHtldmVudERldGFpbHMuZm9ybWF0dGVkVGltZX1cbiAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICApfVxuICAgICAgICAgIHtzZWdtZW50LmlzTXVsdGlEYXkgJiYgKFxuICAgICAgICAgICAgPE11bHRpRGF5RXZlbnRCYWRnZVxuICAgICAgICAgICAgICBzZWdtZW50PXtzZWdtZW50fVxuICAgICAgICAgICAgICB2aWV3PXt2aWV3fVxuICAgICAgICAgICAgICBzaXplPVwic21hbGxcIlxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2V2ZW50Q2xhc3Nlcy5jb250aW51YXRpb25DbGFzc2VzfVxuICAgICAgICAgICAgICBpc0VuZE9mRXZlbnQ9e2lzRW5kT2ZFdmVudH1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICApO1xuICAgIH1cblxuICAgIC8vIERheSBhbmQgV2VlayB2aWV3cyAtIHZlcnRpY2FsIGxheW91dFxuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT17ZXZlbnRDbGFzc2VzLmNvbnRhaW5lckNsYXNzZXN9PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT17ZXZlbnRDbGFzc2VzLnRpdGxlQ2xhc3Nlc30+XG4gICAgICAgICAge2V2ZW50LnRpdGxlfVxuICAgICAgICAgIHtzZWdtZW50LmlzTXVsdGlEYXkgJiYgIWV2ZW50RGV0YWlscy5zaG93VGltZSAmJiAoXG4gICAgICAgICAgICA8Q29udGludWF0aW9uQXJyb3dcbiAgICAgICAgICAgICAgZGlyZWN0aW9uPXtzZWdtZW50LmlzRmlyc3RTZWdtZW50ID8gJ3JpZ2h0JyA6IHNlZ21lbnQuaXNMYXN0U2VnbWVudCA/ICdsZWZ0JyA6ICdib3RoJ31cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibWwtMVwiXG4gICAgICAgICAgICAvPlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgICB7KGV2ZW50RGV0YWlscy5zaG93VGltZSB8fCBzZWdtZW50LmlzQWxsRGF5KSAmJiBldmVudERldGFpbHMuZm9ybWF0dGVkVGltZSAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2V2ZW50Q2xhc3Nlcy50aW1lQ2xhc3Nlc30+XG4gICAgICAgICAgICB7ZXZlbnREZXRhaWxzLmZvcm1hdHRlZFRpbWV9XG4gICAgICAgICAgICB7c2VnbWVudC5pc011bHRpRGF5ICYmIChcbiAgICAgICAgICAgICAgPENvbnRpbnVhdGlvbkFycm93XG4gICAgICAgICAgICAgICAgZGlyZWN0aW9uPXtzZWdtZW50LmlzRmlyc3RTZWdtZW50ID8gJ3JpZ2h0JyA6IHNlZ21lbnQuaXNMYXN0U2VnbWVudCA/ICdsZWZ0JyA6ICdib3RoJ31cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtbC0xXCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICAgIHtzZWdtZW50LmlzTXVsdGlEYXkgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtldmVudENsYXNzZXMuY29udGludWF0aW9uQ2xhc3Nlc30+XG4gICAgICAgICAgICA8TXVsdGlEYXlFdmVudEJhZGdlXG4gICAgICAgICAgICAgIHNlZ21lbnQ9e3NlZ21lbnR9XG4gICAgICAgICAgICAgIHZpZXc9e3ZpZXd9XG4gICAgICAgICAgICAgIHNpemU9e2V2ZW50RGV0YWlscy5ldmVudFNpemUgPT09ICdsYXJnZScgPyAnbWVkaXVtJyA6ICdzbWFsbCd9XG4gICAgICAgICAgICAgIGlzRW5kT2ZFdmVudD17aXNFbmRPZkV2ZW50fVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2XG4gICAgICBpZD17YGV2ZW50LSR7c2VnbWVudC5vcmlnaW5hbEV2ZW50LmlkfWB9XG4gICAgICByZWY9eyhub2RlKSA9PiB7XG4gICAgICAgIHNldE5vZGVSZWYobm9kZSk7XG4gICAgICAgIChkcmFnUmVmIGFzIFJlYWN0Lk11dGFibGVSZWZPYmplY3Q8SFRNTERpdkVsZW1lbnQgfCBudWxsPikuY3VycmVudCA9IG5vZGU7XG4gICAgICB9fVxuICAgICAgc3R5bGU9e2V2ZW50U3R5bGVzLnN0eWxlfVxuICAgICAgY2xhc3NOYW1lPXtldmVudENsYXNzZXMuYmFzZUNsYXNzZXN9XG4gICAgICBvbkNsaWNrPXtvbkNsaWNrfVxuICAgICAgb25Db250ZXh0TWVudT17b25Db250ZXh0TWVudX1cbiAgICAgIHsuLi5saXN0ZW5lcnN9XG4gICAgICB7Li4uYXR0cmlidXRlc31cbiAgICA+XG4gICAgICB7cmVuZGVyRXZlbnRDb250ZW50KCl9XG4gICAgPC9kaXY+XG4gICk7XG59OyAiXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VNZW1vIiwidXNlUmVmIiwiY24iLCJDb2xvckluZm8iLCJmb3JtYXRFdmVudFRpbWUiLCJnZXRFdmVudFNpemUiLCJnZXRTZWdtZW50U3R5bGluZ0NsYXNzZXMiLCJzaG91bGRTaG93VGltZUluU2VnbWVudCIsImdldFNlZ21lbnRDb250aW51YXRpb25UZXh0IiwiTXVsdGlEYXlFdmVudEJhZGdlIiwiQ29udGludWF0aW9uQXJyb3ciLCJ1c2VEcmFnZ2FibGUiLCJDYWxlbmRhckV2ZW50U2VnbWVudCIsInNlZ21lbnQiLCJzdHlsZSIsIm9uQ2xpY2siLCJvbkNvbnRleHRNZW51IiwidmlldyIsImlzRW5kT2ZFdmVudCIsImlzRHJhZ2dpbmciLCJkcmFnUmVmIiwiYXR0cmlidXRlcyIsImxpc3RlbmVycyIsInNldE5vZGVSZWYiLCJkbmRJc0RyYWdnaW5nIiwiaWQiLCJkYXRhIiwidHlwZSIsInBheWxvYWQiLCJjb21iaW5lZElzRHJhZ2dpbmciLCJldmVudERldGFpbHMiLCJldmVudEhlaWdodCIsImhlaWdodCIsInBhcnNlSW50IiwidG9TdHJpbmciLCJyZXBsYWNlIiwic2hvd1RpbWUiLCJjb250aW51YXRpb25UZXh0IiwiZXZlbnRTaXplIiwiZm9ybWF0dGVkVGltZSIsInN0YXJ0VGltZSIsInNob3J0Rm9ybWF0IiwiZXZlbnRTdHlsZXMiLCJkZW5pbUNvbG9ySW5mbyIsInN0eWxpbmdDbGFzc2VzIiwicmdiTWF0Y2giLCJiZyIsIm1hdGNoIiwib3BhcXVlQmFja2dyb3VuZCIsImJhY2tncm91bmRDb2xvciIsIm1pbkhlaWdodCIsImJveFNoYWRvdyIsIm9wYWNpdHkiLCJjbGFzc2VzIiwiZXZlbnRDbGFzc2VzIiwiYmFzZUNsYXNzZXMiLCJyb3VuZGVkQ29ybmVycyIsImNvbnRpbnVhdGlvbkluZGljYXRvciIsImNvbnRhaW5lckNsYXNzZXMiLCJ0aXRsZUNsYXNzZXMiLCJpc011bHRpRGF5IiwidGltZUNsYXNzZXMiLCJjb250aW51YXRpb25DbGFzc2VzIiwicmVuZGVyRXZlbnRDb250ZW50IiwiZXZlbnQiLCJvcmlnaW5hbEV2ZW50IiwiZGl2IiwiY2xhc3NOYW1lIiwic3BhbiIsInRpdGxlIiwic2l6ZSIsImRpcmVjdGlvbiIsImlzRmlyc3RTZWdtZW50IiwiaXNMYXN0U2VnbWVudCIsImlzQWxsRGF5IiwicmVmIiwibm9kZSIsImN1cnJlbnQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx\n"));

/***/ })

});