"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx":
/*!*******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/MonthView.tsx ***!
  \*******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MonthView: function() { return /* binding */ MonthView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CalendarEventItem */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx\");\n/* harmony import */ var _NoEvents__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./NoEvents */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/NoEvents.tsx\");\n/* harmony import */ var _CalendarSideCard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./CalendarSideCard */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarSideCard.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Helper function to count events in a cell\nconst getEventsForCell = (date, events, spanningEvents)=>{\n    const cellEvents = [];\n    // Add spanning (multi-day) events that pass through this cell\n    spanningEvents.forEach((spanningEvent)=>{\n        const eventStart = new Date(spanningEvent.segment.originalEvent.start);\n        const eventEnd = new Date(spanningEvent.segment.originalEvent.end);\n        if (date >= (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(eventStart) && date <= (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(eventEnd)) {\n            cellEvents.push({\n                event: spanningEvent.segment.originalEvent,\n                isMultiDay: true,\n                row: spanningEvent.row,\n                colSpan: spanningEvent.colSpan\n            });\n        }\n    });\n    // Add single-day events for this cell\n    events.forEach((event)=>{\n        const eventStart = new Date(event.start);\n        if ((0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(date, eventStart)) {\n            cellEvents.push({\n                event,\n                isMultiDay: false\n            });\n        }\n    });\n    return {\n        visibleEvents: cellEvents.slice(0, 4),\n        hiddenCount: Math.max(0, cellEvents.length - 4)\n    };\n};\n// New helper function to process events for the entire month\nconst useMonthEvents = (weeks, events)=>{\n    _s();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const positionedEventsByWeek = new Map();\n        weeks.forEach((week, weekIndex)=>{\n            const weekStart = week[0];\n            const weekEnd = week[6];\n            const weekEvents = events.filter((event)=>{\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                return eventStart <= weekEnd && eventEnd >= weekStart;\n            });\n            const spanningEvents = [];\n            weekEvents.forEach((event)=>{\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                const startDayIndex = week.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(day, eventStart));\n                const endDayIndex = week.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(day, eventEnd));\n                const eventSpansWeek = startDayIndex !== -1 || endDayIndex !== -1 || eventStart < weekStart && eventEnd > weekEnd;\n                if (eventSpansWeek) {\n                    const start = startDayIndex !== -1 ? startDayIndex : 0;\n                    const end = endDayIndex !== -1 ? endDayIndex : 6;\n                    spanningEvents.push({\n                        event,\n                        startDayIndex: start,\n                        endDayIndex: end,\n                        colSpan: end - start + 1\n                    });\n                }\n            });\n            const positioned = [];\n            const rows = [];\n            const sortedEvents = spanningEvents.sort((a, b)=>a.startDayIndex - b.startDayIndex || b.colSpan - a.colSpan);\n            sortedEvents.forEach((event)=>{\n                let assigned = false;\n                for(let i = 0; i < rows.length; i++){\n                    const row = rows[i];\n                    if (!row.some((e)=>event.startDayIndex <= e.endDayIndex && event.endDayIndex >= e.startDayIndex)) {\n                        row.push(event);\n                        positioned.push({\n                            ...event,\n                            row: i\n                        });\n                        assigned = true;\n                        break;\n                    }\n                }\n                if (!assigned) {\n                    rows.push([\n                        event\n                    ]);\n                    positioned.push({\n                        ...event,\n                        row: rows.length - 1\n                    });\n                }\n            });\n            positionedEventsByWeek.set(weekIndex, positioned);\n        });\n        return positionedEventsByWeek;\n    }, [\n        weeks,\n        events\n    ]);\n};\n_s(useMonthEvents, \"nwk+m61qLgjDVUp4IGV/072DDN4=\");\n// Helper function to check if two events overlap\nconst hasOverlap = (a, b)=>{\n    const aStart = new Date(a.segment.originalEvent.start);\n    const aEnd = new Date(a.segment.originalEvent.end);\n    const bStart = new Date(b.segment.originalEvent.start);\n    const bEnd = new Date(b.segment.originalEvent.end);\n    return aStart <= bEnd && aEnd >= bStart;\n};\n// Helper function to position spanning events\nconst positionSpanningEvents = (events)=>{\n    const positioned = [];\n    const rows = [];\n    // Sort events by their span length (longer events first)\n    const sortedEvents = [\n        ...events\n    ].sort((a, b)=>a.colSpan === b.colSpan ? 0 : b.colSpan - a.colSpan);\n    sortedEvents.forEach((event)=>{\n        let assigned = false;\n        // Try to fit in existing rows\n        for(let i = 0; i < rows.length; i++){\n            const row = rows[i];\n            if (!row.some((e)=>hasOverlap(event, e))) {\n                row.push(event);\n                positioned.push({\n                    ...event,\n                    row: i\n                });\n                assigned = true;\n                break;\n            }\n        }\n        // If no existing row works, create a new row\n        if (!assigned) {\n            rows.push([\n                event\n            ]);\n            positioned.push({\n                ...event,\n                row: rows.length - 1\n            });\n        }\n    });\n    return positioned;\n};\nconst DayCell = (param)=>{\n    let { date, isCurrentMonth, onClick, events, spanningEvents, onEventClick, selectedEvent } = param;\n    _s1();\n    const { setNodeRef, isOver } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__.useDroppable)({\n        id: \"daycell-\".concat((0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(date, \"yyyy-MM-dd\")),\n        data: {\n            type: \"daycell\",\n            date: date\n        }\n    });\n    const { visibleEvents, hiddenCount } = getEventsForCell(date, events, spanningEvents);\n    const isCurrentDay = (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(date);\n    const MAX_VISIBLE_EVENTS = 4;\n    const ROW_HEIGHT = 28;\n    // Calculate container height\n    const maxRow = visibleEvents.reduce((max, event)=>{\n        if (\"row\" in event) {\n            return Math.max(max, event.row || 0);\n        }\n        return max;\n    }, 0);\n    const containerHeight = hiddenCount > 0 ? MAX_VISIBLE_EVENTS * ROW_HEIGHT + 20 : (maxRow + 1) * ROW_HEIGHT;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: setNodeRef,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"group min-h-[120px] p-2 border-b border-r border-neutral-300\", \"cursor-pointer hover:bg-neutral-50 transition-colors\", !isCurrentMonth && \"bg-neutral-50\", isOver && \"bg-blue-50\"),\n        onClick: onClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center rounded-full font-semibold text-xs w-6 h-6\", isCurrentDay ? \"bg-black text-white\" : isCurrentMonth ? \"text-black hover:bg-neutral-100\" : \"text-neutral-400\"),\n                    children: (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(date, \"d\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 240,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                style: {\n                    height: \"\".concat(containerHeight, \"px\")\n                },\n                children: [\n                    visibleEvents.map((eventInfo)=>{\n                        const row = \"row\" in eventInfo && eventInfo.row !== undefined ? eventInfo.row : 0;\n                        const colSpan = \"colSpan\" in eventInfo && eventInfo.colSpan !== undefined ? eventInfo.colSpan : 1;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute\",\n                            style: {\n                                top: \"\".concat(row * ROW_HEIGHT, \"px\"),\n                                left: \"2px\",\n                                width: colSpan > 1 ? \"calc(\".concat(colSpan * 100, \"% + \").concat((colSpan - 1) * 19, \"px)\") : \"calc(100% - 4px)\",\n                                zIndex: 10 + row\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__.CalendarEventItem, {\n                                event: eventInfo.event,\n                                view: \"month\",\n                                onClick: (e)=>{\n                                    e.stopPropagation();\n                                    onEventClick(eventInfo.event);\n                                },\n                                isDragging: false\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 15\n                            }, undefined)\n                        }, eventInfo.event.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 13\n                        }, undefined);\n                    }),\n                    hiddenCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-black hover:text-black font-medium text-xs cursor-pointer\",\n                        style: {\n                            position: \"absolute\",\n                            top: \"\".concat(MAX_VISIBLE_EVENTS * ROW_HEIGHT, \"px\"),\n                            left: \"2px\"\n                        },\n                        onClick: (e)=>{\n                            e.stopPropagation();\n                            onClick();\n                        },\n                        children: [\n                            \"+ \",\n                            hiddenCount,\n                            \" more\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 230,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(DayCell, \"DmJTTt6A5xWIX/faBiFge3FOLrw=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__.useDroppable\n    ];\n});\n_c = DayCell;\nconst MonthView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, setSelectedDate, openAddEventForm, canEditData, handleEventClick, activeDragData } = param;\n    _s2();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_5__.useMaybeRecord)();\n    const isInRecordTab = !!maybeRecord;\n    // Memoize month calculations\n    const monthCalculations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const monthStart = (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(selectedDate);\n        const monthEnd = (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(selectedDate);\n        const startDay = (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(monthStart, {\n            weekStartsOn: 0\n        });\n        const endDay = (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_18__[\"default\"])(monthEnd, {\n            weekStartsOn: 0\n        });\n        const days = [];\n        let day = startDay;\n        while(day <= endDay){\n            days.push(day);\n            day = (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_19__[\"default\"])(day, 1);\n        }\n        const weeks = [];\n        for(let i = 0; i < days.length; i += 7){\n            weeks.push(days.slice(i, i + 7));\n        }\n        return {\n            monthStart,\n            monthEnd,\n            startDay,\n            endDay,\n            days,\n            weeks\n        };\n    }, [\n        selectedDate\n    ]);\n    // Get all events for the visible month range\n    const monthEvents = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>events.filter((event)=>{\n            const eventStart = new Date(event.start);\n            const eventEnd = new Date(event.end);\n            return eventStart <= monthCalculations.endDay && eventEnd >= monthCalculations.startDay;\n        }), [\n        events,\n        monthCalculations.startDay,\n        monthCalculations.endDay\n    ]);\n    // Calculate spanning events for each week\n    const weeklySpanningEvents = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const spanningEventsByWeek = new Map();\n        monthCalculations.weeks.forEach((week, weekIndex)=>{\n            const weekStart = week[0];\n            const weekEnd = week[6];\n            // Filter events that span this week\n            const weekEvents = monthEvents.filter((event)=>{\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                return eventStart <= weekEnd && eventEnd >= weekStart;\n            });\n            // Process spanning events\n            const spanningEvents = [];\n            weekEvents.forEach((event)=>{\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                const startDayIndex = week.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(day, eventStart) || day > eventStart);\n                const endDayIndex = week.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(day, eventEnd) || day > eventEnd);\n                const start = startDayIndex !== -1 ? startDayIndex : 0;\n                const end = endDayIndex !== -1 ? endDayIndex : 6;\n                const extendedEvent = {\n                    ...event,\n                    startDayIndex: start\n                };\n                spanningEvents.push({\n                    segment: {\n                        originalEvent: extendedEvent\n                    },\n                    row: 0,\n                    colSpan: end - start + 1\n                });\n            });\n            // Position spanning events in rows\n            const positioned = positionSpanningEvents(spanningEvents);\n            spanningEventsByWeek.set(weekIndex, positioned);\n        });\n        return spanningEventsByWeek;\n    }, [\n        monthEvents,\n        monthCalculations.weeks\n    ]);\n    // Render empty state when no events\n    const renderEmptyState = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-7 border-b border-neutral-300 bg-white\",\n                    children: [\n                        \"Sunday\",\n                        \"Monday\",\n                        \"Tuesday\",\n                        \"Wednesday\",\n                        \"Thursday\",\n                        \"Friday\",\n                        \"Saturday\"\n                    ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-black\", \"py-2 text-xs\"),\n                            children: dayName.substring(0, 3)\n                        }, dayName, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 406,\n                            columnNumber: 11\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 404,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NoEvents__WEBPACK_IMPORTED_MODULE_7__.NoEvents, {\n                    title: \"No events this month\",\n                    message: \"\".concat((0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(selectedDate, \"MMMM yyyy\"), \" is completely free. Start planning your month!\"),\n                    showCreateButton: canEditData,\n                    onCreate: ()=>openAddEventForm(selectedDate)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 415,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n            lineNumber: 403,\n            columnNumber: 5\n        }, undefined);\n    // Render day cell content\n    const renderDayCellContent = (day, dayEvents)=>{\n        const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n        const isCurrentDay = (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(day);\n        const MAX_VISIBLE_EVENTS = 4;\n        const ROW_HEIGHT = 28;\n        const sortedEvents = dayEvents.sort((a, b)=>a.row - b.row);\n        const visibleEvents = sortedEvents.slice(0, MAX_VISIBLE_EVENTS);\n        const hasMore = sortedEvents.length > MAX_VISIBLE_EVENTS;\n        // To fix the gap after the \"+more\" link, we must calculate the container's height\n        // instead of letting it expand. The link is placed at the start of the 5th row,\n        // and we'll give it 20px of height.\n        const maxRow = visibleEvents.reduce((max, event)=>Math.max(max, event.row), -1);\n        const containerHeight = hasMore ? MAX_VISIBLE_EVENTS * ROW_HEIGHT + 20 : (maxRow + 1) * ROW_HEIGHT;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center rounded-full font-semibold text-xs w-6 h-6\", isCurrentDay ? \"bg-black text-white\" : isCurrentMonth ? \"text-black hover:bg-neutral-100\" : \"text-neutral-400\"),\n                            children: (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(day, \"d\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 446,\n                            columnNumber: 11\n                        }, undefined),\n                        canEditData && isCurrentMonth && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            className: \"rounded-full h-6 w-6 p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-neutral-100 hidden lg:inline-flex\",\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                setTimeout(()=>openAddEventForm(day), 150);\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                className: \"h-3 w-3 text-black\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 461,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 453,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 445,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    style: {\n                        height: \"\".concat(containerHeight, \"px\")\n                    },\n                    children: [\n                        visibleEvents.map((pe)=>{\n                            var _activeDragData_payload;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute\",\n                                style: {\n                                    top: \"\".concat(pe.row * ROW_HEIGHT, \"px\"),\n                                    left: \"2px\",\n                                    width: pe.colSpan > 1 ? \"calc(\".concat(pe.colSpan * 100, \"% + \").concat((pe.colSpan - 1) * 19, \"px)\") : \"calc(100% - 4px)\",\n                                    // zIndex: pe.colSpan > 1 ? 10 : 1,\n                                    zIndex: 10 + pe.row\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__.CalendarEventItem, {\n                                    event: pe.event,\n                                    view: \"month\",\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        setSelectedEvent(pe.event.id);\n                                        handleEventClick(pe.event);\n                                    },\n                                    isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === pe.event.id\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, pe.event.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 13\n                            }, undefined);\n                        }),\n                        hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-black hover:text-black font-medium text-xs cursor-pointer\",\n                            style: {\n                                position: \"absolute\",\n                                top: \"\".concat(MAX_VISIBLE_EVENTS * ROW_HEIGHT, \"px\"),\n                                left: \"2px\"\n                            },\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                setSelectedDate(day);\n                            },\n                            children: [\n                                \"+ \",\n                                sortedEvents.length - MAX_VISIBLE_EVENTS,\n                                \" more\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 495,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 466,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true);\n    };\n    // Render main view\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-background flex flex-col lg:flex-row\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col min-h-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        \"data-day-headers\": \"true\",\n                        className: \"grid grid-cols-7 border-b border-neutral-300 sticky top-0 z-10 bg-white\",\n                        children: [\n                            \"Sunday\",\n                            \"Monday\",\n                            \"Tuesday\",\n                            \"Wednesday\",\n                            \"Thursday\",\n                            \"Friday\",\n                            \"Saturday\"\n                        ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-black\", \"py-2 text-xs\"),\n                                children: dayName.substring(0, 3)\n                            }, dayName, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 525,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 520,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n                        className: \"flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-7 border-neutral-300 border-b\",\n                            children: monthCalculations.weeks.map((week, weekIndex)=>week.map((day, dayIndex)=>{\n                                    const weekEvents = weeklySpanningEvents.get(weekIndex) || [];\n                                    const dayEvents = weekEvents.filter((pe)=>pe.segment.originalEvent.startDayIndex === dayIndex);\n                                    const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DayCell, {\n                                        date: day,\n                                        isCurrentMonth: isCurrentMonth,\n                                        onClick: ()=>setSelectedDate(day),\n                                        events: monthEvents,\n                                        spanningEvents: weekEvents,\n                                        onEventClick: handleEventClick,\n                                        selectedEvent: selectedEvent\n                                    }, \"\".concat(weekIndex, \"-\").concat(dayIndex), false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                        lineNumber: 544,\n                                        columnNumber: 19\n                                    }, undefined);\n                                }))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 536,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 535,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 518,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarSideCard__WEBPACK_IMPORTED_MODULE_8__.CalendarSideCard, {\n                selectedDate: selectedDate,\n                events: events,\n                selectedEvent: selectedEvent,\n                setSelectedEvent: setSelectedEvent,\n                handleEventClick: handleEventClick\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 561,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 517,\n        columnNumber: 5\n    }, undefined);\n};\n_s2(MonthView, \"kPDDlaHW0EW5WI3Ne0bDTWgAARs=\", false, function() {\n    return [\n        _providers_record__WEBPACK_IMPORTED_MODULE_5__.useMaybeRecord\n    ];\n});\n_c1 = MonthView;\nfunction isMultiDay(event) {\n    return !(0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(new Date(event.start), new Date(event.end));\n}\nvar _c, _c1;\n$RefreshReg$(_c, \"DayCell\");\n$RefreshReg$(_c1, \"MonthView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3dvcmtzcGFjZS9tYWluL3ZpZXdzL2NhbGVuZGFyL2NvbXBvbmVudHMvTW9udGhWaWV3LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXVDO0FBQ2dHO0FBQ3RHO0FBQ2U7QUFDTztBQUNFO0FBRUw7QUFDSTtBQUNsQjtBQUNnQjtBQUNUO0FBZ0M3Qyw0Q0FBNEM7QUFDNUMsTUFBTXFCLG1CQUFtQixDQUFDQyxNQUFZQyxRQUF5QkM7SUFDN0QsTUFBTUMsYUFBOEIsRUFBRTtJQUV0Qyw4REFBOEQ7SUFDOURELGVBQWVFLE9BQU8sQ0FBQ0MsQ0FBQUE7UUFDckIsTUFBTUMsYUFBYSxJQUFJQyxLQUFLRixjQUFjRyxPQUFPLENBQUNDLGFBQWEsQ0FBQ0MsS0FBSztRQUNyRSxNQUFNQyxXQUFXLElBQUlKLEtBQUtGLGNBQWNHLE9BQU8sQ0FBQ0MsYUFBYSxDQUFDRyxHQUFHO1FBRWpFLElBQUlaLFFBQVFaLCtLQUFVQSxDQUFDa0IsZUFBZU4sUUFBUVgsK0tBQVFBLENBQUNzQixXQUFXO1lBQ2hFUixXQUFXVSxJQUFJLENBQUM7Z0JBQ2RDLE9BQU9ULGNBQWNHLE9BQU8sQ0FBQ0MsYUFBYTtnQkFDMUNNLFlBQVk7Z0JBQ1pDLEtBQUtYLGNBQWNXLEdBQUc7Z0JBQ3RCQyxTQUFTWixjQUFjWSxPQUFPO1lBQ2hDO1FBQ0Y7SUFDRjtJQUVBLHNDQUFzQztJQUN0Q2hCLE9BQU9HLE9BQU8sQ0FBQ1UsQ0FBQUE7UUFDYixNQUFNUixhQUFhLElBQUlDLEtBQUtPLE1BQU1KLEtBQUs7UUFDdkMsSUFBSXhCLCtLQUFTQSxDQUFDYyxNQUFNTSxhQUFhO1lBQy9CSCxXQUFXVSxJQUFJLENBQUM7Z0JBQ2RDO2dCQUNBQyxZQUFZO1lBQ2Q7UUFDRjtJQUNGO0lBRUEsT0FBTztRQUNMRyxlQUFlZixXQUFXZ0IsS0FBSyxDQUFDLEdBQUc7UUFDbkNDLGFBQWFDLEtBQUtDLEdBQUcsQ0FBQyxHQUFHbkIsV0FBV29CLE1BQU0sR0FBRztJQUMvQztBQUNGO0FBRUEsNkRBQTZEO0FBQzdELE1BQU1DLGlCQUFpQixDQUFDQyxPQUFpQnhCOztJQUN2QyxPQUFPdEIsOENBQU9BLENBQUM7UUFDYixNQUFNK0MseUJBQXlCLElBQUlDO1FBRW5DRixNQUFNckIsT0FBTyxDQUFDLENBQUN3QixNQUFNQztZQUNuQixNQUFNQyxZQUFZRixJQUFJLENBQUMsRUFBRTtZQUN6QixNQUFNRyxVQUFVSCxJQUFJLENBQUMsRUFBRTtZQUV2QixNQUFNSSxhQUFhL0IsT0FBT2dDLE1BQU0sQ0FBQ25CLENBQUFBO2dCQUMvQixNQUFNUixhQUFhLElBQUlDLEtBQUtPLE1BQU1KLEtBQUs7Z0JBQ3ZDLE1BQU1DLFdBQVcsSUFBSUosS0FBS08sTUFBTUYsR0FBRztnQkFDbkMsT0FBT04sY0FBY3lCLFdBQVdwQixZQUFZbUI7WUFDOUM7WUFFQSxNQUFNNUIsaUJBQXdCLEVBQUU7WUFDaEM4QixXQUFXNUIsT0FBTyxDQUFDVSxDQUFBQTtnQkFDakIsTUFBTVIsYUFBYSxJQUFJQyxLQUFLTyxNQUFNSixLQUFLO2dCQUN2QyxNQUFNQyxXQUFXLElBQUlKLEtBQUtPLE1BQU1GLEdBQUc7Z0JBRW5DLE1BQU1zQixnQkFBZ0JOLEtBQUtPLFNBQVMsQ0FBQ0MsQ0FBQUEsTUFBT2xELCtLQUFTQSxDQUFDa0QsS0FBSzlCO2dCQUMzRCxNQUFNK0IsY0FBY1QsS0FBS08sU0FBUyxDQUFDQyxDQUFBQSxNQUFPbEQsK0tBQVNBLENBQUNrRCxLQUFLekI7Z0JBRXpELE1BQU0yQixpQkFBaUJKLGtCQUFrQixDQUFDLEtBQUtHLGdCQUFnQixDQUFDLEtBQU0vQixhQUFhd0IsYUFBYW5CLFdBQVdvQjtnQkFFM0csSUFBSU8sZ0JBQWdCO29CQUNsQixNQUFNNUIsUUFBUXdCLGtCQUFrQixDQUFDLElBQUlBLGdCQUFnQjtvQkFDckQsTUFBTXRCLE1BQU15QixnQkFBZ0IsQ0FBQyxJQUFJQSxjQUFjO29CQUMvQ25DLGVBQWVXLElBQUksQ0FBQzt3QkFDbEJDO3dCQUNBb0IsZUFBZXhCO3dCQUNmMkIsYUFBYXpCO3dCQUNiSyxTQUFTTCxNQUFNRixRQUFRO29CQUN6QjtnQkFDRjtZQUNGO1lBRUEsTUFBTTZCLGFBQW9CLEVBQUU7WUFDNUIsTUFBTUMsT0FBZ0IsRUFBRTtZQUN4QixNQUFNQyxlQUFldkMsZUFBZXdDLElBQUksQ0FBQyxDQUFDQyxHQUFHQyxJQUFNRCxFQUFFVCxhQUFhLEdBQUdVLEVBQUVWLGFBQWEsSUFBSVUsRUFBRTNCLE9BQU8sR0FBRzBCLEVBQUUxQixPQUFPO1lBRTdHd0IsYUFBYXJDLE9BQU8sQ0FBQ1UsQ0FBQUE7Z0JBQ25CLElBQUkrQixXQUFXO2dCQUNmLElBQUssSUFBSUMsSUFBSSxHQUFHQSxJQUFJTixLQUFLakIsTUFBTSxFQUFFdUIsSUFBSztvQkFDcEMsTUFBTTlCLE1BQU13QixJQUFJLENBQUNNLEVBQUU7b0JBQ25CLElBQUksQ0FBQzlCLElBQUkrQixJQUFJLENBQUNDLENBQUFBLElBQUtsQyxNQUFNb0IsYUFBYSxJQUFJYyxFQUFFWCxXQUFXLElBQUl2QixNQUFNdUIsV0FBVyxJQUFJVyxFQUFFZCxhQUFhLEdBQUc7d0JBQ2hHbEIsSUFBSUgsSUFBSSxDQUFDQzt3QkFDVHlCLFdBQVcxQixJQUFJLENBQUM7NEJBQUUsR0FBR0MsS0FBSzs0QkFBRUUsS0FBSzhCO3dCQUFFO3dCQUNuQ0QsV0FBVzt3QkFDWDtvQkFDRjtnQkFDRjtnQkFDQSxJQUFJLENBQUNBLFVBQVU7b0JBQ2JMLEtBQUszQixJQUFJLENBQUM7d0JBQUNDO3FCQUFNO29CQUNqQnlCLFdBQVcxQixJQUFJLENBQUM7d0JBQUUsR0FBR0MsS0FBSzt3QkFBRUUsS0FBS3dCLEtBQUtqQixNQUFNLEdBQUc7b0JBQUU7Z0JBQ25EO1lBQ0Y7WUFDQUcsdUJBQXVCdUIsR0FBRyxDQUFDcEIsV0FBV1U7UUFDeEM7UUFFQSxPQUFPYjtJQUNULEdBQUc7UUFBQ0Q7UUFBT3hCO0tBQU87QUFDcEI7R0E3RE11QjtBQStETixpREFBaUQ7QUFDakQsTUFBTTBCLGFBQWEsQ0FBQ1AsR0FBa0JDO0lBQ3BDLE1BQU1PLFNBQVMsSUFBSTVDLEtBQUtvQyxFQUFFbkMsT0FBTyxDQUFDQyxhQUFhLENBQUNDLEtBQUs7SUFDckQsTUFBTTBDLE9BQU8sSUFBSTdDLEtBQUtvQyxFQUFFbkMsT0FBTyxDQUFDQyxhQUFhLENBQUNHLEdBQUc7SUFDakQsTUFBTXlDLFNBQVMsSUFBSTlDLEtBQUtxQyxFQUFFcEMsT0FBTyxDQUFDQyxhQUFhLENBQUNDLEtBQUs7SUFDckQsTUFBTTRDLE9BQU8sSUFBSS9DLEtBQUtxQyxFQUFFcEMsT0FBTyxDQUFDQyxhQUFhLENBQUNHLEdBQUc7SUFDakQsT0FBT3VDLFVBQVVHLFFBQVFGLFFBQVFDO0FBQ25DO0FBRUEsOENBQThDO0FBQzlDLE1BQU1FLHlCQUF5QixDQUFDdEQ7SUFDOUIsTUFBTXNDLGFBQThCLEVBQUU7SUFDdEMsTUFBTUMsT0FBMEIsRUFBRTtJQUVsQyx5REFBeUQ7SUFDekQsTUFBTUMsZUFBZTtXQUFJeEM7S0FBTyxDQUFDeUMsSUFBSSxDQUFDLENBQUNDLEdBQUdDLElBQ3hDRCxFQUFFMUIsT0FBTyxLQUFLMkIsRUFBRTNCLE9BQU8sR0FBRyxJQUFJMkIsRUFBRTNCLE9BQU8sR0FBRzBCLEVBQUUxQixPQUFPO0lBR3JEd0IsYUFBYXJDLE9BQU8sQ0FBQ1UsQ0FBQUE7UUFDbkIsSUFBSStCLFdBQVc7UUFDZiw4QkFBOEI7UUFDOUIsSUFBSyxJQUFJQyxJQUFJLEdBQUdBLElBQUlOLEtBQUtqQixNQUFNLEVBQUV1QixJQUFLO1lBQ3BDLE1BQU05QixNQUFNd0IsSUFBSSxDQUFDTSxFQUFFO1lBQ25CLElBQUksQ0FBQzlCLElBQUkrQixJQUFJLENBQUNDLENBQUFBLElBQUtFLFdBQVdwQyxPQUFPa0MsS0FBSztnQkFDeENoQyxJQUFJSCxJQUFJLENBQUNDO2dCQUNUeUIsV0FBVzFCLElBQUksQ0FBQztvQkFBRSxHQUFHQyxLQUFLO29CQUFFRSxLQUFLOEI7Z0JBQUU7Z0JBQ25DRCxXQUFXO2dCQUNYO1lBQ0Y7UUFDRjtRQUNBLDZDQUE2QztRQUM3QyxJQUFJLENBQUNBLFVBQVU7WUFDYkwsS0FBSzNCLElBQUksQ0FBQztnQkFBQ0M7YUFBTTtZQUNqQnlCLFdBQVcxQixJQUFJLENBQUM7Z0JBQUUsR0FBR0MsS0FBSztnQkFBRUUsS0FBS3dCLEtBQUtqQixNQUFNLEdBQUc7WUFBRTtRQUNuRDtJQUNGO0lBRUEsT0FBT2dCO0FBQ1Q7QUFZQSxNQUFNaUIsVUFBa0M7UUFBQyxFQUN2Q3hELElBQUksRUFDSnlELGNBQWMsRUFDZEMsT0FBTyxFQUNQekQsTUFBTSxFQUNOQyxjQUFjLEVBQ2R5RCxZQUFZLEVBQ1pDLGFBQWEsRUFDZDs7SUFDQyxNQUFNLEVBQUVDLFVBQVUsRUFBRUMsTUFBTSxFQUFFLEdBQUdoRSwyREFBWUEsQ0FBQztRQUMxQ2lFLElBQUksV0FBc0MsT0FBM0JuRiwrS0FBTUEsQ0FBQ29CLE1BQU07UUFDNUJnRSxNQUFNO1lBQ0pDLE1BQU07WUFDTmpFLE1BQU1BO1FBQ1I7SUFDRjtJQUVBLE1BQU0sRUFBRWtCLGFBQWEsRUFBRUUsV0FBVyxFQUFFLEdBQUdyQixpQkFBaUJDLE1BQU1DLFFBQVFDO0lBQ3RFLE1BQU1nRSxlQUFlL0UsK0tBQU9BLENBQUNhO0lBQzdCLE1BQU1tRSxxQkFBcUI7SUFDM0IsTUFBTUMsYUFBYTtJQUVuQiw2QkFBNkI7SUFDN0IsTUFBTUMsU0FBU25ELGNBQWNvRCxNQUFNLENBQUMsQ0FBQ2hELEtBQUtSO1FBQ3hDLElBQUksU0FBU0EsT0FBTztZQUNsQixPQUFPTyxLQUFLQyxHQUFHLENBQUNBLEtBQUtSLE1BQU1FLEdBQUcsSUFBSTtRQUNwQztRQUNBLE9BQU9NO0lBQ1QsR0FBRztJQUVILE1BQU1pRCxrQkFBa0JuRCxjQUFjLElBQ2xDLHFCQUFzQmdELGFBQWMsS0FDbkMsQ0FBQ0MsU0FBUyxLQUFLRDtJQUVwQixxQkFDRSw4REFBQ0k7UUFDQ0MsS0FBS1o7UUFDTGEsV0FBV3BGLDhDQUFFQSxDQUNYLGdFQUNBLHdEQUNBLENBQUNtRSxrQkFBa0IsaUJBQ25CSyxVQUFVO1FBRVpKLFNBQVNBOzswQkFFVCw4REFBQ2M7Z0JBQUlFLFdBQVU7MEJBQ2IsNEVBQUNDO29CQUFLRCxXQUFXcEYsOENBQUVBLENBQ2pCLHNGQUNBNEUsZUFBZSx3QkFBd0JULGlCQUFpQixvQ0FBb0M7OEJBRTNGN0UsK0tBQU1BLENBQUNvQixNQUFNOzs7Ozs7Ozs7OzswQkFJbEIsOERBQUN3RTtnQkFBSUUsV0FBVTtnQkFBV0UsT0FBTztvQkFBRUMsUUFBUSxHQUFtQixPQUFoQk4saUJBQWdCO2dCQUFJOztvQkFDL0RyRCxjQUFjNEQsR0FBRyxDQUFDQyxDQUFBQTt3QkFDakIsTUFBTS9ELE1BQU0sU0FBUytELGFBQWFBLFVBQVUvRCxHQUFHLEtBQUtnRSxZQUFZRCxVQUFVL0QsR0FBRyxHQUFHO3dCQUNoRixNQUFNQyxVQUFVLGFBQWE4RCxhQUFhQSxVQUFVOUQsT0FBTyxLQUFLK0QsWUFBWUQsVUFBVTlELE9BQU8sR0FBRzt3QkFFaEcscUJBQ0UsOERBQUN1RDs0QkFFQ0UsV0FBVTs0QkFDVkUsT0FBTztnQ0FDTEssS0FBSyxHQUFvQixPQUFqQmpFLE1BQU1vRCxZQUFXO2dDQUN6QmMsTUFBTTtnQ0FDTkMsT0FBT2xFLFVBQVUsSUFDYixRQUE0QixPQUFwQkEsVUFBVSxLQUFJLFFBQXlCLE9BQW5CLENBQUNBLFVBQVUsS0FBSyxJQUFHLFNBQy9DO2dDQUNKbUUsUUFBUSxLQUFLcEU7NEJBQ2Y7c0NBRUEsNEVBQUNyQixpRUFBaUJBO2dDQUNoQm1CLE9BQU9pRSxVQUFVakUsS0FBSztnQ0FDdEJ1RSxNQUFLO2dDQUNMM0IsU0FBUyxDQUFDVjtvQ0FDUkEsRUFBRXNDLGVBQWU7b0NBQ2pCM0IsYUFBYW9CLFVBQVVqRSxLQUFLO2dDQUM5QjtnQ0FDQXlFLFlBQVk7Ozs7OzsyQkFsQlRSLFVBQVVqRSxLQUFLLENBQUNpRCxFQUFFOzs7OztvQkFzQjdCO29CQUVDM0MsY0FBYyxtQkFDYiw4REFBQ29EO3dCQUNDRSxXQUFVO3dCQUNWRSxPQUFPOzRCQUNMWSxVQUFVOzRCQUNWUCxLQUFLLEdBQW1DLE9BQWhDZCxxQkFBcUJDLFlBQVc7NEJBQ3hDYyxNQUFNO3dCQUNSO3dCQUNBeEIsU0FBUyxDQUFDVjs0QkFDUkEsRUFBRXNDLGVBQWU7NEJBQ2pCNUI7d0JBQ0Y7OzRCQUNEOzRCQUNJdEM7NEJBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNM0I7SUF4R01vQzs7UUFTMkIxRCx1REFBWUE7OztLQVR2QzBEO0FBMEdDLE1BQU1pQyxZQUFzQztRQUFDLEVBQ2xEQyxZQUFZLEVBQ1p6RixNQUFNLEVBQ04yRCxhQUFhLEVBQ2IrQixnQkFBZ0IsRUFDaEJDLGVBQWUsRUFDZkMsZ0JBQWdCLEVBQ2hCQyxXQUFXLEVBQ1hDLGdCQUFnQixFQUNoQkMsY0FBYyxFQUNmOztJQUNDLE1BQU1DLGNBQWN2RyxpRUFBY0E7SUFDbEMsTUFBTXdHLGdCQUFnQixDQUFDLENBQUNEO0lBRXhCLDZCQUE2QjtJQUM3QixNQUFNRSxvQkFBb0J4SCw4Q0FBT0EsQ0FBQztRQUNoQyxNQUFNeUgsYUFBYXZILCtLQUFZQSxDQUFDNkc7UUFDaEMsTUFBTVcsV0FBV3ZILCtLQUFVQSxDQUFDNEc7UUFDNUIsTUFBTVksV0FBV3ZILCtLQUFXQSxDQUFDcUgsWUFBWTtZQUFFRyxjQUFjO1FBQUU7UUFDM0QsTUFBTUMsU0FBU3hILCtLQUFTQSxDQUFDcUgsVUFBVTtZQUFFRSxjQUFjO1FBQUU7UUFFckQsTUFBTUUsT0FBTyxFQUFFO1FBQ2YsSUFBSXJFLE1BQU1rRTtRQUNWLE1BQU9sRSxPQUFPb0UsT0FBUTtZQUNwQkMsS0FBSzVGLElBQUksQ0FBQ3VCO1lBQ1ZBLE1BQU1uRCwrS0FBT0EsQ0FBQ21ELEtBQUs7UUFDckI7UUFFQSxNQUFNWCxRQUFRLEVBQUU7UUFDaEIsSUFBSyxJQUFJcUIsSUFBSSxHQUFHQSxJQUFJMkQsS0FBS2xGLE1BQU0sRUFBRXVCLEtBQUssRUFBRztZQUN2Q3JCLE1BQU1aLElBQUksQ0FBQzRGLEtBQUt0RixLQUFLLENBQUMyQixHQUFHQSxJQUFJO1FBQy9CO1FBRUEsT0FBTztZQUFFc0Q7WUFBWUM7WUFBVUM7WUFBVUU7WUFBUUM7WUFBTWhGO1FBQU07SUFDL0QsR0FBRztRQUFDaUU7S0FBYTtJQUVqQiw2Q0FBNkM7SUFDN0MsTUFBTWdCLGNBQWMvSCw4Q0FBT0EsQ0FBQyxJQUMxQnNCLE9BQU9nQyxNQUFNLENBQUNuQixDQUFBQTtZQUNaLE1BQU1SLGFBQWEsSUFBSUMsS0FBS08sTUFBTUosS0FBSztZQUN2QyxNQUFNQyxXQUFXLElBQUlKLEtBQUtPLE1BQU1GLEdBQUc7WUFDbkMsT0FBT04sY0FBYzZGLGtCQUFrQkssTUFBTSxJQUN0QzdGLFlBQVl3RixrQkFBa0JHLFFBQVE7UUFDL0MsSUFDQTtRQUFDckc7UUFBUWtHLGtCQUFrQkcsUUFBUTtRQUFFSCxrQkFBa0JLLE1BQU07S0FBQztJQUdoRSwwQ0FBMEM7SUFDMUMsTUFBTUcsdUJBQXVCaEksOENBQU9BLENBQUM7UUFDbkMsTUFBTWlJLHVCQUF1QixJQUFJakY7UUFFakN3RSxrQkFBa0IxRSxLQUFLLENBQUNyQixPQUFPLENBQUMsQ0FBQ3dCLE1BQU1DO1lBQ3JDLE1BQU1DLFlBQVlGLElBQUksQ0FBQyxFQUFFO1lBQ3pCLE1BQU1HLFVBQVVILElBQUksQ0FBQyxFQUFFO1lBRXZCLG9DQUFvQztZQUNwQyxNQUFNSSxhQUFhMEUsWUFBWXpFLE1BQU0sQ0FBQ25CLENBQUFBO2dCQUNwQyxNQUFNUixhQUFhLElBQUlDLEtBQUtPLE1BQU1KLEtBQUs7Z0JBQ3ZDLE1BQU1DLFdBQVcsSUFBSUosS0FBS08sTUFBTUYsR0FBRztnQkFDbkMsT0FBT04sY0FBY3lCLFdBQVdwQixZQUFZbUI7WUFDOUM7WUFFQSwwQkFBMEI7WUFDMUIsTUFBTTVCLGlCQUFrQyxFQUFFO1lBQzFDOEIsV0FBVzVCLE9BQU8sQ0FBQ1UsQ0FBQUE7Z0JBQ2pCLE1BQU1SLGFBQWEsSUFBSUMsS0FBS08sTUFBTUosS0FBSztnQkFDdkMsTUFBTUMsV0FBVyxJQUFJSixLQUFLTyxNQUFNRixHQUFHO2dCQUVuQyxNQUFNc0IsZ0JBQWdCTixLQUFLTyxTQUFTLENBQUNDLENBQUFBLE1BQ25DbEQsK0tBQVNBLENBQUNrRCxLQUFLOUIsZUFBZThCLE1BQU05QjtnQkFFdEMsTUFBTStCLGNBQWNULEtBQUtPLFNBQVMsQ0FBQ0MsQ0FBQUEsTUFDakNsRCwrS0FBU0EsQ0FBQ2tELEtBQUt6QixhQUFheUIsTUFBTXpCO2dCQUdwQyxNQUFNRCxRQUFRd0Isa0JBQWtCLENBQUMsSUFBSUEsZ0JBQWdCO2dCQUNyRCxNQUFNdEIsTUFBTXlCLGdCQUFnQixDQUFDLElBQUlBLGNBQWM7Z0JBRS9DLE1BQU13RSxnQkFBZ0I7b0JBQ3BCLEdBQUcvRixLQUFLO29CQUNSb0IsZUFBZXhCO2dCQUNqQjtnQkFFQVIsZUFBZVcsSUFBSSxDQUFDO29CQUNsQkwsU0FBUzt3QkFDUEMsZUFBZW9HO29CQUNqQjtvQkFDQTdGLEtBQUs7b0JBQ0xDLFNBQVNMLE1BQU1GLFFBQVE7Z0JBQ3pCO1lBQ0Y7WUFFQSxtQ0FBbUM7WUFDbkMsTUFBTTZCLGFBQWFnQix1QkFBdUJyRDtZQUMxQzBHLHFCQUFxQjNELEdBQUcsQ0FBQ3BCLFdBQVdVO1FBQ3RDO1FBRUEsT0FBT3FFO0lBQ1QsR0FBRztRQUFDRjtRQUFhUCxrQkFBa0IxRSxLQUFLO0tBQUM7SUFFekMsb0NBQW9DO0lBQ3BDLE1BQU1xRixtQkFBbUIsa0JBQ3ZCLDhEQUFDdEM7WUFBSUUsV0FBVTs7OEJBQ2IsOERBQUNGO29CQUFJRSxXQUFVOzhCQUNaO3dCQUFDO3dCQUFVO3dCQUFVO3dCQUFXO3dCQUFhO3dCQUFZO3dCQUFVO3FCQUFXLENBQUNJLEdBQUcsQ0FBQ2lDLENBQUFBLHdCQUNsRiw4REFBQ3ZDOzRCQUFrQkUsV0FBV3BGLDhDQUFFQSxDQUM5Qix3Q0FDQTtzQ0FFQ3lILFFBQVFDLFNBQVMsQ0FBQyxHQUFHOzJCQUpkRDs7Ozs7Ozs7Ozs4QkFTZCw4REFBQ25ILCtDQUFRQTtvQkFDUHFILE9BQU07b0JBQ05DLFNBQVMsR0FBcUMsT0FBbEN0SSwrS0FBTUEsQ0FBQzhHLGNBQWMsY0FBYTtvQkFDOUN5QixrQkFBa0JyQjtvQkFDbEJzQixVQUFVLElBQU12QixpQkFBaUJIOzs7Ozs7Ozs7Ozs7SUFLdkMsMEJBQTBCO0lBQzFCLE1BQU0yQix1QkFBdUIsQ0FBQ2pGLEtBQVdrRjtRQUN2QyxNQUFNN0QsaUJBQWlCckIsSUFBSW1GLFFBQVEsT0FBTzdCLGFBQWE2QixRQUFRO1FBQy9ELE1BQU1yRCxlQUFlL0UsK0tBQU9BLENBQUNpRDtRQUM3QixNQUFNK0IscUJBQXFCO1FBQzNCLE1BQU1DLGFBQWE7UUFFbkIsTUFBTTNCLGVBQWU2RSxVQUFVNUUsSUFBSSxDQUFDLENBQUNDLEdBQUdDLElBQU1ELEVBQUUzQixHQUFHLEdBQUc0QixFQUFFNUIsR0FBRztRQUMzRCxNQUFNRSxnQkFBZ0J1QixhQUFhdEIsS0FBSyxDQUFDLEdBQUdnRDtRQUM1QyxNQUFNcUQsVUFBVS9FLGFBQWFsQixNQUFNLEdBQUc0QztRQUV0QyxrRkFBa0Y7UUFDbEYsZ0ZBQWdGO1FBQ2hGLG9DQUFvQztRQUNwQyxNQUFNRSxTQUFTbkQsY0FBY29ELE1BQU0sQ0FBQyxDQUFDaEQsS0FBS1IsUUFBVU8sS0FBS0MsR0FBRyxDQUFDQSxLQUFLUixNQUFNRSxHQUFHLEdBQUcsQ0FBQztRQUMvRSxNQUFNdUQsa0JBQWtCaUQsVUFDcEIscUJBQXNCcEQsYUFBYyxLQUNwQyxDQUFDQyxTQUFTLEtBQUtEO1FBRW5CLHFCQUNFOzs4QkFDRSw4REFBQ0k7b0JBQUlFLFdBQVU7O3NDQUNiLDhEQUFDQzs0QkFBS0QsV0FBV3BGLDhDQUFFQSxDQUNqQixzRkFDQTRFLGVBQWUsd0JBQXdCVCxpQkFBaUIsb0NBQW9DO3NDQUUzRjdFLCtLQUFNQSxDQUFDd0QsS0FBSzs7Ozs7O3dCQUVkMEQsZUFBZXJDLGdDQUNkLDhEQUFDbEUseURBQU1BOzRCQUNMa0ksU0FBUTs0QkFDUi9DLFdBQVU7NEJBQ1ZoQixTQUFTLENBQUNWO2dDQUNSQSxFQUFFc0MsZUFBZTtnQ0FDakJvQyxXQUFXLElBQU03QixpQkFBaUJ6RCxNQUFNOzRCQUMxQztzQ0FFQSw0RUFBQzVDLG1HQUFRQTtnQ0FBQ2tGLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQUsxQiw4REFBQ0Y7b0JBQUlFLFdBQVU7b0JBQVdFLE9BQU87d0JBQUVDLFFBQVEsR0FBbUIsT0FBaEJOLGlCQUFnQjtvQkFBSTs7d0JBQy9EckQsY0FBYzRELEdBQUcsQ0FBQzZDLENBQUFBO2dDQXNCRDNCO2lEQXJCaEIsOERBQUN4QjtnQ0FFQ0UsV0FBVTtnQ0FDVkUsT0FBTztvQ0FDTEssS0FBSyxHQUF1QixPQUFwQjBDLEdBQUczRyxHQUFHLEdBQUdvRCxZQUFXO29DQUM1QmMsTUFBTTtvQ0FDTkMsT0FBT3dDLEdBQUcxRyxPQUFPLEdBQUcsSUFDaEIsUUFBK0IsT0FBdkIwRyxHQUFHMUcsT0FBTyxHQUFHLEtBQUksUUFBNEIsT0FBdEIsQ0FBQzBHLEdBQUcxRyxPQUFPLEdBQUcsS0FBSyxJQUFHLFNBQ3JEO29DQUNKLG1DQUFtQztvQ0FDbkNtRSxRQUFRLEtBQUt1QyxHQUFHM0csR0FBRztnQ0FDckI7MENBRUEsNEVBQUNyQixpRUFBaUJBO29DQUNoQm1CLE9BQU82RyxHQUFHN0csS0FBSztvQ0FDZnVFLE1BQUs7b0NBQ0wzQixTQUFTLENBQUNWO3dDQUNSQSxFQUFFc0MsZUFBZTt3Q0FDakJLLGlCQUFpQmdDLEdBQUc3RyxLQUFLLENBQUNpRCxFQUFFO3dDQUM1QmdDLGlCQUFpQjRCLEdBQUc3RyxLQUFLO29DQUMzQjtvQ0FDQXlFLFlBQVlTLENBQUFBLDJCQUFBQSxzQ0FBQUEsMEJBQUFBLGVBQWdCNEIsT0FBTyxjQUF2QjVCLDhDQUFBQSx3QkFBeUJqQyxFQUFFLE1BQUs0RCxHQUFHN0csS0FBSyxDQUFDaUQsRUFBRTs7Ozs7OytCQXBCcEQ0RCxHQUFHN0csS0FBSyxDQUFDaUQsRUFBRTs7Ozs7O3dCQXlCbkJ5RCx5QkFDQyw4REFBQ2hEOzRCQUNDRSxXQUFVOzRCQUNWRSxPQUFPO2dDQUNMWSxVQUFVO2dDQUNWUCxLQUFLLEdBQW1DLE9BQWhDZCxxQkFBcUJDLFlBQVc7Z0NBQ3hDYyxNQUFNOzRCQUNSOzRCQUNBeEIsU0FBUyxDQUFDVjtnQ0FDUkEsRUFBRXNDLGVBQWU7Z0NBQ2pCTSxnQkFBZ0J4RDs0QkFDbEI7O2dDQUNEO2dDQUNJSyxhQUFhbEIsTUFBTSxHQUFHNEM7Z0NBQW1COzs7Ozs7Ozs7Ozs7Ozs7SUFNeEQ7SUFFQSxtQkFBbUI7SUFDbkIscUJBQ0UsOERBQUNLO1FBQUlFLFdBQVU7OzBCQUNiLDhEQUFDRjtnQkFBSUUsV0FBVTs7a0NBRWIsOERBQUNGO3dCQUNDcUQsb0JBQWlCO3dCQUNqQm5ELFdBQVU7a0NBRVQ7NEJBQUM7NEJBQVU7NEJBQVU7NEJBQVc7NEJBQWE7NEJBQVk7NEJBQVU7eUJBQVcsQ0FBQ0ksR0FBRyxDQUFDaUMsQ0FBQUEsd0JBQ2xGLDhEQUFDdkM7Z0NBQWtCRSxXQUFXcEYsOENBQUVBLENBQzlCLHdDQUNBOzBDQUVDeUgsUUFBUUMsU0FBUyxDQUFDLEdBQUc7K0JBSmREOzs7Ozs7Ozs7O2tDQVVkLDhEQUFDdEgsa0VBQVVBO3dCQUFDaUYsV0FBVTtrQ0FDcEIsNEVBQUNGOzRCQUFJRSxXQUFVO3NDQUNaeUIsa0JBQWtCMUUsS0FBSyxDQUFDcUQsR0FBRyxDQUFDLENBQUNsRCxNQUFNQyxZQUNsQ0QsS0FBS2tELEdBQUcsQ0FBQyxDQUFDMUMsS0FBSzBGO29DQUNiLE1BQU05RixhQUFhMkUscUJBQXFCb0IsR0FBRyxDQUFDbEcsY0FBYyxFQUFFO29DQUM1RCxNQUFNeUYsWUFBWXRGLFdBQVdDLE1BQU0sQ0FBQzBGLENBQUFBLEtBQU1BLEdBQUduSCxPQUFPLENBQUNDLGFBQWEsQ0FBQ3lCLGFBQWEsS0FBSzRGO29DQUNyRixNQUFNckUsaUJBQWlCckIsSUFBSW1GLFFBQVEsT0FBTzdCLGFBQWE2QixRQUFRO29DQUUvRCxxQkFDRSw4REFBQy9EO3dDQUVDeEQsTUFBTW9DO3dDQUNOcUIsZ0JBQWdCQTt3Q0FDaEJDLFNBQVMsSUFBTWtDLGdCQUFnQnhEO3dDQUMvQm5DLFFBQVF5Rzt3Q0FDUnhHLGdCQUFnQjhCO3dDQUNoQjJCLGNBQWNvQzt3Q0FDZG5DLGVBQWVBO3VDQVBWLEdBQWdCa0UsT0FBYmpHLFdBQVUsS0FBWSxPQUFUaUc7Ozs7O2dDQVUzQjs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTVIsOERBQUNqSSwrREFBZ0JBO2dCQUNmNkYsY0FBY0E7Z0JBQ2R6RixRQUFRQTtnQkFDUjJELGVBQWVBO2dCQUNmK0Isa0JBQWtCQTtnQkFDbEJJLGtCQUFrQkE7Ozs7Ozs7Ozs7OztBQUkxQixFQUFFO0lBN1FXTjs7UUFXUy9GLDZEQUFjQTs7O01BWHZCK0Y7QUErUWIsU0FBUzFFLFdBQVdELEtBQW9CO0lBQ3RDLE9BQU8sQ0FBQzVCLCtLQUFTQSxDQUFDLElBQUlxQixLQUFLTyxNQUFNSixLQUFLLEdBQUcsSUFBSUgsS0FBS08sTUFBTUYsR0FBRztBQUM3RCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy93b3Jrc3BhY2UvbWFpbi92aWV3cy9jYWxlbmRhci9jb21wb25lbnRzL01vbnRoVmlldy50c3g/YjQ1ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QsIHsgdXNlTWVtbyB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IGZvcm1hdCwgc3RhcnRPZk1vbnRoLCBlbmRPZk1vbnRoLCBzdGFydE9mV2VlaywgZW5kT2ZXZWVrLCBhZGREYXlzLCBpc1NhbWVEYXksIGlzVG9kYXksIHN0YXJ0T2ZEYXksIGVuZE9mRGF5IH0gZnJvbSAnZGF0ZS1mbnMnO1xuaW1wb3J0IHsgY24gfSBmcm9tICdAL2xpYi91dGlscyc7XG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJztcbmltcG9ydCB7IFBsdXNJY29uIH0gZnJvbSAnQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lJztcbmltcG9ydCB7IFNjcm9sbEFyZWEgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvc2Nyb2xsLWFyZWEnO1xuaW1wb3J0IHsgQ2FsZW5kYXJFdmVudCB9IGZyb20gJ0AvdHlwaW5ncy9wYWdlJztcbmltcG9ydCB7IHVzZU1heWJlUmVjb3JkIH0gZnJvbSAnQC9wcm92aWRlcnMvcmVjb3JkJztcbmltcG9ydCB7IENhbGVuZGFyRXZlbnRJdGVtIH0gZnJvbSAnLi9DYWxlbmRhckV2ZW50SXRlbSc7XG5pbXBvcnQgeyBOb0V2ZW50cyB9IGZyb20gJy4vTm9FdmVudHMnO1xuaW1wb3J0IHsgQ2FsZW5kYXJTaWRlQ2FyZCB9IGZyb20gJy4vQ2FsZW5kYXJTaWRlQ2FyZCc7XG5pbXBvcnQgeyB1c2VEcm9wcGFibGUgfSBmcm9tICdAZG5kLWtpdC9jb3JlJztcblxuXG5pbnRlcmZhY2UgTW9udGhWaWV3UHJvcHMge1xuICBzZWxlY3RlZERhdGU6IERhdGU7XG4gIGV2ZW50czogQ2FsZW5kYXJFdmVudFtdO1xuICBzZWxlY3RlZEV2ZW50OiBzdHJpbmcgfCBudWxsO1xuICBzZXRTZWxlY3RlZEV2ZW50OiAoaWQ6IHN0cmluZykgPT4gdm9pZDtcbiAgc2V0U2VsZWN0ZWREYXRlOiAoZGF0ZTogRGF0ZSkgPT4gdm9pZDtcbiAgb3BlbkFkZEV2ZW50Rm9ybTogKGRhdGU6IERhdGUpID0+IHZvaWQ7XG4gIGNhbkVkaXREYXRhOiBib29sZWFuO1xuICBoYW5kbGVFdmVudENsaWNrOiAoZXZlbnQ6IENhbGVuZGFyRXZlbnQpID0+IHZvaWQ7XG4gIGFjdGl2ZURyYWdEYXRhOiBhbnk7XG59XG5cbmludGVyZmFjZSBTcGFubmluZ0V2ZW50IHtcbiAgc2VnbWVudDoge1xuICAgIG9yaWdpbmFsRXZlbnQ6IENhbGVuZGFyRXZlbnQgJiB7XG4gICAgICBzdGFydERheUluZGV4PzogbnVtYmVyO1xuICAgIH07XG4gIH07XG4gIHJvdzogbnVtYmVyO1xuICBjb2xTcGFuOiBudW1iZXI7XG59XG5cbmludGVyZmFjZSBFdmVudFNsb3RJbmZvIHtcbiAgZXZlbnQ6IENhbGVuZGFyRXZlbnQ7XG4gIGlzTXVsdGlEYXk6IGJvb2xlYW47XG4gIHJvdz86IG51bWJlcjtcbiAgY29sU3Bhbj86IG51bWJlcjtcbn1cblxuLy8gSGVscGVyIGZ1bmN0aW9uIHRvIGNvdW50IGV2ZW50cyBpbiBhIGNlbGxcbmNvbnN0IGdldEV2ZW50c0ZvckNlbGwgPSAoZGF0ZTogRGF0ZSwgZXZlbnRzOiBDYWxlbmRhckV2ZW50W10sIHNwYW5uaW5nRXZlbnRzOiBTcGFubmluZ0V2ZW50W10pID0+IHtcbiAgY29uc3QgY2VsbEV2ZW50czogRXZlbnRTbG90SW5mb1tdID0gW107XG5cbiAgLy8gQWRkIHNwYW5uaW5nIChtdWx0aS1kYXkpIGV2ZW50cyB0aGF0IHBhc3MgdGhyb3VnaCB0aGlzIGNlbGxcbiAgc3Bhbm5pbmdFdmVudHMuZm9yRWFjaChzcGFubmluZ0V2ZW50ID0+IHtcbiAgICBjb25zdCBldmVudFN0YXJ0ID0gbmV3IERhdGUoc3Bhbm5pbmdFdmVudC5zZWdtZW50Lm9yaWdpbmFsRXZlbnQuc3RhcnQpO1xuICAgIGNvbnN0IGV2ZW50RW5kID0gbmV3IERhdGUoc3Bhbm5pbmdFdmVudC5zZWdtZW50Lm9yaWdpbmFsRXZlbnQuZW5kKTtcbiAgICBcbiAgICBpZiAoZGF0ZSA+PSBzdGFydE9mRGF5KGV2ZW50U3RhcnQpICYmIGRhdGUgPD0gZW5kT2ZEYXkoZXZlbnRFbmQpKSB7XG4gICAgICBjZWxsRXZlbnRzLnB1c2goe1xuICAgICAgICBldmVudDogc3Bhbm5pbmdFdmVudC5zZWdtZW50Lm9yaWdpbmFsRXZlbnQsXG4gICAgICAgIGlzTXVsdGlEYXk6IHRydWUsXG4gICAgICAgIHJvdzogc3Bhbm5pbmdFdmVudC5yb3csXG4gICAgICAgIGNvbFNwYW46IHNwYW5uaW5nRXZlbnQuY29sU3BhblxuICAgICAgfSk7XG4gICAgfVxuICB9KTtcblxuICAvLyBBZGQgc2luZ2xlLWRheSBldmVudHMgZm9yIHRoaXMgY2VsbFxuICBldmVudHMuZm9yRWFjaChldmVudCA9PiB7XG4gICAgY29uc3QgZXZlbnRTdGFydCA9IG5ldyBEYXRlKGV2ZW50LnN0YXJ0KTtcbiAgICBpZiAoaXNTYW1lRGF5KGRhdGUsIGV2ZW50U3RhcnQpKSB7XG4gICAgICBjZWxsRXZlbnRzLnB1c2goe1xuICAgICAgICBldmVudCxcbiAgICAgICAgaXNNdWx0aURheTogZmFsc2VcbiAgICAgIH0pO1xuICAgIH1cbiAgfSk7XG5cbiAgcmV0dXJuIHtcbiAgICB2aXNpYmxlRXZlbnRzOiBjZWxsRXZlbnRzLnNsaWNlKDAsIDQpLFxuICAgIGhpZGRlbkNvdW50OiBNYXRoLm1heCgwLCBjZWxsRXZlbnRzLmxlbmd0aCAtIDQpXG4gIH07XG59O1xuXG4vLyBOZXcgaGVscGVyIGZ1bmN0aW9uIHRvIHByb2Nlc3MgZXZlbnRzIGZvciB0aGUgZW50aXJlIG1vbnRoXG5jb25zdCB1c2VNb250aEV2ZW50cyA9ICh3ZWVrczogRGF0ZVtdW10sIGV2ZW50czogQ2FsZW5kYXJFdmVudFtdKSA9PiB7XG4gIHJldHVybiB1c2VNZW1vKCgpID0+IHtcbiAgICBjb25zdCBwb3NpdGlvbmVkRXZlbnRzQnlXZWVrID0gbmV3IE1hcDxudW1iZXIsIGFueVtdPigpO1xuXG4gICAgd2Vla3MuZm9yRWFjaCgod2Vlaywgd2Vla0luZGV4KSA9PiB7XG4gICAgICBjb25zdCB3ZWVrU3RhcnQgPSB3ZWVrWzBdO1xuICAgICAgY29uc3Qgd2Vla0VuZCA9IHdlZWtbNl07XG5cbiAgICAgIGNvbnN0IHdlZWtFdmVudHMgPSBldmVudHMuZmlsdGVyKGV2ZW50ID0+IHtcbiAgICAgICAgY29uc3QgZXZlbnRTdGFydCA9IG5ldyBEYXRlKGV2ZW50LnN0YXJ0KTtcbiAgICAgICAgY29uc3QgZXZlbnRFbmQgPSBuZXcgRGF0ZShldmVudC5lbmQpO1xuICAgICAgICByZXR1cm4gZXZlbnRTdGFydCA8PSB3ZWVrRW5kICYmIGV2ZW50RW5kID49IHdlZWtTdGFydDtcbiAgICAgIH0pO1xuXG4gICAgICBjb25zdCBzcGFubmluZ0V2ZW50czogYW55W10gPSBbXTtcbiAgICAgIHdlZWtFdmVudHMuZm9yRWFjaChldmVudCA9PiB7XG4gICAgICAgIGNvbnN0IGV2ZW50U3RhcnQgPSBuZXcgRGF0ZShldmVudC5zdGFydCk7XG4gICAgICAgIGNvbnN0IGV2ZW50RW5kID0gbmV3IERhdGUoZXZlbnQuZW5kKTtcbiAgICAgICAgXG4gICAgICAgIGNvbnN0IHN0YXJ0RGF5SW5kZXggPSB3ZWVrLmZpbmRJbmRleChkYXkgPT4gaXNTYW1lRGF5KGRheSwgZXZlbnRTdGFydCkpO1xuICAgICAgICBjb25zdCBlbmREYXlJbmRleCA9IHdlZWsuZmluZEluZGV4KGRheSA9PiBpc1NhbWVEYXkoZGF5LCBldmVudEVuZCkpO1xuXG4gICAgICAgIGNvbnN0IGV2ZW50U3BhbnNXZWVrID0gc3RhcnREYXlJbmRleCAhPT0gLTEgfHwgZW5kRGF5SW5kZXggIT09IC0xIHx8IChldmVudFN0YXJ0IDwgd2Vla1N0YXJ0ICYmIGV2ZW50RW5kID4gd2Vla0VuZCk7XG5cbiAgICAgICAgaWYgKGV2ZW50U3BhbnNXZWVrKSB7XG4gICAgICAgICAgY29uc3Qgc3RhcnQgPSBzdGFydERheUluZGV4ICE9PSAtMSA/IHN0YXJ0RGF5SW5kZXggOiAwO1xuICAgICAgICAgIGNvbnN0IGVuZCA9IGVuZERheUluZGV4ICE9PSAtMSA/IGVuZERheUluZGV4IDogNjtcbiAgICAgICAgICBzcGFubmluZ0V2ZW50cy5wdXNoKHtcbiAgICAgICAgICAgIGV2ZW50LFxuICAgICAgICAgICAgc3RhcnREYXlJbmRleDogc3RhcnQsXG4gICAgICAgICAgICBlbmREYXlJbmRleDogZW5kLFxuICAgICAgICAgICAgY29sU3BhbjogZW5kIC0gc3RhcnQgKyAxLFxuICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICB9KTtcbiAgICAgIFxuICAgICAgY29uc3QgcG9zaXRpb25lZDogYW55W10gPSBbXTtcbiAgICAgIGNvbnN0IHJvd3M6IGFueVtdW10gPSBbXTtcbiAgICAgIGNvbnN0IHNvcnRlZEV2ZW50cyA9IHNwYW5uaW5nRXZlbnRzLnNvcnQoKGEsIGIpID0+IGEuc3RhcnREYXlJbmRleCAtIGIuc3RhcnREYXlJbmRleCB8fCBiLmNvbFNwYW4gLSBhLmNvbFNwYW4pO1xuXG4gICAgICBzb3J0ZWRFdmVudHMuZm9yRWFjaChldmVudCA9PiB7XG4gICAgICAgIGxldCBhc3NpZ25lZCA9IGZhbHNlO1xuICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHJvd3MubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgICBjb25zdCByb3cgPSByb3dzW2ldO1xuICAgICAgICAgIGlmICghcm93LnNvbWUoZSA9PiBldmVudC5zdGFydERheUluZGV4IDw9IGUuZW5kRGF5SW5kZXggJiYgZXZlbnQuZW5kRGF5SW5kZXggPj0gZS5zdGFydERheUluZGV4KSkge1xuICAgICAgICAgICAgcm93LnB1c2goZXZlbnQpO1xuICAgICAgICAgICAgcG9zaXRpb25lZC5wdXNoKHsgLi4uZXZlbnQsIHJvdzogaSB9KTtcbiAgICAgICAgICAgIGFzc2lnbmVkID0gdHJ1ZTtcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBpZiAoIWFzc2lnbmVkKSB7XG4gICAgICAgICAgcm93cy5wdXNoKFtldmVudF0pO1xuICAgICAgICAgIHBvc2l0aW9uZWQucHVzaCh7IC4uLmV2ZW50LCByb3c6IHJvd3MubGVuZ3RoIC0gMSB9KTtcbiAgICAgICAgfVxuICAgICAgfSk7XG4gICAgICBwb3NpdGlvbmVkRXZlbnRzQnlXZWVrLnNldCh3ZWVrSW5kZXgsIHBvc2l0aW9uZWQpO1xuICAgIH0pO1xuXG4gICAgcmV0dXJuIHBvc2l0aW9uZWRFdmVudHNCeVdlZWs7XG4gIH0sIFt3ZWVrcywgZXZlbnRzXSk7XG59O1xuXG4vLyBIZWxwZXIgZnVuY3Rpb24gdG8gY2hlY2sgaWYgdHdvIGV2ZW50cyBvdmVybGFwXG5jb25zdCBoYXNPdmVybGFwID0gKGE6IFNwYW5uaW5nRXZlbnQsIGI6IFNwYW5uaW5nRXZlbnQpID0+IHtcbiAgY29uc3QgYVN0YXJ0ID0gbmV3IERhdGUoYS5zZWdtZW50Lm9yaWdpbmFsRXZlbnQuc3RhcnQpO1xuICBjb25zdCBhRW5kID0gbmV3IERhdGUoYS5zZWdtZW50Lm9yaWdpbmFsRXZlbnQuZW5kKTtcbiAgY29uc3QgYlN0YXJ0ID0gbmV3IERhdGUoYi5zZWdtZW50Lm9yaWdpbmFsRXZlbnQuc3RhcnQpO1xuICBjb25zdCBiRW5kID0gbmV3IERhdGUoYi5zZWdtZW50Lm9yaWdpbmFsRXZlbnQuZW5kKTtcbiAgcmV0dXJuIGFTdGFydCA8PSBiRW5kICYmIGFFbmQgPj0gYlN0YXJ0O1xufTtcblxuLy8gSGVscGVyIGZ1bmN0aW9uIHRvIHBvc2l0aW9uIHNwYW5uaW5nIGV2ZW50c1xuY29uc3QgcG9zaXRpb25TcGFubmluZ0V2ZW50cyA9IChldmVudHM6IFNwYW5uaW5nRXZlbnRbXSk6IFNwYW5uaW5nRXZlbnRbXSA9PiB7XG4gIGNvbnN0IHBvc2l0aW9uZWQ6IFNwYW5uaW5nRXZlbnRbXSA9IFtdO1xuICBjb25zdCByb3dzOiBTcGFubmluZ0V2ZW50W11bXSA9IFtdO1xuICBcbiAgLy8gU29ydCBldmVudHMgYnkgdGhlaXIgc3BhbiBsZW5ndGggKGxvbmdlciBldmVudHMgZmlyc3QpXG4gIGNvbnN0IHNvcnRlZEV2ZW50cyA9IFsuLi5ldmVudHNdLnNvcnQoKGEsIGIpID0+IFxuICAgIGEuY29sU3BhbiA9PT0gYi5jb2xTcGFuID8gMCA6IGIuY29sU3BhbiAtIGEuY29sU3BhblxuICApO1xuXG4gIHNvcnRlZEV2ZW50cy5mb3JFYWNoKGV2ZW50ID0+IHtcbiAgICBsZXQgYXNzaWduZWQgPSBmYWxzZTtcbiAgICAvLyBUcnkgdG8gZml0IGluIGV4aXN0aW5nIHJvd3NcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IHJvd3MubGVuZ3RoOyBpKyspIHtcbiAgICAgIGNvbnN0IHJvdyA9IHJvd3NbaV07XG4gICAgICBpZiAoIXJvdy5zb21lKGUgPT4gaGFzT3ZlcmxhcChldmVudCwgZSkpKSB7XG4gICAgICAgIHJvdy5wdXNoKGV2ZW50KTtcbiAgICAgICAgcG9zaXRpb25lZC5wdXNoKHsgLi4uZXZlbnQsIHJvdzogaSB9KTtcbiAgICAgICAgYXNzaWduZWQgPSB0cnVlO1xuICAgICAgICBicmVhaztcbiAgICAgIH1cbiAgICB9XG4gICAgLy8gSWYgbm8gZXhpc3Rpbmcgcm93IHdvcmtzLCBjcmVhdGUgYSBuZXcgcm93XG4gICAgaWYgKCFhc3NpZ25lZCkge1xuICAgICAgcm93cy5wdXNoKFtldmVudF0pO1xuICAgICAgcG9zaXRpb25lZC5wdXNoKHsgLi4uZXZlbnQsIHJvdzogcm93cy5sZW5ndGggLSAxIH0pO1xuICAgIH1cbiAgfSk7XG5cbiAgcmV0dXJuIHBvc2l0aW9uZWQ7XG59O1xuXG5pbnRlcmZhY2UgRGF5Q2VsbFByb3BzIHtcbiAgZGF0ZTogRGF0ZTtcbiAgaXNDdXJyZW50TW9udGg6IGJvb2xlYW47XG4gIG9uQ2xpY2s6ICgpID0+IHZvaWQ7XG4gIGV2ZW50czogQ2FsZW5kYXJFdmVudFtdO1xuICBzcGFubmluZ0V2ZW50czogU3Bhbm5pbmdFdmVudFtdO1xuICBvbkV2ZW50Q2xpY2s6IChldmVudDogQ2FsZW5kYXJFdmVudCkgPT4gdm9pZDtcbiAgc2VsZWN0ZWRFdmVudDogc3RyaW5nIHwgbnVsbDtcbn1cblxuY29uc3QgRGF5Q2VsbDogUmVhY3QuRkM8RGF5Q2VsbFByb3BzPiA9ICh7XG4gIGRhdGUsXG4gIGlzQ3VycmVudE1vbnRoLFxuICBvbkNsaWNrLFxuICBldmVudHMsXG4gIHNwYW5uaW5nRXZlbnRzLFxuICBvbkV2ZW50Q2xpY2ssXG4gIHNlbGVjdGVkRXZlbnQsXG59KSA9PiB7XG4gIGNvbnN0IHsgc2V0Tm9kZVJlZiwgaXNPdmVyIH0gPSB1c2VEcm9wcGFibGUoe1xuICAgIGlkOiBgZGF5Y2VsbC0ke2Zvcm1hdChkYXRlLCAneXl5eS1NTS1kZCcpfWAsXG4gICAgZGF0YToge1xuICAgICAgdHlwZTogJ2RheWNlbGwnLFxuICAgICAgZGF0ZTogZGF0ZVxuICAgIH1cbiAgfSk7XG5cbiAgY29uc3QgeyB2aXNpYmxlRXZlbnRzLCBoaWRkZW5Db3VudCB9ID0gZ2V0RXZlbnRzRm9yQ2VsbChkYXRlLCBldmVudHMsIHNwYW5uaW5nRXZlbnRzKTtcbiAgY29uc3QgaXNDdXJyZW50RGF5ID0gaXNUb2RheShkYXRlKTtcbiAgY29uc3QgTUFYX1ZJU0lCTEVfRVZFTlRTID0gNDtcbiAgY29uc3QgUk9XX0hFSUdIVCA9IDI4O1xuXG4gIC8vIENhbGN1bGF0ZSBjb250YWluZXIgaGVpZ2h0XG4gIGNvbnN0IG1heFJvdyA9IHZpc2libGVFdmVudHMucmVkdWNlKChtYXgsIGV2ZW50KSA9PiB7XG4gICAgaWYgKCdyb3cnIGluIGV2ZW50KSB7XG4gICAgICByZXR1cm4gTWF0aC5tYXgobWF4LCBldmVudC5yb3cgfHwgMCk7XG4gICAgfVxuICAgIHJldHVybiBtYXg7XG4gIH0sIDApO1xuICBcbiAgY29uc3QgY29udGFpbmVySGVpZ2h0ID0gaGlkZGVuQ291bnQgPiAwXG4gICAgPyAoTUFYX1ZJU0lCTEVfRVZFTlRTICogUk9XX0hFSUdIVCkgKyAyMCBcbiAgICA6ICgobWF4Um93ICsgMSkgKiBST1dfSEVJR0hUKTtcblxuICByZXR1cm4gKFxuICAgIDxkaXZcbiAgICAgIHJlZj17c2V0Tm9kZVJlZn1cbiAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgIFwiZ3JvdXAgbWluLWgtWzEyMHB4XSBwLTIgYm9yZGVyLWIgYm9yZGVyLXIgYm9yZGVyLW5ldXRyYWwtMzAwXCIsXG4gICAgICAgIFwiY3Vyc29yLXBvaW50ZXIgaG92ZXI6YmctbmV1dHJhbC01MCB0cmFuc2l0aW9uLWNvbG9yc1wiLFxuICAgICAgICAhaXNDdXJyZW50TW9udGggJiYgXCJiZy1uZXV0cmFsLTUwXCIsXG4gICAgICAgIGlzT3ZlciAmJiBcImJnLWJsdWUtNTBcIlxuICAgICAgKX1cbiAgICAgIG9uQ2xpY2s9e29uQ2xpY2t9XG4gICAgPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItMlwiPlxuICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgIFwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHJvdW5kZWQtZnVsbCBmb250LXNlbWlib2xkIHRleHQteHMgdy02IGgtNlwiLFxuICAgICAgICAgIGlzQ3VycmVudERheSA/IFwiYmctYmxhY2sgdGV4dC13aGl0ZVwiIDogaXNDdXJyZW50TW9udGggPyBcInRleHQtYmxhY2sgaG92ZXI6YmctbmV1dHJhbC0xMDBcIiA6IFwidGV4dC1uZXV0cmFsLTQwMFwiXG4gICAgICAgICl9PlxuICAgICAgICAgIHtmb3JtYXQoZGF0ZSwgJ2QnKX1cbiAgICAgICAgPC9zcGFuPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIiBzdHlsZT17eyBoZWlnaHQ6IGAke2NvbnRhaW5lckhlaWdodH1weGAgfX0+XG4gICAgICAgIHt2aXNpYmxlRXZlbnRzLm1hcChldmVudEluZm8gPT4ge1xuICAgICAgICAgIGNvbnN0IHJvdyA9ICdyb3cnIGluIGV2ZW50SW5mbyAmJiBldmVudEluZm8ucm93ICE9PSB1bmRlZmluZWQgPyBldmVudEluZm8ucm93IDogMDtcbiAgICAgICAgICBjb25zdCBjb2xTcGFuID0gJ2NvbFNwYW4nIGluIGV2ZW50SW5mbyAmJiBldmVudEluZm8uY29sU3BhbiAhPT0gdW5kZWZpbmVkID8gZXZlbnRJbmZvLmNvbFNwYW4gOiAxO1xuICAgICAgICAgIFxuICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgIGtleT17ZXZlbnRJbmZvLmV2ZW50LmlkfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZVwiXG4gICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgdG9wOiBgJHtyb3cgKiBST1dfSEVJR0hUfXB4YCxcbiAgICAgICAgICAgICAgICBsZWZ0OiAnMnB4JyxcbiAgICAgICAgICAgICAgICB3aWR0aDogY29sU3BhbiA+IDEgXG4gICAgICAgICAgICAgICAgICA/IGBjYWxjKCR7Y29sU3BhbiAqIDEwMH0lICsgJHsoY29sU3BhbiAtIDEpICogMTl9cHgpYFxuICAgICAgICAgICAgICAgICAgOiAnY2FsYygxMDAlIC0gNHB4KScsXG4gICAgICAgICAgICAgICAgekluZGV4OiAxMCArIHJvdyxcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPENhbGVuZGFyRXZlbnRJdGVtXG4gICAgICAgICAgICAgICAgZXZlbnQ9e2V2ZW50SW5mby5ldmVudH1cbiAgICAgICAgICAgICAgICB2aWV3PVwibW9udGhcIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xuICAgICAgICAgICAgICAgICAgb25FdmVudENsaWNrKGV2ZW50SW5mby5ldmVudCk7XG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICBpc0RyYWdnaW5nPXtmYWxzZX1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICk7XG4gICAgICAgIH0pfVxuXG4gICAgICAgIHtoaWRkZW5Db3VudCA+IDAgJiYgKFxuICAgICAgICAgIDxkaXYgXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWJsYWNrIGhvdmVyOnRleHQtYmxhY2sgZm9udC1tZWRpdW0gdGV4dC14cyBjdXJzb3ItcG9pbnRlclwiXG4gICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICBwb3NpdGlvbjogJ2Fic29sdXRlJyxcbiAgICAgICAgICAgICAgdG9wOiBgJHtNQVhfVklTSUJMRV9FVkVOVFMgKiBST1dfSEVJR0hUfXB4YCxcbiAgICAgICAgICAgICAgbGVmdDogJzJweCcsXG4gICAgICAgICAgICB9fVxuICAgICAgICAgICAgb25DbGljaz17KGUpID0+IHtcbiAgICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKTtcbiAgICAgICAgICAgICAgb25DbGljaygpO1xuICAgICAgICAgICAgfX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICArIHtoaWRkZW5Db3VudH0gbW9yZVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG5leHBvcnQgY29uc3QgTW9udGhWaWV3OiBSZWFjdC5GQzxNb250aFZpZXdQcm9wcz4gPSAoe1xuICBzZWxlY3RlZERhdGUsXG4gIGV2ZW50cyxcbiAgc2VsZWN0ZWRFdmVudCxcbiAgc2V0U2VsZWN0ZWRFdmVudCxcbiAgc2V0U2VsZWN0ZWREYXRlLFxuICBvcGVuQWRkRXZlbnRGb3JtLFxuICBjYW5FZGl0RGF0YSxcbiAgaGFuZGxlRXZlbnRDbGljayxcbiAgYWN0aXZlRHJhZ0RhdGEsXG59KSA9PiB7XG4gIGNvbnN0IG1heWJlUmVjb3JkID0gdXNlTWF5YmVSZWNvcmQoKTtcbiAgY29uc3QgaXNJblJlY29yZFRhYiA9ICEhbWF5YmVSZWNvcmQ7XG5cbiAgLy8gTWVtb2l6ZSBtb250aCBjYWxjdWxhdGlvbnNcbiAgY29uc3QgbW9udGhDYWxjdWxhdGlvbnMgPSB1c2VNZW1vKCgpID0+IHtcbiAgICBjb25zdCBtb250aFN0YXJ0ID0gc3RhcnRPZk1vbnRoKHNlbGVjdGVkRGF0ZSk7XG4gICAgY29uc3QgbW9udGhFbmQgPSBlbmRPZk1vbnRoKHNlbGVjdGVkRGF0ZSk7XG4gICAgY29uc3Qgc3RhcnREYXkgPSBzdGFydE9mV2Vlayhtb250aFN0YXJ0LCB7IHdlZWtTdGFydHNPbjogMCB9KTtcbiAgICBjb25zdCBlbmREYXkgPSBlbmRPZldlZWsobW9udGhFbmQsIHsgd2Vla1N0YXJ0c09uOiAwIH0pO1xuXG4gICAgY29uc3QgZGF5cyA9IFtdO1xuICAgIGxldCBkYXkgPSBzdGFydERheTtcbiAgICB3aGlsZSAoZGF5IDw9IGVuZERheSkge1xuICAgICAgZGF5cy5wdXNoKGRheSk7XG4gICAgICBkYXkgPSBhZGREYXlzKGRheSwgMSk7XG4gICAgfVxuXG4gICAgY29uc3Qgd2Vla3MgPSBbXTtcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IGRheXMubGVuZ3RoOyBpICs9IDcpIHtcbiAgICAgIHdlZWtzLnB1c2goZGF5cy5zbGljZShpLCBpICsgNykpO1xuICAgIH1cblxuICAgIHJldHVybiB7IG1vbnRoU3RhcnQsIG1vbnRoRW5kLCBzdGFydERheSwgZW5kRGF5LCBkYXlzLCB3ZWVrcyB9O1xuICB9LCBbc2VsZWN0ZWREYXRlXSk7XG5cbiAgLy8gR2V0IGFsbCBldmVudHMgZm9yIHRoZSB2aXNpYmxlIG1vbnRoIHJhbmdlXG4gIGNvbnN0IG1vbnRoRXZlbnRzID0gdXNlTWVtbygoKSA9PiBcbiAgICBldmVudHMuZmlsdGVyKGV2ZW50ID0+IHtcbiAgICAgIGNvbnN0IGV2ZW50U3RhcnQgPSBuZXcgRGF0ZShldmVudC5zdGFydCk7XG4gICAgICBjb25zdCBldmVudEVuZCA9IG5ldyBEYXRlKGV2ZW50LmVuZCk7XG4gICAgICByZXR1cm4gZXZlbnRTdGFydCA8PSBtb250aENhbGN1bGF0aW9ucy5lbmREYXkgJiYgXG4gICAgICAgICAgICAgZXZlbnRFbmQgPj0gbW9udGhDYWxjdWxhdGlvbnMuc3RhcnREYXk7XG4gICAgfSksIFxuICAgIFtldmVudHMsIG1vbnRoQ2FsY3VsYXRpb25zLnN0YXJ0RGF5LCBtb250aENhbGN1bGF0aW9ucy5lbmREYXldXG4gICk7XG5cbiAgLy8gQ2FsY3VsYXRlIHNwYW5uaW5nIGV2ZW50cyBmb3IgZWFjaCB3ZWVrXG4gIGNvbnN0IHdlZWtseVNwYW5uaW5nRXZlbnRzID0gdXNlTWVtbygoKSA9PiB7XG4gICAgY29uc3Qgc3Bhbm5pbmdFdmVudHNCeVdlZWsgPSBuZXcgTWFwPG51bWJlciwgU3Bhbm5pbmdFdmVudFtdPigpO1xuICAgIFxuICAgIG1vbnRoQ2FsY3VsYXRpb25zLndlZWtzLmZvckVhY2goKHdlZWssIHdlZWtJbmRleCkgPT4ge1xuICAgICAgY29uc3Qgd2Vla1N0YXJ0ID0gd2Vla1swXTtcbiAgICAgIGNvbnN0IHdlZWtFbmQgPSB3ZWVrWzZdO1xuICAgICAgXG4gICAgICAvLyBGaWx0ZXIgZXZlbnRzIHRoYXQgc3BhbiB0aGlzIHdlZWtcbiAgICAgIGNvbnN0IHdlZWtFdmVudHMgPSBtb250aEV2ZW50cy5maWx0ZXIoZXZlbnQgPT4ge1xuICAgICAgICBjb25zdCBldmVudFN0YXJ0ID0gbmV3IERhdGUoZXZlbnQuc3RhcnQpO1xuICAgICAgICBjb25zdCBldmVudEVuZCA9IG5ldyBEYXRlKGV2ZW50LmVuZCk7XG4gICAgICAgIHJldHVybiBldmVudFN0YXJ0IDw9IHdlZWtFbmQgJiYgZXZlbnRFbmQgPj0gd2Vla1N0YXJ0O1xuICAgICAgfSk7XG5cbiAgICAgIC8vIFByb2Nlc3Mgc3Bhbm5pbmcgZXZlbnRzXG4gICAgICBjb25zdCBzcGFubmluZ0V2ZW50czogU3Bhbm5pbmdFdmVudFtdID0gW107XG4gICAgICB3ZWVrRXZlbnRzLmZvckVhY2goZXZlbnQgPT4ge1xuICAgICAgICBjb25zdCBldmVudFN0YXJ0ID0gbmV3IERhdGUoZXZlbnQuc3RhcnQpO1xuICAgICAgICBjb25zdCBldmVudEVuZCA9IG5ldyBEYXRlKGV2ZW50LmVuZCk7XG4gICAgICAgIFxuICAgICAgICBjb25zdCBzdGFydERheUluZGV4ID0gd2Vlay5maW5kSW5kZXgoZGF5ID0+IFxuICAgICAgICAgIGlzU2FtZURheShkYXksIGV2ZW50U3RhcnQpIHx8IGRheSA+IGV2ZW50U3RhcnRcbiAgICAgICAgKTtcbiAgICAgICAgY29uc3QgZW5kRGF5SW5kZXggPSB3ZWVrLmZpbmRJbmRleChkYXkgPT4gXG4gICAgICAgICAgaXNTYW1lRGF5KGRheSwgZXZlbnRFbmQpIHx8IGRheSA+IGV2ZW50RW5kXG4gICAgICAgICk7XG5cbiAgICAgICAgY29uc3Qgc3RhcnQgPSBzdGFydERheUluZGV4ICE9PSAtMSA/IHN0YXJ0RGF5SW5kZXggOiAwO1xuICAgICAgICBjb25zdCBlbmQgPSBlbmREYXlJbmRleCAhPT0gLTEgPyBlbmREYXlJbmRleCA6IDY7XG5cbiAgICAgICAgY29uc3QgZXh0ZW5kZWRFdmVudCA9IHtcbiAgICAgICAgICAuLi5ldmVudCxcbiAgICAgICAgICBzdGFydERheUluZGV4OiBzdGFydFxuICAgICAgICB9O1xuXG4gICAgICAgIHNwYW5uaW5nRXZlbnRzLnB1c2goe1xuICAgICAgICAgIHNlZ21lbnQ6IHsgXG4gICAgICAgICAgICBvcmlnaW5hbEV2ZW50OiBleHRlbmRlZEV2ZW50XG4gICAgICAgICAgfSxcbiAgICAgICAgICByb3c6IDAsIC8vIFdpbGwgYmUgY2FsY3VsYXRlZCBpbiBwb3NpdGlvbmluZ1xuICAgICAgICAgIGNvbFNwYW46IGVuZCAtIHN0YXJ0ICsgMVxuICAgICAgICB9KTtcbiAgICAgIH0pO1xuXG4gICAgICAvLyBQb3NpdGlvbiBzcGFubmluZyBldmVudHMgaW4gcm93c1xuICAgICAgY29uc3QgcG9zaXRpb25lZCA9IHBvc2l0aW9uU3Bhbm5pbmdFdmVudHMoc3Bhbm5pbmdFdmVudHMpO1xuICAgICAgc3Bhbm5pbmdFdmVudHNCeVdlZWsuc2V0KHdlZWtJbmRleCwgcG9zaXRpb25lZCk7XG4gICAgfSk7XG5cbiAgICByZXR1cm4gc3Bhbm5pbmdFdmVudHNCeVdlZWs7XG4gIH0sIFttb250aEV2ZW50cywgbW9udGhDYWxjdWxhdGlvbnMud2Vla3NdKTtcblxuICAvLyBSZW5kZXIgZW1wdHkgc3RhdGUgd2hlbiBubyBldmVudHNcbiAgY29uc3QgcmVuZGVyRW1wdHlTdGF0ZSA9ICgpID0+IChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaC1mdWxsIGJnLWJhY2tncm91bmRcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtNyBib3JkZXItYiBib3JkZXItbmV1dHJhbC0zMDAgYmctd2hpdGVcIj5cbiAgICAgICAge1snU3VuZGF5JywgJ01vbmRheScsICdUdWVzZGF5JywgJ1dlZG5lc2RheScsICdUaHVyc2RheScsICdGcmlkYXknLCAnU2F0dXJkYXknXS5tYXAoZGF5TmFtZSA9PiAoXG4gICAgICAgICAgPGRpdiBrZXk9e2RheU5hbWV9IGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICBcInRleHQtY2VudGVyIGZvbnQtc2VtaWJvbGQgdGV4dC1ibGFja1wiLFxuICAgICAgICAgICAgXCJweS0yIHRleHQteHNcIlxuICAgICAgICAgICl9PlxuICAgICAgICAgICAge2RheU5hbWUuc3Vic3RyaW5nKDAsIDMpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApKX1cbiAgICAgIDwvZGl2PlxuXG4gICAgICA8Tm9FdmVudHNcbiAgICAgICAgdGl0bGU9XCJObyBldmVudHMgdGhpcyBtb250aFwiXG4gICAgICAgIG1lc3NhZ2U9e2Ake2Zvcm1hdChzZWxlY3RlZERhdGUsICdNTU1NIHl5eXknKX0gaXMgY29tcGxldGVseSBmcmVlLiBTdGFydCBwbGFubmluZyB5b3VyIG1vbnRoIWB9XG4gICAgICAgIHNob3dDcmVhdGVCdXR0b249e2NhbkVkaXREYXRhfVxuICAgICAgICBvbkNyZWF0ZT17KCkgPT4gb3BlbkFkZEV2ZW50Rm9ybShzZWxlY3RlZERhdGUpfVxuICAgICAgLz5cbiAgICA8L2Rpdj5cbiAgKTtcblxuICAvLyBSZW5kZXIgZGF5IGNlbGwgY29udGVudFxuICBjb25zdCByZW5kZXJEYXlDZWxsQ29udGVudCA9IChkYXk6IERhdGUsIGRheUV2ZW50czogYW55W10pID0+IHtcbiAgICBjb25zdCBpc0N1cnJlbnRNb250aCA9IGRheS5nZXRNb250aCgpID09PSBzZWxlY3RlZERhdGUuZ2V0TW9udGgoKTtcbiAgICBjb25zdCBpc0N1cnJlbnREYXkgPSBpc1RvZGF5KGRheSk7XG4gICAgY29uc3QgTUFYX1ZJU0lCTEVfRVZFTlRTID0gNDtcbiAgICBjb25zdCBST1dfSEVJR0hUID0gMjg7XG5cbiAgICBjb25zdCBzb3J0ZWRFdmVudHMgPSBkYXlFdmVudHMuc29ydCgoYSwgYikgPT4gYS5yb3cgLSBiLnJvdyk7XG4gICAgY29uc3QgdmlzaWJsZUV2ZW50cyA9IHNvcnRlZEV2ZW50cy5zbGljZSgwLCBNQVhfVklTSUJMRV9FVkVOVFMpO1xuICAgIGNvbnN0IGhhc01vcmUgPSBzb3J0ZWRFdmVudHMubGVuZ3RoID4gTUFYX1ZJU0lCTEVfRVZFTlRTO1xuXG4gICAgLy8gVG8gZml4IHRoZSBnYXAgYWZ0ZXIgdGhlIFwiK21vcmVcIiBsaW5rLCB3ZSBtdXN0IGNhbGN1bGF0ZSB0aGUgY29udGFpbmVyJ3MgaGVpZ2h0XG4gICAgLy8gaW5zdGVhZCBvZiBsZXR0aW5nIGl0IGV4cGFuZC4gVGhlIGxpbmsgaXMgcGxhY2VkIGF0IHRoZSBzdGFydCBvZiB0aGUgNXRoIHJvdyxcbiAgICAvLyBhbmQgd2UnbGwgZ2l2ZSBpdCAyMHB4IG9mIGhlaWdodC5cbiAgICBjb25zdCBtYXhSb3cgPSB2aXNpYmxlRXZlbnRzLnJlZHVjZSgobWF4LCBldmVudCkgPT4gTWF0aC5tYXgobWF4LCBldmVudC5yb3cpLCAtMSk7XG4gICAgY29uc3QgY29udGFpbmVySGVpZ2h0ID0gaGFzTW9yZVxuICAgICAgPyAoTUFYX1ZJU0lCTEVfRVZFTlRTICogUk9XX0hFSUdIVCkgKyAyMCBcbiAgICAgIDogKG1heFJvdyArIDEpICogUk9XX0hFSUdIVDtcblxuICAgIHJldHVybiAoXG4gICAgICA8PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi0yXCI+XG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgIFwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHJvdW5kZWQtZnVsbCBmb250LXNlbWlib2xkIHRleHQteHMgdy02IGgtNlwiLFxuICAgICAgICAgICAgaXNDdXJyZW50RGF5ID8gXCJiZy1ibGFjayB0ZXh0LXdoaXRlXCIgOiBpc0N1cnJlbnRNb250aCA/IFwidGV4dC1ibGFjayBob3ZlcjpiZy1uZXV0cmFsLTEwMFwiIDogXCJ0ZXh0LW5ldXRyYWwtNDAwXCJcbiAgICAgICAgICApfT5cbiAgICAgICAgICAgIHtmb3JtYXQoZGF5LCAnZCcpfVxuICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICB7Y2FuRWRpdERhdGEgJiYgaXNDdXJyZW50TW9udGggJiYgKFxuICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyb3VuZGVkLWZ1bGwgaC02IHctNiBwLTEgb3BhY2l0eS0wIGdyb3VwLWhvdmVyOm9wYWNpdHktMTAwIHRyYW5zaXRpb24tb3BhY2l0eSBob3ZlcjpiZy1uZXV0cmFsLTEwMCBoaWRkZW4gbGc6aW5saW5lLWZsZXhcIlxuICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKCk7XG4gICAgICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiBvcGVuQWRkRXZlbnRGb3JtKGRheSksIDE1MCk7XG4gICAgICAgICAgICAgIH19XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxQbHVzSWNvbiBjbGFzc05hbWU9XCJoLTMgdy0zIHRleHQtYmxhY2tcIiAvPlxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiIHN0eWxlPXt7IGhlaWdodDogYCR7Y29udGFpbmVySGVpZ2h0fXB4YCB9fT5cbiAgICAgICAgICB7dmlzaWJsZUV2ZW50cy5tYXAocGUgPT4gKFxuICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICBrZXk9e3BlLmV2ZW50LmlkfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZVwiXG4gICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgdG9wOiBgJHtwZS5yb3cgKiBST1dfSEVJR0hUfXB4YCxcbiAgICAgICAgICAgICAgICBsZWZ0OiAnMnB4JyxcbiAgICAgICAgICAgICAgICB3aWR0aDogcGUuY29sU3BhbiA+IDEgXG4gICAgICAgICAgICAgICAgICA/IGBjYWxjKCR7cGUuY29sU3BhbiAqIDEwMH0lICsgJHsocGUuY29sU3BhbiAtIDEpICogMTl9cHgpYFxuICAgICAgICAgICAgICAgICAgOiAnY2FsYygxMDAlIC0gNHB4KScsXG4gICAgICAgICAgICAgICAgLy8gekluZGV4OiBwZS5jb2xTcGFuID4gMSA/IDEwIDogMSxcbiAgICAgICAgICAgICAgICB6SW5kZXg6IDEwICsgcGUucm93LFxuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8Q2FsZW5kYXJFdmVudEl0ZW1cbiAgICAgICAgICAgICAgICBldmVudD17cGUuZXZlbnR9XG4gICAgICAgICAgICAgICAgdmlldz1cIm1vbnRoXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKTtcbiAgICAgICAgICAgICAgICAgIHNldFNlbGVjdGVkRXZlbnQocGUuZXZlbnQuaWQpO1xuICAgICAgICAgICAgICAgICAgaGFuZGxlRXZlbnRDbGljayhwZS5ldmVudCk7XG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICBpc0RyYWdnaW5nPXthY3RpdmVEcmFnRGF0YT8ucGF5bG9hZD8uaWQgPT09IHBlLmV2ZW50LmlkfVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKSl9XG5cbiAgICAgICAgICB7aGFzTW9yZSAmJiAoXG4gICAgICAgICAgICA8ZGl2IFxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWJsYWNrIGhvdmVyOnRleHQtYmxhY2sgZm9udC1tZWRpdW0gdGV4dC14cyBjdXJzb3ItcG9pbnRlclwiXG4gICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgcG9zaXRpb246ICdhYnNvbHV0ZScsXG4gICAgICAgICAgICAgICAgdG9wOiBgJHtNQVhfVklTSUJMRV9FVkVOVFMgKiBST1dfSEVJR0hUfXB4YCxcbiAgICAgICAgICAgICAgICBsZWZ0OiAnMnB4JyxcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgb25DbGljaz17KGUpID0+IHtcbiAgICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xuICAgICAgICAgICAgICAgIHNldFNlbGVjdGVkRGF0ZShkYXkpO1xuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICArIHtzb3J0ZWRFdmVudHMubGVuZ3RoIC0gTUFYX1ZJU0lCTEVfRVZFTlRTfSBtb3JlXG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvPlxuICAgICk7XG4gIH07XG5cbiAgLy8gUmVuZGVyIG1haW4gdmlld1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiaC1mdWxsIGJnLWJhY2tncm91bmQgZmxleCBmbGV4LWNvbCBsZzpmbGV4LXJvd1wiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgZmxleCBmbGV4LWNvbCBtaW4taC0wXCI+XG4gICAgICAgIHsvKiBEYXkgSGVhZGVycyAqL31cbiAgICAgICAgPGRpdiBcbiAgICAgICAgICBkYXRhLWRheS1oZWFkZXJzPVwidHJ1ZVwiXG4gICAgICAgICAgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtNyBib3JkZXItYiBib3JkZXItbmV1dHJhbC0zMDAgc3RpY2t5IHRvcC0wIHotMTAgYmctd2hpdGVcIlxuICAgICAgICA+XG4gICAgICAgICAge1snU3VuZGF5JywgJ01vbmRheScsICdUdWVzZGF5JywgJ1dlZG5lc2RheScsICdUaHVyc2RheScsICdGcmlkYXknLCAnU2F0dXJkYXknXS5tYXAoZGF5TmFtZSA9PiAoXG4gICAgICAgICAgICA8ZGl2IGtleT17ZGF5TmFtZX0gY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgXCJ0ZXh0LWNlbnRlciBmb250LXNlbWlib2xkIHRleHQtYmxhY2tcIixcbiAgICAgICAgICAgICAgXCJweS0yIHRleHQteHNcIlxuICAgICAgICAgICAgKX0+XG4gICAgICAgICAgICAgIHtkYXlOYW1lLnN1YnN0cmluZygwLCAzKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICkpfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogTW9udGggR3JpZCAqL31cbiAgICAgICAgPFNjcm9sbEFyZWEgY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy03IGJvcmRlci1uZXV0cmFsLTMwMCBib3JkZXItYlwiPlxuICAgICAgICAgICAge21vbnRoQ2FsY3VsYXRpb25zLndlZWtzLm1hcCgod2Vlaywgd2Vla0luZGV4KSA9PlxuICAgICAgICAgICAgICB3ZWVrLm1hcCgoZGF5LCBkYXlJbmRleCkgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IHdlZWtFdmVudHMgPSB3ZWVrbHlTcGFubmluZ0V2ZW50cy5nZXQod2Vla0luZGV4KSB8fCBbXTtcbiAgICAgICAgICAgICAgICBjb25zdCBkYXlFdmVudHMgPSB3ZWVrRXZlbnRzLmZpbHRlcihwZSA9PiBwZS5zZWdtZW50Lm9yaWdpbmFsRXZlbnQuc3RhcnREYXlJbmRleCA9PT0gZGF5SW5kZXgpO1xuICAgICAgICAgICAgICAgIGNvbnN0IGlzQ3VycmVudE1vbnRoID0gZGF5LmdldE1vbnRoKCkgPT09IHNlbGVjdGVkRGF0ZS5nZXRNb250aCgpO1xuXG4gICAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICAgIDxEYXlDZWxsXG4gICAgICAgICAgICAgICAgICAgIGtleT17YCR7d2Vla0luZGV4fS0ke2RheUluZGV4fWB9XG4gICAgICAgICAgICAgICAgICAgIGRhdGU9e2RheX1cbiAgICAgICAgICAgICAgICAgICAgaXNDdXJyZW50TW9udGg9e2lzQ3VycmVudE1vbnRofVxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTZWxlY3RlZERhdGUoZGF5KX1cbiAgICAgICAgICAgICAgICAgICAgZXZlbnRzPXttb250aEV2ZW50c31cbiAgICAgICAgICAgICAgICAgICAgc3Bhbm5pbmdFdmVudHM9e3dlZWtFdmVudHN9XG4gICAgICAgICAgICAgICAgICAgIG9uRXZlbnRDbGljaz17aGFuZGxlRXZlbnRDbGlja31cbiAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRFdmVudD17c2VsZWN0ZWRFdmVudH1cbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgICAgfSksXG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L1Njcm9sbEFyZWE+XG4gICAgICA8L2Rpdj5cblxuICAgICAgPENhbGVuZGFyU2lkZUNhcmRcbiAgICAgICAgc2VsZWN0ZWREYXRlPXtzZWxlY3RlZERhdGV9XG4gICAgICAgIGV2ZW50cz17ZXZlbnRzfVxuICAgICAgICBzZWxlY3RlZEV2ZW50PXtzZWxlY3RlZEV2ZW50fVxuICAgICAgICBzZXRTZWxlY3RlZEV2ZW50PXtzZXRTZWxlY3RlZEV2ZW50fVxuICAgICAgICBoYW5kbGVFdmVudENsaWNrPXtoYW5kbGVFdmVudENsaWNrfVxuICAgICAgLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG5cbmZ1bmN0aW9uIGlzTXVsdGlEYXkoZXZlbnQ6IENhbGVuZGFyRXZlbnQpOiBib29sZWFuIHtcbiAgcmV0dXJuICFpc1NhbWVEYXkobmV3IERhdGUoZXZlbnQuc3RhcnQpLCBuZXcgRGF0ZShldmVudC5lbmQpKTtcbn0iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VNZW1vIiwiZm9ybWF0Iiwic3RhcnRPZk1vbnRoIiwiZW5kT2ZNb250aCIsInN0YXJ0T2ZXZWVrIiwiZW5kT2ZXZWVrIiwiYWRkRGF5cyIsImlzU2FtZURheSIsImlzVG9kYXkiLCJzdGFydE9mRGF5IiwiZW5kT2ZEYXkiLCJjbiIsIkJ1dHRvbiIsIlBsdXNJY29uIiwiU2Nyb2xsQXJlYSIsInVzZU1heWJlUmVjb3JkIiwiQ2FsZW5kYXJFdmVudEl0ZW0iLCJOb0V2ZW50cyIsIkNhbGVuZGFyU2lkZUNhcmQiLCJ1c2VEcm9wcGFibGUiLCJnZXRFdmVudHNGb3JDZWxsIiwiZGF0ZSIsImV2ZW50cyIsInNwYW5uaW5nRXZlbnRzIiwiY2VsbEV2ZW50cyIsImZvckVhY2giLCJzcGFubmluZ0V2ZW50IiwiZXZlbnRTdGFydCIsIkRhdGUiLCJzZWdtZW50Iiwib3JpZ2luYWxFdmVudCIsInN0YXJ0IiwiZXZlbnRFbmQiLCJlbmQiLCJwdXNoIiwiZXZlbnQiLCJpc011bHRpRGF5Iiwicm93IiwiY29sU3BhbiIsInZpc2libGVFdmVudHMiLCJzbGljZSIsImhpZGRlbkNvdW50IiwiTWF0aCIsIm1heCIsImxlbmd0aCIsInVzZU1vbnRoRXZlbnRzIiwid2Vla3MiLCJwb3NpdGlvbmVkRXZlbnRzQnlXZWVrIiwiTWFwIiwid2VlayIsIndlZWtJbmRleCIsIndlZWtTdGFydCIsIndlZWtFbmQiLCJ3ZWVrRXZlbnRzIiwiZmlsdGVyIiwic3RhcnREYXlJbmRleCIsImZpbmRJbmRleCIsImRheSIsImVuZERheUluZGV4IiwiZXZlbnRTcGFuc1dlZWsiLCJwb3NpdGlvbmVkIiwicm93cyIsInNvcnRlZEV2ZW50cyIsInNvcnQiLCJhIiwiYiIsImFzc2lnbmVkIiwiaSIsInNvbWUiLCJlIiwic2V0IiwiaGFzT3ZlcmxhcCIsImFTdGFydCIsImFFbmQiLCJiU3RhcnQiLCJiRW5kIiwicG9zaXRpb25TcGFubmluZ0V2ZW50cyIsIkRheUNlbGwiLCJpc0N1cnJlbnRNb250aCIsIm9uQ2xpY2siLCJvbkV2ZW50Q2xpY2siLCJzZWxlY3RlZEV2ZW50Iiwic2V0Tm9kZVJlZiIsImlzT3ZlciIsImlkIiwiZGF0YSIsInR5cGUiLCJpc0N1cnJlbnREYXkiLCJNQVhfVklTSUJMRV9FVkVOVFMiLCJST1dfSEVJR0hUIiwibWF4Um93IiwicmVkdWNlIiwiY29udGFpbmVySGVpZ2h0IiwiZGl2IiwicmVmIiwiY2xhc3NOYW1lIiwic3BhbiIsInN0eWxlIiwiaGVpZ2h0IiwibWFwIiwiZXZlbnRJbmZvIiwidW5kZWZpbmVkIiwidG9wIiwibGVmdCIsIndpZHRoIiwiekluZGV4IiwidmlldyIsInN0b3BQcm9wYWdhdGlvbiIsImlzRHJhZ2dpbmciLCJwb3NpdGlvbiIsIk1vbnRoVmlldyIsInNlbGVjdGVkRGF0ZSIsInNldFNlbGVjdGVkRXZlbnQiLCJzZXRTZWxlY3RlZERhdGUiLCJvcGVuQWRkRXZlbnRGb3JtIiwiY2FuRWRpdERhdGEiLCJoYW5kbGVFdmVudENsaWNrIiwiYWN0aXZlRHJhZ0RhdGEiLCJtYXliZVJlY29yZCIsImlzSW5SZWNvcmRUYWIiLCJtb250aENhbGN1bGF0aW9ucyIsIm1vbnRoU3RhcnQiLCJtb250aEVuZCIsInN0YXJ0RGF5Iiwid2Vla1N0YXJ0c09uIiwiZW5kRGF5IiwiZGF5cyIsIm1vbnRoRXZlbnRzIiwid2Vla2x5U3Bhbm5pbmdFdmVudHMiLCJzcGFubmluZ0V2ZW50c0J5V2VlayIsImV4dGVuZGVkRXZlbnQiLCJyZW5kZXJFbXB0eVN0YXRlIiwiZGF5TmFtZSIsInN1YnN0cmluZyIsInRpdGxlIiwibWVzc2FnZSIsInNob3dDcmVhdGVCdXR0b24iLCJvbkNyZWF0ZSIsInJlbmRlckRheUNlbGxDb250ZW50IiwiZGF5RXZlbnRzIiwiZ2V0TW9udGgiLCJoYXNNb3JlIiwidmFyaWFudCIsInNldFRpbWVvdXQiLCJwZSIsInBheWxvYWQiLCJkYXRhLWRheS1oZWFkZXJzIiwiZGF5SW5kZXgiLCJnZXQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx\n"));

/***/ })

});