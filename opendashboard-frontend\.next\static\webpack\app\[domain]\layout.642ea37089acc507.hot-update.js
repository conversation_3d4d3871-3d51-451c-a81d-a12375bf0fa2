"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[domain]/layout",{

/***/ "(app-pages-browser)/./node_modules/date-fns/esm/_lib/cloneObject/index.js":
/*!*************************************************************!*\
  !*** ./node_modules/date-fns/esm/_lib/cloneObject/index.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ cloneObject; }\n/* harmony export */ });\n/* harmony import */ var _assign_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../assign/index.js */ \"(app-pages-browser)/./node_modules/date-fns/esm/_lib/assign/index.js\");\n\nfunction cloneObject(object) {\n    return (0,_assign_index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, object);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9kYXRlLWZucy9lc20vX2xpYi9jbG9uZU9iamVjdC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF3QztBQUN6QixTQUFTQyxZQUFZQyxNQUFNO0lBQ3hDLE9BQU9GLDREQUFNQSxDQUFDLENBQUMsR0FBR0U7QUFDcEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2RhdGUtZm5zL2VzbS9fbGliL2Nsb25lT2JqZWN0L2luZGV4LmpzPzM3ZGEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGFzc2lnbiBmcm9tIFwiLi4vYXNzaWduL2luZGV4LmpzXCI7XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBjbG9uZU9iamVjdChvYmplY3QpIHtcbiAgcmV0dXJuIGFzc2lnbih7fSwgb2JqZWN0KTtcbn0iXSwibmFtZXMiOlsiYXNzaWduIiwiY2xvbmVPYmplY3QiLCJvYmplY3QiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/esm/_lib/cloneObject/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/esm/compareAsc/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/date-fns/esm/compareAsc/index.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ compareAsc; }\n/* harmony export */ });\n/* harmony import */ var _toDate_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../toDate/index.js */ \"(app-pages-browser)/./node_modules/date-fns/esm/toDate/index.js\");\n/* harmony import */ var _lib_requiredArgs_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../_lib/requiredArgs/index.js */ \"(app-pages-browser)/./node_modules/date-fns/esm/_lib/requiredArgs/index.js\");\n\n\n/**\n * @name compareAsc\n * @category Common Helpers\n * @summary Compare the two dates and return -1, 0 or 1.\n *\n * @description\n * Compare the two dates and return 1 if the first date is after the second,\n * -1 if the first date is before the second or 0 if dates are equal.\n *\n * @param {Date|Number} dateLeft - the first date to compare\n * @param {Date|Number} dateRight - the second date to compare\n * @returns {Number} the result of the comparison\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Compare 11 February 1987 and 10 July 1989:\n * const result = compareAsc(new Date(1987, 1, 11), new Date(1989, 6, 10))\n * //=> -1\n *\n * @example\n * // Sort the array of dates:\n * const result = [\n *   new Date(1995, 6, 2),\n *   new Date(1987, 1, 11),\n *   new Date(1989, 6, 10)\n * ].sort(compareAsc)\n * //=> [\n * //   Wed Feb 11 1987 00:00:00,\n * //   Mon Jul 10 1989 00:00:00,\n * //   Sun Jul 02 1995 00:00:00\n * // ]\n */ function compareAsc(dirtyDateLeft, dirtyDateRight) {\n    (0,_lib_requiredArgs_index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(2, arguments);\n    var dateLeft = (0,_toDate_index_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(dirtyDateLeft);\n    var dateRight = (0,_toDate_index_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(dirtyDateRight);\n    var diff = dateLeft.getTime() - dateRight.getTime();\n    if (diff < 0) {\n        return -1;\n    } else if (diff > 0) {\n        return 1;\n    // Return 0 if diff is 0; return NaN if diff is NaN\n    } else {\n        return diff;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/esm/compareAsc/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/esm/differenceInMonths/index.js":
/*!***************************************************************!*\
  !*** ./node_modules/date-fns/esm/differenceInMonths/index.js ***!
  \***************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ differenceInMonths; }\n/* harmony export */ });\n/* harmony import */ var _toDate_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../toDate/index.js */ \"(app-pages-browser)/./node_modules/date-fns/esm/toDate/index.js\");\n/* harmony import */ var _differenceInCalendarMonths_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../differenceInCalendarMonths/index.js */ \"(app-pages-browser)/./node_modules/date-fns/esm/differenceInCalendarMonths/index.js\");\n/* harmony import */ var _compareAsc_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../compareAsc/index.js */ \"(app-pages-browser)/./node_modules/date-fns/esm/compareAsc/index.js\");\n/* harmony import */ var _lib_requiredArgs_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../_lib/requiredArgs/index.js */ \"(app-pages-browser)/./node_modules/date-fns/esm/_lib/requiredArgs/index.js\");\n/* harmony import */ var _isLastDayOfMonth_index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../isLastDayOfMonth/index.js */ \"(app-pages-browser)/./node_modules/date-fns/esm/isLastDayOfMonth/index.js\");\n\n\n\n\n\n/**\n * @name differenceInMonths\n * @category Month Helpers\n * @summary Get the number of full months between the given dates.\n *\n * @description\n * Get the number of full months between the given dates using trunc as a default rounding method.\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @returns {Number} the number of full months\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many full months are between 31 January 2014 and 1 September 2014?\n * const result = differenceInMonths(new Date(2014, 8, 1), new Date(2014, 0, 31))\n * //=> 7\n */ function differenceInMonths(dirtyDateLeft, dirtyDateRight) {\n    (0,_lib_requiredArgs_index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(2, arguments);\n    var dateLeft = (0,_toDate_index_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(dirtyDateLeft);\n    var dateRight = (0,_toDate_index_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(dirtyDateRight);\n    var sign = (0,_compareAsc_index_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(dateLeft, dateRight);\n    var difference = Math.abs((0,_differenceInCalendarMonths_index_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(dateLeft, dateRight));\n    var result;\n    // Check for the difference of less than month\n    if (difference < 1) {\n        result = 0;\n    } else {\n        if (dateLeft.getMonth() === 1 && dateLeft.getDate() > 27) {\n            // This will check if the date is end of Feb and assign a higher end of month date\n            // to compare it with Jan\n            dateLeft.setDate(30);\n        }\n        dateLeft.setMonth(dateLeft.getMonth() - sign * difference);\n        // Math.abs(diff in full months - diff in calendar months) === 1 if last calendar month is not full\n        // If so, result must be decreased by 1 in absolute value\n        var isLastMonthNotFull = (0,_compareAsc_index_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(dateLeft, dateRight) === -sign;\n        // Check for cases of one full calendar month\n        if ((0,_isLastDayOfMonth_index_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])((0,_toDate_index_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(dirtyDateLeft)) && difference === 1 && (0,_compareAsc_index_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(dirtyDateLeft, dateRight) === 1) {\n            isLastMonthNotFull = false;\n        }\n        result = sign * (difference - Number(isLastMonthNotFull));\n    }\n    // Prevent negative zero\n    return result === 0 ? 0 : result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/esm/differenceInMonths/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/esm/differenceInSeconds/index.js":
/*!****************************************************************!*\
  !*** ./node_modules/date-fns/esm/differenceInSeconds/index.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ differenceInSeconds; }\n/* harmony export */ });\n/* harmony import */ var _differenceInMilliseconds_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../differenceInMilliseconds/index.js */ \"(app-pages-browser)/./node_modules/date-fns/esm/differenceInMilliseconds/index.js\");\n/* harmony import */ var _lib_requiredArgs_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../_lib/requiredArgs/index.js */ \"(app-pages-browser)/./node_modules/date-fns/esm/_lib/requiredArgs/index.js\");\n/* harmony import */ var _lib_roundingMethods_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../_lib/roundingMethods/index.js */ \"(app-pages-browser)/./node_modules/date-fns/esm/_lib/roundingMethods/index.js\");\n\n\n\n/**\n * @name differenceInSeconds\n * @category Second Helpers\n * @summary Get the number of seconds between the given dates.\n *\n * @description\n * Get the number of seconds between the given dates.\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @param {Object} [options] - an object with options.\n * @param {String} [options.roundingMethod='trunc'] - a rounding method (`ceil`, `floor`, `round` or `trunc`)\n * @returns {Number} the number of seconds\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many seconds are between\n * // 2 July 2014 12:30:07.999 and 2 July 2014 12:30:20.000?\n * const result = differenceInSeconds(\n *   new Date(2014, 6, 2, 12, 30, 20, 0),\n *   new Date(2014, 6, 2, 12, 30, 7, 999)\n * )\n * //=> 12\n */ function differenceInSeconds(dateLeft, dateRight, options) {\n    (0,_lib_requiredArgs_index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(2, arguments);\n    var diff = (0,_differenceInMilliseconds_index_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(dateLeft, dateRight) / 1000;\n    return (0,_lib_roundingMethods_index_js__WEBPACK_IMPORTED_MODULE_2__.getRoundingMethod)(options === null || options === void 0 ? void 0 : options.roundingMethod)(diff);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/esm/differenceInSeconds/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/esm/formatDistanceToNow/index.js":
/*!****************************************************************!*\
  !*** ./node_modules/date-fns/esm/formatDistanceToNow/index.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ formatDistanceToNow; }\n/* harmony export */ });\n/* harmony import */ var _formatDistance_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../formatDistance/index.js */ \"(app-pages-browser)/./node_modules/date-fns/esm/formatDistance/index.js\");\n/* harmony import */ var _lib_requiredArgs_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../_lib/requiredArgs/index.js */ \"(app-pages-browser)/./node_modules/date-fns/esm/_lib/requiredArgs/index.js\");\n\n\n/**\n * @name formatDistanceToNow\n * @category Common Helpers\n * @summary Return the distance between the given date and now in words.\n * @pure false\n *\n * @description\n * Return the distance between the given date and now in words.\n *\n * | Distance to now                                                   | Result              |\n * |-------------------------------------------------------------------|---------------------|\n * | 0 ... 30 secs                                                     | less than a minute  |\n * | 30 secs ... 1 min 30 secs                                         | 1 minute            |\n * | 1 min 30 secs ... 44 mins 30 secs                                 | [2..44] minutes     |\n * | 44 mins ... 30 secs ... 89 mins 30 secs                           | about 1 hour        |\n * | 89 mins 30 secs ... 23 hrs 59 mins 30 secs                        | about [2..24] hours |\n * | 23 hrs 59 mins 30 secs ... 41 hrs 59 mins 30 secs                 | 1 day               |\n * | 41 hrs 59 mins 30 secs ... 29 days 23 hrs 59 mins 30 secs         | [2..30] days        |\n * | 29 days 23 hrs 59 mins 30 secs ... 44 days 23 hrs 59 mins 30 secs | about 1 month       |\n * | 44 days 23 hrs 59 mins 30 secs ... 59 days 23 hrs 59 mins 30 secs | about 2 months      |\n * | 59 days 23 hrs 59 mins 30 secs ... 1 yr                           | [2..12] months      |\n * | 1 yr ... 1 yr 3 months                                            | about 1 year        |\n * | 1 yr 3 months ... 1 yr 9 month s                                  | over 1 year         |\n * | 1 yr 9 months ... 2 yrs                                           | almost 2 years      |\n * | N yrs ... N yrs 3 months                                          | about N years       |\n * | N yrs 3 months ... N yrs 9 months                                 | over N years        |\n * | N yrs 9 months ... N+1 yrs                                        | almost N+1 years    |\n *\n * With `options.includeSeconds == true`:\n * | Distance to now     | Result               |\n * |---------------------|----------------------|\n * | 0 secs ... 5 secs   | less than 5 seconds  |\n * | 5 secs ... 10 secs  | less than 10 seconds |\n * | 10 secs ... 20 secs | less than 20 seconds |\n * | 20 secs ... 40 secs | half a minute        |\n * | 40 secs ... 60 secs | less than a minute   |\n * | 60 secs ... 90 secs | 1 minute             |\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `Date.now()` internally hence impure and can't be safely curried.\n *\n * @param {Date|Number} date - the given date\n * @param {Object} [options] - the object with options\n * @param {Boolean} [options.includeSeconds=false] - distances less than a minute are more detailed\n * @param {Boolean} [options.addSuffix=false] - result specifies if now is earlier or later than the passed date\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @returns {String} the distance in words\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `date` must not be Invalid Date\n * @throws {RangeError} `options.locale` must contain `formatDistance` property\n *\n * @example\n * // If today is 1 January 2015, what is the distance to 2 July 2014?\n * const result = formatDistanceToNow(\n *   new Date(2014, 6, 2)\n * )\n * //=> '6 months'\n *\n * @example\n * // If now is 1 January 2015 00:00:00,\n * // what is the distance to 1 January 2015 00:00:15, including seconds?\n * const result = formatDistanceToNow(\n *   new Date(2015, 0, 1, 0, 0, 15),\n *   {includeSeconds: true}\n * )\n * //=> 'less than 20 seconds'\n *\n * @example\n * // If today is 1 January 2015,\n * // what is the distance to 1 January 2016, with a suffix?\n * const result = formatDistanceToNow(\n *   new Date(2016, 0, 1),\n *   {addSuffix: true}\n * )\n * //=> 'in about 1 year'\n *\n * @example\n * // If today is 1 January 2015,\n * // what is the distance to 1 August 2016 in Esperanto?\n * const eoLocale = require('date-fns/locale/eo')\n * const result = formatDistanceToNow(\n *   new Date(2016, 7, 1),\n *   {locale: eoLocale}\n * )\n * //=> 'pli ol 1 jaro'\n */ function formatDistanceToNow(dirtyDate, options) {\n    (0,_lib_requiredArgs_index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(1, arguments);\n    return (0,_formatDistance_index_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(dirtyDate, Date.now(), options);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/esm/formatDistanceToNow/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/esm/formatDistance/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/date-fns/esm/formatDistance/index.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ formatDistance; }\n/* harmony export */ });\n/* harmony import */ var _lib_defaultOptions_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../_lib/defaultOptions/index.js */ \"(app-pages-browser)/./node_modules/date-fns/esm/_lib/defaultOptions/index.js\");\n/* harmony import */ var _compareAsc_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../compareAsc/index.js */ \"(app-pages-browser)/./node_modules/date-fns/esm/compareAsc/index.js\");\n/* harmony import */ var _differenceInMonths_index_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../differenceInMonths/index.js */ \"(app-pages-browser)/./node_modules/date-fns/esm/differenceInMonths/index.js\");\n/* harmony import */ var _differenceInSeconds_index_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../differenceInSeconds/index.js */ \"(app-pages-browser)/./node_modules/date-fns/esm/differenceInSeconds/index.js\");\n/* harmony import */ var _lib_defaultLocale_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../_lib/defaultLocale/index.js */ \"(app-pages-browser)/./node_modules/date-fns/esm/_lib/defaultLocale/index.js\");\n/* harmony import */ var _toDate_index_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../toDate/index.js */ \"(app-pages-browser)/./node_modules/date-fns/esm/toDate/index.js\");\n/* harmony import */ var _lib_cloneObject_index_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../_lib/cloneObject/index.js */ \"(app-pages-browser)/./node_modules/date-fns/esm/_lib/cloneObject/index.js\");\n/* harmony import */ var _lib_assign_index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../_lib/assign/index.js */ \"(app-pages-browser)/./node_modules/date-fns/esm/_lib/assign/index.js\");\n/* harmony import */ var _lib_getTimezoneOffsetInMilliseconds_index_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../_lib/getTimezoneOffsetInMilliseconds/index.js */ \"(app-pages-browser)/./node_modules/date-fns/esm/_lib/getTimezoneOffsetInMilliseconds/index.js\");\n/* harmony import */ var _lib_requiredArgs_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../_lib/requiredArgs/index.js */ \"(app-pages-browser)/./node_modules/date-fns/esm/_lib/requiredArgs/index.js\");\n\n\n\n\n\n\n\n\n\n\nvar MINUTES_IN_DAY = 1440;\nvar MINUTES_IN_ALMOST_TWO_DAYS = 2520;\nvar MINUTES_IN_MONTH = 43200;\nvar MINUTES_IN_TWO_MONTHS = 86400;\n/**\n * @name formatDistance\n * @category Common Helpers\n * @summary Return the distance between the given dates in words.\n *\n * @description\n * Return the distance between the given dates in words.\n *\n * | Distance between dates                                            | Result              |\n * |-------------------------------------------------------------------|---------------------|\n * | 0 ... 30 secs                                                     | less than a minute  |\n * | 30 secs ... 1 min 30 secs                                         | 1 minute            |\n * | 1 min 30 secs ... 44 mins 30 secs                                 | [2..44] minutes     |\n * | 44 mins ... 30 secs ... 89 mins 30 secs                           | about 1 hour        |\n * | 89 mins 30 secs ... 23 hrs 59 mins 30 secs                        | about [2..24] hours |\n * | 23 hrs 59 mins 30 secs ... 41 hrs 59 mins 30 secs                 | 1 day               |\n * | 41 hrs 59 mins 30 secs ... 29 days 23 hrs 59 mins 30 secs         | [2..30] days        |\n * | 29 days 23 hrs 59 mins 30 secs ... 44 days 23 hrs 59 mins 30 secs | about 1 month       |\n * | 44 days 23 hrs 59 mins 30 secs ... 59 days 23 hrs 59 mins 30 secs | about 2 months      |\n * | 59 days 23 hrs 59 mins 30 secs ... 1 yr                           | [2..12] months      |\n * | 1 yr ... 1 yr 3 months                                            | about 1 year        |\n * | 1 yr 3 months ... 1 yr 9 month s                                  | over 1 year         |\n * | 1 yr 9 months ... 2 yrs                                           | almost 2 years      |\n * | N yrs ... N yrs 3 months                                          | about N years       |\n * | N yrs 3 months ... N yrs 9 months                                 | over N years        |\n * | N yrs 9 months ... N+1 yrs                                        | almost N+1 years    |\n *\n * With `options.includeSeconds == true`:\n * | Distance between dates | Result               |\n * |------------------------|----------------------|\n * | 0 secs ... 5 secs      | less than 5 seconds  |\n * | 5 secs ... 10 secs     | less than 10 seconds |\n * | 10 secs ... 20 secs    | less than 20 seconds |\n * | 20 secs ... 40 secs    | half a minute        |\n * | 40 secs ... 60 secs    | less than a minute   |\n * | 60 secs ... 90 secs    | 1 minute             |\n *\n * @param {Date|Number} date - the date\n * @param {Date|Number} baseDate - the date to compare with\n * @param {Object} [options] - an object with options.\n * @param {Boolean} [options.includeSeconds=false] - distances less than a minute are more detailed\n * @param {Boolean} [options.addSuffix=false] - result indicates if the second date is earlier or later than the first\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @returns {String} the distance in words\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} `date` must not be Invalid Date\n * @throws {RangeError} `baseDate` must not be Invalid Date\n * @throws {RangeError} `options.locale` must contain `formatDistance` property\n *\n * @example\n * // What is the distance between 2 July 2014 and 1 January 2015?\n * const result = formatDistance(new Date(2014, 6, 2), new Date(2015, 0, 1))\n * //=> '6 months'\n *\n * @example\n * // What is the distance between 1 January 2015 00:00:15\n * // and 1 January 2015 00:00:00, including seconds?\n * const result = formatDistance(\n *   new Date(2015, 0, 1, 0, 0, 15),\n *   new Date(2015, 0, 1, 0, 0, 0),\n *   { includeSeconds: true }\n * )\n * //=> 'less than 20 seconds'\n *\n * @example\n * // What is the distance from 1 January 2016\n * // to 1 January 2015, with a suffix?\n * const result = formatDistance(new Date(2015, 0, 1), new Date(2016, 0, 1), {\n *   addSuffix: true\n * })\n * //=> 'about 1 year ago'\n *\n * @example\n * // What is the distance between 1 August 2016 and 1 January 2015 in Esperanto?\n * import { eoLocale } from 'date-fns/locale/eo'\n * const result = formatDistance(new Date(2016, 7, 1), new Date(2015, 0, 1), {\n *   locale: eoLocale\n * })\n * //=> 'pli ol 1 jaro'\n */ function formatDistance(dirtyDate, dirtyBaseDate, options) {\n    var _ref, _options$locale;\n    (0,_lib_requiredArgs_index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(2, arguments);\n    var defaultOptions = (0,_lib_defaultOptions_index_js__WEBPACK_IMPORTED_MODULE_1__.getDefaultOptions)();\n    var locale = (_ref = (_options$locale = options === null || options === void 0 ? void 0 : options.locale) !== null && _options$locale !== void 0 ? _options$locale : defaultOptions.locale) !== null && _ref !== void 0 ? _ref : _lib_defaultLocale_index_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"];\n    if (!locale.formatDistance) {\n        throw new RangeError(\"locale must contain formatDistance property\");\n    }\n    var comparison = (0,_compareAsc_index_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(dirtyDate, dirtyBaseDate);\n    if (isNaN(comparison)) {\n        throw new RangeError(\"Invalid time value\");\n    }\n    var localizeOptions = (0,_lib_assign_index_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])((0,_lib_cloneObject_index_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(options), {\n        addSuffix: Boolean(options === null || options === void 0 ? void 0 : options.addSuffix),\n        comparison: comparison\n    });\n    var dateLeft;\n    var dateRight;\n    if (comparison > 0) {\n        dateLeft = (0,_toDate_index_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(dirtyBaseDate);\n        dateRight = (0,_toDate_index_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(dirtyDate);\n    } else {\n        dateLeft = (0,_toDate_index_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(dirtyDate);\n        dateRight = (0,_toDate_index_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(dirtyBaseDate);\n    }\n    var seconds = (0,_differenceInSeconds_index_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(dateRight, dateLeft);\n    var offsetInSeconds = ((0,_lib_getTimezoneOffsetInMilliseconds_index_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(dateRight) - (0,_lib_getTimezoneOffsetInMilliseconds_index_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(dateLeft)) / 1000;\n    var minutes = Math.round((seconds - offsetInSeconds) / 60);\n    var months;\n    // 0 up to 2 mins\n    if (minutes < 2) {\n        if (options !== null && options !== void 0 && options.includeSeconds) {\n            if (seconds < 5) {\n                return locale.formatDistance(\"lessThanXSeconds\", 5, localizeOptions);\n            } else if (seconds < 10) {\n                return locale.formatDistance(\"lessThanXSeconds\", 10, localizeOptions);\n            } else if (seconds < 20) {\n                return locale.formatDistance(\"lessThanXSeconds\", 20, localizeOptions);\n            } else if (seconds < 40) {\n                return locale.formatDistance(\"halfAMinute\", 0, localizeOptions);\n            } else if (seconds < 60) {\n                return locale.formatDistance(\"lessThanXMinutes\", 1, localizeOptions);\n            } else {\n                return locale.formatDistance(\"xMinutes\", 1, localizeOptions);\n            }\n        } else {\n            if (minutes === 0) {\n                return locale.formatDistance(\"lessThanXMinutes\", 1, localizeOptions);\n            } else {\n                return locale.formatDistance(\"xMinutes\", minutes, localizeOptions);\n            }\n        }\n    // 2 mins up to 0.75 hrs\n    } else if (minutes < 45) {\n        return locale.formatDistance(\"xMinutes\", minutes, localizeOptions);\n    // 0.75 hrs up to 1.5 hrs\n    } else if (minutes < 90) {\n        return locale.formatDistance(\"aboutXHours\", 1, localizeOptions);\n    // 1.5 hrs up to 24 hrs\n    } else if (minutes < MINUTES_IN_DAY) {\n        var hours = Math.round(minutes / 60);\n        return locale.formatDistance(\"aboutXHours\", hours, localizeOptions);\n    // 1 day up to 1.75 days\n    } else if (minutes < MINUTES_IN_ALMOST_TWO_DAYS) {\n        return locale.formatDistance(\"xDays\", 1, localizeOptions);\n    // 1.75 days up to 30 days\n    } else if (minutes < MINUTES_IN_MONTH) {\n        var days = Math.round(minutes / MINUTES_IN_DAY);\n        return locale.formatDistance(\"xDays\", days, localizeOptions);\n    // 1 month up to 2 months\n    } else if (minutes < MINUTES_IN_TWO_MONTHS) {\n        months = Math.round(minutes / MINUTES_IN_MONTH);\n        return locale.formatDistance(\"aboutXMonths\", months, localizeOptions);\n    }\n    months = (0,_differenceInMonths_index_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(dateRight, dateLeft);\n    // 2 months up to 12 months\n    if (months < 12) {\n        var nearestMonth = Math.round(minutes / MINUTES_IN_MONTH);\n        return locale.formatDistance(\"xMonths\", nearestMonth, localizeOptions);\n    // 1 year up to max Date\n    } else {\n        var monthsSinceStartOfYear = months % 12;\n        var years = Math.floor(months / 12);\n        // N years up to 1 years 3 months\n        if (monthsSinceStartOfYear < 3) {\n            return locale.formatDistance(\"aboutXYears\", years, localizeOptions);\n        // N years 3 months up to N years 9 months\n        } else if (monthsSinceStartOfYear < 9) {\n            return locale.formatDistance(\"overXYears\", years, localizeOptions);\n        // N years 9 months up to N year 12 months\n        } else {\n            return locale.formatDistance(\"almostXYears\", years + 1, localizeOptions);\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/esm/formatDistance/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/esm/isLastDayOfMonth/index.js":
/*!*************************************************************!*\
  !*** ./node_modules/date-fns/esm/isLastDayOfMonth/index.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ isLastDayOfMonth; }\n/* harmony export */ });\n/* harmony import */ var _toDate_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../toDate/index.js */ \"(app-pages-browser)/./node_modules/date-fns/esm/toDate/index.js\");\n/* harmony import */ var _endOfDay_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../endOfDay/index.js */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfDay/index.js\");\n/* harmony import */ var _endOfMonth_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../endOfMonth/index.js */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfMonth/index.js\");\n/* harmony import */ var _lib_requiredArgs_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../_lib/requiredArgs/index.js */ \"(app-pages-browser)/./node_modules/date-fns/esm/_lib/requiredArgs/index.js\");\n\n\n\n\n/**\n * @name isLastDayOfMonth\n * @category Month Helpers\n * @summary Is the given date the last day of a month?\n *\n * @description\n * Is the given date the last day of a month?\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is the last day of a month\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Is 28 February 2014 the last day of a month?\n * const result = isLastDayOfMonth(new Date(2014, 1, 28))\n * //=> true\n */ function isLastDayOfMonth(dirtyDate) {\n    (0,_lib_requiredArgs_index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(1, arguments);\n    var date = (0,_toDate_index_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(dirtyDate);\n    return (0,_endOfDay_index_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(date).getTime() === (0,_endOfMonth_index_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(date).getTime();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9kYXRlLWZucy9lc20vaXNMYXN0RGF5T2ZNb250aC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUF3QztBQUNJO0FBQ0k7QUFDUztBQUN6RDs7Ozs7Ozs7Ozs7Ozs7OztDQWdCQyxHQUNjLFNBQVNJLGlCQUFpQkMsU0FBUztJQUNoREYsc0VBQVlBLENBQUMsR0FBR0c7SUFDaEIsSUFBSUMsT0FBT1AsNERBQU1BLENBQUNLO0lBQ2xCLE9BQU9KLDhEQUFRQSxDQUFDTSxNQUFNQyxPQUFPLE9BQU9OLGdFQUFVQSxDQUFDSyxNQUFNQyxPQUFPO0FBQzlEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9kYXRlLWZucy9lc20vaXNMYXN0RGF5T2ZNb250aC9pbmRleC5qcz84ZWJmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0b0RhdGUgZnJvbSBcIi4uL3RvRGF0ZS9pbmRleC5qc1wiO1xuaW1wb3J0IGVuZE9mRGF5IGZyb20gXCIuLi9lbmRPZkRheS9pbmRleC5qc1wiO1xuaW1wb3J0IGVuZE9mTW9udGggZnJvbSBcIi4uL2VuZE9mTW9udGgvaW5kZXguanNcIjtcbmltcG9ydCByZXF1aXJlZEFyZ3MgZnJvbSBcIi4uL19saWIvcmVxdWlyZWRBcmdzL2luZGV4LmpzXCI7XG4vKipcbiAqIEBuYW1lIGlzTGFzdERheU9mTW9udGhcbiAqIEBjYXRlZ29yeSBNb250aCBIZWxwZXJzXG4gKiBAc3VtbWFyeSBJcyB0aGUgZ2l2ZW4gZGF0ZSB0aGUgbGFzdCBkYXkgb2YgYSBtb250aD9cbiAqXG4gKiBAZGVzY3JpcHRpb25cbiAqIElzIHRoZSBnaXZlbiBkYXRlIHRoZSBsYXN0IGRheSBvZiBhIG1vbnRoP1xuICpcbiAqIEBwYXJhbSB7RGF0ZXxOdW1iZXJ9IGRhdGUgLSB0aGUgZGF0ZSB0byBjaGVja1xuICogQHJldHVybnMge0Jvb2xlYW59IHRoZSBkYXRlIGlzIHRoZSBsYXN0IGRheSBvZiBhIG1vbnRoXG4gKiBAdGhyb3dzIHtUeXBlRXJyb3J9IDEgYXJndW1lbnQgcmVxdWlyZWRcbiAqXG4gKiBAZXhhbXBsZVxuICogLy8gSXMgMjggRmVicnVhcnkgMjAxNCB0aGUgbGFzdCBkYXkgb2YgYSBtb250aD9cbiAqIGNvbnN0IHJlc3VsdCA9IGlzTGFzdERheU9mTW9udGgobmV3IERhdGUoMjAxNCwgMSwgMjgpKVxuICogLy89PiB0cnVlXG4gKi9cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGlzTGFzdERheU9mTW9udGgoZGlydHlEYXRlKSB7XG4gIHJlcXVpcmVkQXJncygxLCBhcmd1bWVudHMpO1xuICB2YXIgZGF0ZSA9IHRvRGF0ZShkaXJ0eURhdGUpO1xuICByZXR1cm4gZW5kT2ZEYXkoZGF0ZSkuZ2V0VGltZSgpID09PSBlbmRPZk1vbnRoKGRhdGUpLmdldFRpbWUoKTtcbn0iXSwibmFtZXMiOlsidG9EYXRlIiwiZW5kT2ZEYXkiLCJlbmRPZk1vbnRoIiwicmVxdWlyZWRBcmdzIiwiaXNMYXN0RGF5T2ZNb250aCIsImRpcnR5RGF0ZSIsImFyZ3VtZW50cyIsImRhdGUiLCJnZXRUaW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/esm/isLastDayOfMonth/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/workspace/main/common/search.tsx":
/*!*********************************************************!*\
  !*** ./src/components/workspace/main/common/search.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/icons/FontAwesomeRegular */ \"(app-pages-browser)/./src/components/icons/FontAwesomeRegular.tsx\");\n/* harmony import */ var _components_workspace_main_common_searchmodal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/workspace/main/common/searchmodal */ \"(app-pages-browser)/./src/components/workspace/main/common/searchmodal.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst SearchModal = ()=>{\n    _s();\n    const [isSearchModalOpen, setSearchModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                variant: \"ghost\",\n                className: \"h-auto text-sm p-2 py-1.5 rounded-none justify-start\",\n                onClick: ()=>setSearchModalOpen(true),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_2__.MagnifyingGlassIcon, {\n                        className: \"mr-2 size-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\search.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 8\n                    }, undefined),\n                    \"Search\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\search.tsx\",\n                lineNumber: 10,\n                columnNumber: 6\n            }, undefined),\n            isSearchModalOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_common_searchmodal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                onClose: ()=>setSearchModalOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\search.tsx\",\n                lineNumber: 17,\n                columnNumber: 8\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(SearchModal, \"2LkPy3Uf3LqafUw24TYS9GgNPJU=\");\n_c = SearchModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SearchModal);\nvar _c;\n$RefreshReg$(_c, \"SearchModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/common/search.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/workspace/main/common/searchmodal.tsx":
/*!**************************************************************!*\
  !*** ./src/components/workspace/main/common/searchmodal.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SearchModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var _providers_user__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/user */ \"(app-pages-browser)/./src/providers/user.tsx\");\n/* harmony import */ var _api_workspace__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/api/workspace */ \"(app-pages-browser)/./src/api/workspace.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/formatDistanceToNow/index.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! lodash/debounce */ \"(app-pages-browser)/./node_modules/lodash/debounce.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/icons/FontAwesomeRegular */ \"(app-pages-browser)/./src/components/icons/FontAwesomeRegular.tsx\");\n/* harmony import */ var _components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/custom-ui/loader */ \"(app-pages-browser)/./src/components/custom-ui/loader.tsx\");\n/* harmony import */ var _views_viewIcon__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../views/viewIcon */ \"(app-pages-browser)/./src/components/workspace/main/views/viewIcon.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! opendb-app-db-utils/lib/typings/view */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/typings/view.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst HighlightedContent = (param)=>{\n    let { content, highlight } = param;\n    if (!content) return null;\n    if (!highlight) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: content\n    }, void 0, false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            content.substring(0, highlight.start),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mark\", {\n                className: \"bg-yellow-200\",\n                children: content.substring(highlight.start, highlight.end)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, undefined),\n            content.substring(highlight.end)\n        ]\n    }, void 0, true);\n};\n_c = HighlightedContent;\nconst groupResults = (results)=>{\n    const grouped = {\n        databases: [],\n        pages: [],\n        views: [],\n        documents: [],\n        members: []\n    };\n    results.forEach((result)=>{\n        // If it has an image or no source, it's a member\n        if (result.image || !result.source) {\n            grouped.members.push(result);\n        } else if (result.source) {\n            if (result.source.databaseId && !result.source.viewId) {\n                grouped.databases.push(result);\n            } else if (result.source.viewId) {\n                grouped.views.push(result);\n            } else if (result.source.documentId) {\n                grouped.documents.push(result);\n            } else if (result.source.pageId) {\n                grouped.pages.push(result);\n            }\n        }\n    });\n    return grouped;\n};\nfunction SearchModal(param) {\n    let { onClose, debounceTimeoutMS = 250 } = param;\n    var _workspace_workspace, _workspace_workspace1;\n    _s();\n    const [query, setQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [recentSearches, setRecentSearches] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const { workspace, url } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_4__.useWorkspace)();\n    const { token } = (0,_providers_user__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const workspaceId = workspace === null || workspace === void 0 ? void 0 : (_workspace_workspace = workspace.workspace) === null || _workspace_workspace === void 0 ? void 0 : _workspace_workspace.id;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedSearches = localStorage.getItem(\"recentSearches\");\n        if (savedSearches) {\n            setRecentSearches(JSON.parse(savedSearches));\n        }\n    }, []);\n    const getErrorMessage = (error)=>{\n        if (error instanceof Error) {\n            if (\"response\" in error) {\n                var _apiError_response_data, _apiError_response;\n                const apiError = error;\n                return ((_apiError_response = apiError.response) === null || _apiError_response === void 0 ? void 0 : (_apiError_response_data = _apiError_response.data) === null || _apiError_response_data === void 0 ? void 0 : _apiError_response_data.message) || apiError.message;\n            }\n            return error.message;\n        }\n        return \"An unexpected error occurred\";\n    };\n    const debouncedSearch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(lodash_debounce__WEBPACK_IMPORTED_MODULE_8___default()(async (searchQuery)=>{\n        if (searchQuery.trim() && workspaceId && token) {\n            setIsLoading(true);\n            setError(null);\n            try {\n                const response = await (0,_api_workspace__WEBPACK_IMPORTED_MODULE_6__.searchWorkspaces)(token.token, workspaceId, searchQuery);\n                if (!response.isSuccess) {\n                    throw new Error(response.error || \"Search failed\");\n                }\n                setResults(response.data.data.results.results || []);\n            } catch (error) {\n                console.error(\"Search error:\", error);\n                setResults([]);\n                const errorMessage = getErrorMessage(error);\n                setError(errorMessage);\n            } finally{\n                setIsLoading(false);\n            }\n        } else {\n            setResults([]);\n            setError(null);\n            setIsLoading(false);\n        }\n    }, debounceTimeoutMS), [\n        workspaceId,\n        token,\n        debounceTimeoutMS\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (query.trim()) {\n            setIsLoading(true);\n        }\n        debouncedSearch(query);\n        return ()=>debouncedSearch.cancel();\n    }, [\n        query,\n        debouncedSearch\n    ]);\n    const handleResultClick = (result)=>{\n        const path = url(result.path);\n        router.push(path);\n        onClose();\n        saveRecentSearch(query);\n    };\n    const saveRecentSearch = (search)=>{\n        if (search.trim()) {\n            const updatedSearches = [\n                search,\n                ...recentSearches.filter((s)=>s !== search)\n            ].slice(0, 5);\n            setRecentSearches(updatedSearches);\n            localStorage.setItem(\"recentSearches\", JSON.stringify(updatedSearches));\n        }\n    };\n    const deleteRecentSearch = (search, e)=>{\n        e.stopPropagation();\n        const updatedSearches = recentSearches.filter((s)=>s !== search);\n        setRecentSearches(updatedSearches);\n        localStorage.setItem(\"recentSearches\", JSON.stringify(updatedSearches));\n    };\n    const getIconForSource = (result)=>{\n        var _result_source;\n        if (result.image) {\n            return null;\n        }\n        if (((_result_source = result.source) === null || _result_source === void 0 ? void 0 : _result_source.viewId) && result.viewType) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_views_viewIcon__WEBPACK_IMPORTED_MODULE_11__.ViewIcon, {\n                type: result.viewType,\n                className: \"h-4 w-4 text-muted-foreground\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                lineNumber: 188,\n                columnNumber: 14\n            }, this);\n        }\n        if (result.source) {\n            if (result.source.databaseId) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.TableIcon, {\n                    className: \"h-4 w-4 text-primary\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                    lineNumber: 193,\n                    columnNumber: 16\n                }, this);\n            }\n            if (result.source.documentId) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.BookIcon, {\n                    className: \"h-4 w-4 text-accent-foreground\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 16\n                }, this);\n            }\n            if (result.source.pageId) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.BookIcon, {\n                    className: \"h-4 w-4 text-secondary-foreground\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 16\n                }, this);\n            }\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.UserGroupIcon, {\n            className: \"h-4 w-4 text-muted-foreground\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n            lineNumber: 203,\n            columnNumber: 12\n        }, this);\n    };\n    const getResultType = (result)=>{\n        if (result.image) {\n            return \"Member\";\n        }\n        if (result.source) {\n            if (result.source.databaseId) {\n                if (result.viewType) {\n                    switch(result.viewType){\n                        case opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_12__.ViewType.Table:\n                            return \"Table View\";\n                        case opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_12__.ViewType.Board:\n                            return \"Board View\";\n                        case opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_12__.ViewType.Dashboard:\n                            return \"Dashboard\";\n                        case opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_12__.ViewType.Document:\n                            return \"Document View\";\n                        case opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_12__.ViewType.Form:\n                            return \"Form View\";\n                        case opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_12__.ViewType.SummaryTable:\n                            return \"Summary View\";\n                        default:\n                            return \"Database View\";\n                    }\n                }\n                return \"Database\";\n            }\n            if (result.source.documentId) {\n                return \"Document\";\n            }\n            if (result.source.pageId) {\n                return \"Page\";\n            }\n        }\n        return \"Member\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        open: true,\n        onOpenChange: onClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n            className: \"p-0 gap-0 max-w-xl rounded-lg shadow-xl border border-gray-200 overflow-hidden\",\n            hideCloseBtn: true,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center border-b px-3 relative bg-gray-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.MagnifyingGlassIcon, {\n                            className: \"mr-2 h-4 w-4 shrink-0 text-gray-500\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                            className: \"flex h-14 rounded-md border-0 bg-transparent py-3 text-sm outline-none placeholder:text-gray-500 focus-visible:ring-0 disabled:cursor-not-allowed disabled:opacity-50\",\n                            placeholder: \"Search in \".concat((workspace === null || workspace === void 0 ? void 0 : (_workspace_workspace1 = workspace.workspace) === null || _workspace_workspace1 === void 0 ? void 0 : _workspace_workspace1.name) || \"workspace\", \"...\"),\n                            value: query,\n                            onChange: (e)=>setQuery(e.target.value),\n                            autoFocus: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                    lineNumber: 249,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-h-[60vh] overflow-y-auto pr-1\",\n                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-8 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_10__.Loader, {\n                                className: \"inline-block w-6 h-6 text-gray-600 animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-3 text-sm text-gray-600\",\n                                children: \"Searching workspace...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 13\n                    }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-8 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-100 mb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.XmarkIcon, {\n                                    className: \"h-5 w-5 text-red-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-medium text-gray-900\",\n                                children: \"Search Error\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-red-500\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 flex justify-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setError(null);\n                                            debouncedSearch(query);\n                                        },\n                                        className: \"inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-primary-600 rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n                                        children: \"Try again\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onClose,\n                                        className: \"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 13\n                    }, this) : !query.trim() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2 px-2\",\n                                children: \"Recent Searches\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 15\n                            }, this),\n                            recentSearches.length > 0 ? recentSearches.map((search, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between px-2 py-2 cursor-pointer hover:bg-gray-50 rounded transition-colors\",\n                                    onClick: ()=>setQuery(search),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.TimerIcon, {\n                                                    className: \"h-4 w-4 text-gray-400 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-700\",\n                                                    children: search\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: (e)=>deleteRecentSearch(search, e),\n                                            className: \"p-1 rounded-full hover:bg-gray-100 transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.XmarkIcon, {\n                                                className: \"h-3 w-3 text-gray-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 19\n                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-2 py-3 text-sm text-gray-500\",\n                                children: \"No recent searches\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 13\n                    }, this) : results.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-8 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-gray-100 mb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.MagnifyingGlassIcon, {\n                                    className: \"h-5 w-5 text-gray-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-medium text-gray-900\",\n                                children: \"No results found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-gray-500\",\n                                children: [\n                                    \"We couldn't find anything matching \\\"\",\n                                    query,\n                                    '\"'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 13\n                    }, this) : Object.entries(groupResults(results)).map((param)=>{\n                        let [category, items] = param;\n                        return items.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xs font-semibold text-gray-500 uppercase tracking-wider px-3 py-2\",\n                                    children: category\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 19\n                                }, this),\n                                items.map((result)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-3 py-3 cursor-pointer hover:bg-gray-50 transition-colors flex items-start gap-3\",\n                                        onClick: ()=>handleResultClick(result),\n                                        children: [\n                                            result.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: result.image,\n                                                alt: result.name || \"\",\n                                                className: \"h-8 w-8 rounded-full object-cover mt-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 25\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0 mt-0.5 p-2 rounded-lg bg-gray-100 text-gray-600\",\n                                                children: getIconForSource(result)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"min-w-0 flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-baseline\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-sm font-medium text-gray-900 truncate\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HighlightedContent, {\n                                                                    content: result.title || result.name,\n                                                                    highlight: result.highlight\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                                    lineNumber: 354,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                                lineNumber: 353,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            result.publishedAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500 ml-2 whitespace-nowrap\",\n                                                                children: (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(new Date(result.publishedAt), {\n                                                                    addSuffix: true\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                                lineNumber: 360,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    result.content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-xs text-gray-500 line-clamp-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HighlightedContent, {\n                                                            content: result.content,\n                                                            highlight: result.highlight\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    result.source && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-1.5\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800\",\n                                                            children: getResultType(result)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, result.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 21\n                                    }, this))\n                            ]\n                        }, category, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 17\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                    lineNumber: 260,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n            lineNumber: 248,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n        lineNumber: 247,\n        columnNumber: 5\n    }, this);\n}\n_s(SearchModal, \"FOO2IJ50ajcpVGomuC5Zoi18Sb4=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_4__.useWorkspace,\n        _providers_user__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter\n    ];\n});\n_c1 = SearchModal;\nvar _c, _c1;\n$RefreshReg$(_c, \"HighlightedContent\");\n$RefreshReg$(_c1, \"SearchModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/common/searchmodal.tsx\n"));

/***/ })

});