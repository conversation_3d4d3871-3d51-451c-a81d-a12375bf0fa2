/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[domain]/layout",{

/***/ "(app-pages-browser)/./src/components/workspace/main/common/search.tsx":
/*!*********************************************************!*\
  !*** ./src/components/workspace/main/common/search.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/icons/FontAwesomeRegular */ \"(app-pages-browser)/./src/components/icons/FontAwesomeRegular.tsx\");\n/* harmony import */ var _components_workspace_main_common_searchmodal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/workspace/main/common/searchmodal */ \"(app-pages-browser)/./src/components/workspace/main/common/searchmodal.tsx\");\n/* harmony import */ var _components_workspace_main_common_searchmodal__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_components_workspace_main_common_searchmodal__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst SearchModal = ()=>{\n    _s();\n    const [isSearchModalOpen, setSearchModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                variant: \"ghost\",\n                className: \"h-auto text-sm p-2 py-1.5 rounded-none justify-start\",\n                onClick: ()=>setSearchModalOpen(true),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_2__.MagnifyingGlassIcon, {\n                        className: \"mr-2 size-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\search.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 8\n                    }, undefined),\n                    \"Search\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\search.tsx\",\n                lineNumber: 10,\n                columnNumber: 6\n            }, undefined),\n            isSearchModalOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_components_workspace_main_common_searchmodal__WEBPACK_IMPORTED_MODULE_3___default()), {\n                onClose: ()=>setSearchModalOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\search.tsx\",\n                lineNumber: 17,\n                columnNumber: 8\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(SearchModal, \"2LkPy3Uf3LqafUw24TYS9GgNPJU=\");\n_c = SearchModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SearchModal);\nvar _c;\n$RefreshReg$(_c, \"SearchModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/common/search.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/workspace/main/common/searchmodal.tsx":
/*!**************************************************************!*\
  !*** ./src/components/workspace/main/common/searchmodal.tsx ***!
  \**************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// \"use client\"\n// import { useState, useEffect, useCallback, useRef } from \"react\"\n// import { Dialog, DialogContent, DialogHeader } from \"@/components/ui/dialog\"\n// import { Input } from \"@/components/ui/input\"\n// import { useWorkspace } from \"@/providers/workspace\"\n// import { useAuth } from \"@/providers/user\"\n// import { searchWorkspaces } from \"@/api/workspace\"\n// import { SearchResult } from \"@/typings/workspace\"\n// import { useRouter } from \"next/navigation\"\n// import { formatDistanceToNow } from \"date-fns\"\n// import debounce from \"lodash/debounce\"\n// import {MagnifyingGlassIcon, TimerIcon, BookIcon, TableIcon, UserGroupIcon, XmarkIcon} from \"@/components/icons/FontAwesomeRegular\"\n// import { Loader } from \"@/components/custom-ui/loader\"\n// import { ViewIcon } from \"../views/viewIcon\"\n// import { ViewType } from \"opendb-app-db-utils/lib/typings/view\"\n// const SENTINEL_HEIGHT = 32;\n// interface SearchModalProps { onClose: () => void; debounceTimeoutMS?: number }\n// const HighlightedContent = ({ content, highlight, query }:\n//   { content?: string; highlight?: { start: number; end: number }; query?: string }) => {\n//   if (!content) return null;\n//   const normalizedContent = content.trim();\n//   if (query && query.trim()) {\n//     const searchTerm = query.trim().toLowerCase();\n//     const contentLower = normalizedContent.toLowerCase();\n//     const escapedSearchTerm = searchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n//     try {\n//       const regex = new RegExp(`(${escapedSearchTerm})`, 'gi');\n//       const parts = normalizedContent.split(regex);\n//       return (\n//         <>\n//           {parts.map((part, index) => {\n//             if (part.toLowerCase() === searchTerm) {\n//               return (\n//                 <mark key={index} className=\"bg-yellow-200 text-yellow-900 px-0.5 rounded-sm\">\n//                   {part}\n//                 </mark>\n//               );\n//             }\n//             return part;\n//           })}\n//         </>\n//       );\n//     } catch (error) {\n//       const parts = [];\n//       let lastIndex = 0;\n//       let currentIndex = contentLower.indexOf(searchTerm, lastIndex);\n//       if (currentIndex === -1) return <>{normalizedContent}</>;\n//       while (currentIndex !== -1) {\n//         parts.push(normalizedContent.substring(lastIndex, currentIndex));\n//         parts.push(\n//           <mark key={currentIndex} className=\"bg-yellow-200 text-yellow-900 px-0.5 rounded-sm\">\n//             {normalizedContent.substring(currentIndex, currentIndex + searchTerm.length)}\n//           </mark>\n//         );\n//         lastIndex = currentIndex + searchTerm.length;\n//         currentIndex = contentLower.indexOf(searchTerm, lastIndex);\n//       }\n//       if (lastIndex < normalizedContent.length) {\n//         parts.push(normalizedContent.substring(lastIndex));\n//       }\n//       return <>{parts}</>;\n//     }\n//   }\n//   if (highlight && highlight.start >= 0 && highlight.end > highlight.start) {\n//     const start = Math.max(0, highlight.start);\n//     const end = Math.min(normalizedContent.length, highlight.end);\n//     if (start < end && start < normalizedContent.length) {\n//       return (\n//         <>\n//           {normalizedContent.substring(0, start)}\n//           <mark className=\"bg-yellow-200 text-yellow-900 px-0.5 rounded-sm\">{normalizedContent.substring(start, end)}</mark>\n//           {normalizedContent.substring(end)}\n//         </>\n//       );\n//     }\n//   }\n//   return <>{normalizedContent}</>;\n// };\n// interface GroupedResults {\n//   databases: SearchResult[]; pages: SearchResult[]; views: SearchResult[];\n//   documents: SearchResult[]; members: SearchResult[]; reminders: SearchResult[];\n// }\n// const groupResults = (results: SearchResult[]): GroupedResults => {\n//   const grouped = { databases: [], pages: [], views: [], documents: [], members: [], reminders: [] } as GroupedResults;\n//   results.forEach(result => {\n//     if (result.image || !result.source) {\n//       grouped.members.push(result);\n//     } else if (result.source) {\n//       if (result.source.databaseId && !result.source.viewId && !result.source.recordId) grouped.databases.push(result);\n//       else if (result.source.viewId) grouped.views.push(result);\n//       else if (result.source.documentId) grouped.documents.push(result);\n//       else if (result.source.pageId && !result.source.viewId) grouped.pages.push(result);\n//       else if (result.source.reminderId) grouped.reminders.push(result);\n//       else grouped.databases.push(result); // Records go to databases\n//     }\n//   });\n//   return grouped;\n// };\n// interface ApiError extends Error {\n//   response?: { data?: { error?: string; message?: string; status?: number } };\n//   status?: number;\n// }\n// export default function SearchModal({ onClose, debounceTimeoutMS = 300 }: SearchModalProps) {\n//   const [query, setQuery] = useState(\"\"), [results, setResults] = useState<SearchResult[]>([])\n//   const [isLoading, setIsLoading] = useState(false), [isLoadingMore, setIsLoadingMore] = useState(false)\n//   const [error, setError] = useState<string | null>(null)\n//   const [recentSearches, setRecentSearches] = useState<string[]>([])\n//   const [currentPage, setCurrentPage] = useState(1), [hasMore, setHasMore] = useState(true)\n//   const [totalItems, setTotalItems] = useState(0), [currentQuery, setCurrentQuery] = useState(\"\")\n//   const [selectedIndex, setSelectedIndex] = useState(0)\n//   const [shouldPreserveScroll, setShouldPreserveScroll] = useState(false)\n//   const [scrollPosition, setScrollPosition] = useState(0)\n//   const { workspace, url } = useWorkspace(), { token } = useAuth(), router = useRouter()\n//   const workspaceId = workspace?.workspace?.id\n//   const resultsContainerRef = useRef<HTMLDivElement | null>(null)\n//   const inputRef = useRef<HTMLInputElement | null>(null)\n//   const sentinelRef = useRef<HTMLDivElement | null>(null)\n//   const selectedItemRef = useRef<HTMLDivElement | null>(null)\n//   const loadMoreTimeoutRef = useRef<NodeJS.Timeout>()\n//   useEffect(() => {\n//     const savedSearches = localStorage.getItem(\"recentSearches\")\n//     if (savedSearches) setRecentSearches(JSON.parse(savedSearches))\n//     // Focus input on mount\n//     if (inputRef.current) {\n//       inputRef.current.focus()\n//     }\n//     // Cleanup timeout on unmount\n//     return () => {\n//       if (loadMoreTimeoutRef.current) {\n//         clearTimeout(loadMoreTimeoutRef.current)\n//       }\n//     }\n//   }, [])\n//   const getErrorMessage = (error: unknown): string => {\n//     if (error instanceof Error) {\n//       if ('response' in error) {\n//         const apiError = error as ApiError;\n//         return apiError.response?.data?.message || apiError.message;\n//       }\n//       return error.message;\n//     }\n//     return 'An unexpected error occurred';\n//   };\n//   const performSearch = useCallback(async (searchQuery: string, page = 1, append = false) => {\n//     if (!searchQuery.trim() || !workspaceId || !token) {\n//       setResults([]); setError(null); setIsLoading(false); setIsLoadingMore(false);\n//       setHasMore(false); setTotalItems(0); setCurrentPage(1);\n//       return;\n//     }\n//     const currentSearchQuery = searchQuery;\n//     if (page === 1) {\n//       setHasMore(true);\n//       setIsLoading(true); // Only set main loading for first page\n//     } else {\n//       setIsLoadingMore(true); // Use separate loading state for subsequent pages\n//     }\n//     setError(null);\n//     try {\n//       const response = await searchWorkspaces(token.token, workspaceId, currentSearchQuery, page, 10);\n//       if (!response.isSuccess) throw new Error(response.error || 'Search failed');\n//       const newResults = response.data.data.results.results || [];\n//       const pagination = response.data.data.results.pagination;\n//       if (currentSearchQuery === query) {\n//         setTotalItems(pagination.totalItems);\n//         setHasMore(page < pagination.totalPages);\n//         setResults(append ? prev => [...prev, ...newResults] : newResults);\n//         setCurrentPage(page);\n//         setCurrentQuery(currentSearchQuery);\n//       }\n//     } catch (error: unknown) {\n//       console.error(\"Search error:\", error);\n//       if (!append && currentSearchQuery === query) {\n//         setError(getErrorMessage(error));\n//       }\n//     } finally {\n//       if (currentSearchQuery === query) {\n//         if (page === 1) {\n//           setIsLoading(false);\n//         } else {\n//           setIsLoadingMore(false);\n//         }\n//       }\n//     }\n//   }, [workspaceId, token, query]);\n//   const debouncedSearch = useCallback(\n//     debounce((searchQuery: string) => {\n//       if (searchQuery.trim()) {\n//         setCurrentPage(1);\n//         performSearch(searchQuery, 1, false);\n//       }\n//     }, debounceTimeoutMS),\n//     [performSearch, debounceTimeoutMS, setCurrentPage]\n//   );\n//   const loadMore = useCallback(() => {\n//     if (!isLoading && !isLoadingMore && hasMore && currentQuery) {\n//       performSearch(currentQuery, currentPage + 1, true);\n//     }\n//   }, [isLoading, isLoadingMore, hasMore, currentQuery, currentPage, performSearch]);\n//   // Keyboard navigation\n//   useEffect(() => {\n//     const handleKeyDown = (e: KeyboardEvent) => {\n//       if (!results.length && !recentSearches.length) return\n//       const currentResults = query.trim() ? results : recentSearches.map((search, index) => ({ id: `recent-${index}`, title: search }))\n//       if (e.key === 'ArrowDown') {\n//         e.preventDefault()\n//         const nextIndex = Math.min(selectedIndex + 1, currentResults.length - 1)\n//         // Check if we're at the bottom and need to load more\n//         if (nextIndex === currentResults.length - 1 && hasMore && !isLoading && !isLoadingMore && query.trim()) {\n//           if (resultsContainerRef.current) {\n//             setScrollPosition(resultsContainerRef.current.scrollTop)\n//             setShouldPreserveScroll(true)\n//           }\n//           loadMore()\n//         }\n//         setSelectedIndex(nextIndex)\n//       } else if (e.key === 'ArrowUp') {\n//         e.preventDefault()\n//         setSelectedIndex(prev => Math.max(prev - 1, 0))\n//       } else if (e.key === 'Enter') {\n//         e.preventDefault()\n//         if (currentResults[selectedIndex]) {\n//           if (query.trim()) {\n//             handleResultClick(currentResults[selectedIndex] as SearchResult)\n//           } else {\n//             const recentSearch = recentSearches[selectedIndex]\n//             if (recentSearch) {\n//               setQuery(recentSearch)\n//             }\n//           }\n//         }\n//       } else if (e.key === 'Escape') {\n//         onClose()\n//       }\n//     }\n//     document.addEventListener('keydown', handleKeyDown)\n//     return () => document.removeEventListener('keydown', handleKeyDown)\n//   }, [results, recentSearches, selectedIndex, query, onClose, hasMore, isLoading, isLoadingMore, loadMore])\n//   // Handle scroll position preservation and ensure selected item is visible\n//   useEffect(() => {\n//     if (shouldPreserveScroll && resultsContainerRef.current && !isLoadingMore) {\n//       resultsContainerRef.current.scrollTop = scrollPosition\n//       setShouldPreserveScroll(false)\n//     }\n//     if (selectedItemRef.current && resultsContainerRef.current) {\n//       const container = resultsContainerRef.current\n//       const selectedElement = selectedItemRef.current\n//       const containerRect = container.getBoundingClientRect()\n//       const selectedRect = selectedElement.getBoundingClientRect()\n//       if (selectedRect.top < containerRect.top || selectedRect.bottom > containerRect.bottom) {\n//         selectedElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' })\n//       }\n//     }\n//   }, [shouldPreserveScroll, scrollPosition, isLoadingMore, selectedIndex])\n//   // Reset selected index when query changes\n//   useEffect(() => {\n//     setSelectedIndex(0)\n//   }, [query, results])\n//   // Improved infinite scroll implementation\n//   useEffect(() => {\n//     const sentinel = sentinelRef.current;\n//     const container = resultsContainerRef.current;\n//     if (!sentinel || !container || !hasMore || isLoading || isLoadingMore || results.length === 0) {\n//       return;\n//     }\n//     const observer = new IntersectionObserver(\n//       (entries) => {\n//         const [entry] = entries;\n//         if (entry.isIntersecting && \n//             entry.boundingClientRect.top < (entry.rootBounds?.bottom || 0) &&\n//             hasMore && !isLoading && !isLoadingMore && results.length > 0) {\n//           // Clear any existing timeout\n//           if (loadMoreTimeoutRef.current) {\n//             clearTimeout(loadMoreTimeoutRef.current);\n//           }\n//           // Add a small delay to prevent flash\n//           loadMoreTimeoutRef.current = setTimeout(() => {\n//             loadMore();\n//           }, 100);\n//         }\n//       },\n//       {\n//         root: container,\n//         rootMargin: '100px 0px',\n//         threshold: 0.1\n//       }\n//     );\n//     observer.observe(sentinel); \n//     return () => {\n//       observer.disconnect();\n//       if (loadMoreTimeoutRef.current) {\n//         clearTimeout(loadMoreTimeoutRef.current);\n//       }\n//     };\n//   }, [loadMore, hasMore, isLoading, isLoadingMore, results.length, currentPage, totalItems]);\n//   // Handle search input changes\n//   useEffect(() => {\n//     debouncedSearch.cancel();\n//     setResults([]);\n//     setCurrentPage(1);\n//     setHasMore(false);\n//     setTotalItems(0);\n//     setCurrentQuery(\"\");\n//     setIsLoadingMore(false);\n//     if (query.trim()) {\n//       setIsLoading(true);\n//       debouncedSearch(query);\n//     } else {\n//       setIsLoading(false);\n//     }\n//     return () => debouncedSearch.cancel();\n//   }, [query, debouncedSearch])\n//   const handleResultClick = (result: SearchResult) => {\n//     router.push(url(result.path)); onClose(); saveRecentSearch(query);\n//   }\n//   const saveRecentSearch = (search: string) => {\n//     if (search.trim()) {\n//       const updatedSearches = [search, ...recentSearches.filter((s) => s !== search)].slice(0, 5)\n//       setRecentSearches(updatedSearches)\n//       localStorage.setItem(\"recentSearches\", JSON.stringify(updatedSearches))\n//     }\n//   }\n//   const deleteRecentSearch = (search: string, e: React.MouseEvent) => {\n//     e.stopPropagation()\n//     const updatedSearches = recentSearches.filter(s => s !== search)\n//     setRecentSearches(updatedSearches)\n//     localStorage.setItem(\"recentSearches\", JSON.stringify(updatedSearches))\n//   }\n//   const getIconForSource = (result: SearchResult) => {\n//     if (result.image) return null;\n//     if (result.source?.viewId && result.viewType)\n//       return <ViewIcon type={result.viewType} className=\"h-4 w-4 text-gray-600\" />;\n//     if (result.source) {\n//       if (result.source.databaseId) return <TableIcon className=\"h-4 w-4 text-gray-600\" />;\n//       if (result.source.documentId) return <BookIcon className=\"h-4 w-4 text-gray-600\" />;\n//       if (result.source.pageId) return <BookIcon className=\"h-4 w-4 text-gray-600\" />;\n//     }\n//     return <UserGroupIcon className=\"h-4 w-4 text-gray-500\" />;\n//   };\n//   const getResultType = (result: SearchResult) => {\n//     if (result.path?.includes('?tab=reminders')) return \"Reminder\";\n//     if (result.path?.includes('?tab=notes')) return \"Note\";\n//     if (result.image) return \"Member\";\n//     if (result.source) {\n//       if (result.source.databaseId) {\n//         if (result.viewType) {\n//           switch (result.viewType) {\n//             case ViewType.Table: return \"Table\";\n//             case ViewType.Board: return \"Board\";\n//             case ViewType.Dashboard: return \"Dashboard\";\n//             case ViewType.Document: return \"Page\";\n//             case ViewType.Form: return \"Form\";\n//             case ViewType.SummaryTable: return \"Summary\";\n//             default: return \"View\";\n//           }\n//         }\n//         return result.source.recordId ? \"Record\" : \"Database\";\n//       }\n//       if (result.source.documentId) return \"Document\";\n//       if (result.source.pageId) return \"Page\";\n//       if (result.source.reminderId) return \"Reminder\";\n//     }\n//     return result.source?.databaseId && result.source.recordId ? \"Record\" : \n//            result.source?.databaseId ? \"Database\" : \n//            result.source?.documentId ? \"Document\" : \n//            result.source?.pageId ? \"Page\" : \"Document\";\n//   };\n//   const categoryConfig: Record<string, { name: string; icon: JSX.Element }> = {\n//     databases: { name: \"Databases & Records\", icon: <TableIcon className=\"h-4 w-4 text-gray-600\" /> },\n//     pages: { name: \"Pages\", icon: <BookIcon className=\"h-4 w-4 text-gray-600\" /> },\n//     views: { name: \"Views\", icon: <ViewIcon type={ViewType.Table} className=\"h-4 w-4 text-gray-600\" /> },\n//     documents: { name: \"Documents\", icon: <BookIcon className=\"h-4 w-4 text-gray-600\" /> },\n//     members: { name: \"People\", icon: <UserGroupIcon className=\"h-4 w-4 text-gray-500\" /> },\n//     reminders: { name: \"Reminders\", icon: <TimerIcon className=\"h-4 w-4 text-gray-600\" /> }\n//   };\n//   const getCategoryName = (category: string) => categoryConfig[category]?.name || category;\n//   const getCategoryIcon = (category: string) => categoryConfig[category]?.icon || <MagnifyingGlassIcon className=\"h-4 w-4 text-gray-400\" />;\n//   return (\n//     <Dialog open onOpenChange={onClose}>\n//       <DialogContent className=\"p-0 gap-0 max-w-xl rounded-lg shadow-xl border border-gray-200 overflow-hidden\" hideCloseBtn>\n//         <div className=\"flex items-center border-b px-3 relative bg-gray-50\">\n//           <MagnifyingGlassIcon className=\"mr-2 h-4 w-4 shrink-0 text-gray-500\" />\n//           <Input\n//             ref={inputRef}\n//             className=\"flex h-14 rounded-md border-0 bg-transparent py-3 text-sm outline-none placeholder:text-gray-500 focus-visible:ring-0 disabled:cursor-not-allowed disabled:opacity-50\"\n//             placeholder={`Search in ${workspace?.workspace?.name || 'workspace'}...`}\n//             value={query}\n//             onChange={(e) => setQuery(e.target.value)}\n//             autoFocus\n//           />\n//           <div className=\"flex items-center gap-2 text-xs text-gray-500 ml-2\">\n//             <kbd className=\"px-1.5 py-0.5 bg-gray-100 rounded text-xs font-mono\">ESC to close</kbd>\n//           </div>\n//         </div>\n//         <div ref={resultsContainerRef} className=\"max-h-[60vh] overflow-y-auto pr-1\">\n//           {isLoading && currentPage === 1 ? (\n//             <div className=\"px-6 py-8 text-center\">\n//               <Loader className=\"inline-block w-6 h-6 text-gray-600 animate-spin\" />\n//               <p className=\"mt-3 text-sm text-gray-600\">Searching workspace...</p>\n//             </div>\n//           ) : error ? (\n//             <div className=\"px-6 py-8 text-center\">\n//               <div className=\"mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-100 mb-3\">\n//                 <XmarkIcon className=\"h-5 w-5 text-red-500\" />\n//               </div>\n//               <h3 className=\"text-sm font-medium text-gray-900\">Search Error</h3>\n//               <p className=\"mt-1 text-sm text-red-500\">{error}</p>\n//               <div className=\"mt-4 flex justify-center gap-3\">\n//                 <button \n//                   onClick={() => {\n//                     setError(null);\n//                     debouncedSearch(query);\n//                   }}\n//                   className=\"inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-primary-600 rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n//                 >\n//                   Try again\n//                 </button>\n//                 <button\n//                   onClick={onClose}\n//                   className=\"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n//                 >\n//                   Cancel\n//                 </button>\n//               </div>\n//             </div>\n//           ) : !query.trim() ? (\n//             <div className=\"px-4 py-4\">\n//               <h3 className=\"text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2 px-2\">Recent Searches</h3>\n//               {recentSearches.length > 0 ? (\n//                 recentSearches.map((search, index) => (\n//                   <div \n//                     key={index}\n//                     ref={selectedIndex === index ? selectedItemRef : null}\n//                     className={`flex items-center justify-between px-2 py-2 cursor-pointer rounded transition-colors ${\n//                       selectedIndex === index ? 'bg-accent border-l-2 border-primary' : 'hover:bg-gray-50'\n//                     }`}\n//                     onClick={() => setQuery(search)}\n//                   >\n//                     <div className=\"flex items-center\">\n//                       <TimerIcon className=\"h-4 w-4 text-gray-400 mr-2\" />\n//                       <span className=\"text-sm text-gray-700\">{search}</span>\n//                     </div>\n//                     <button \n//                       onClick={(e) => deleteRecentSearch(search, e)} \n//                       className=\"p-1 rounded-full hover:bg-gray-100 transition-colors\"\n//                     >\n//                       <XmarkIcon className=\"h-3 w-3 text-gray-500\" />\n//                     </button>\n//                   </div>\n//                 ))\n//               ) : (\n//                 <div className=\"px-2 py-3 text-sm text-gray-500\">\n//                   No recent searches\n//                 </div>\n//               )}\n//             </div>\n//           ) : results.length === 0 ? (\n//             <div className=\"px-6 py-8 text-center\">\n//               <div className=\"mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-gray-100 mb-3\">\n//                 <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-500\" />\n//               </div>\n//               <h3 className=\"text-sm font-medium text-gray-900\">No results found</h3>\n//               <p className=\"mt-1 text-sm text-gray-500\">We couldn't find anything matching \"{query}\"</p>\n//             </div>\n//           ) : (\n//             <>\n//               {Object.entries(groupResults(results)).map(([category, items]) => \n//                 items.length > 0 && (\n//                   <div key={category}>\n//                     <h3 className=\"text-xs font-semibold text-gray-500 uppercase tracking-wider px-3 py-2\">\n//                       {getCategoryName(category)} ({items.length})\n//                     </h3>\n//                     {items.map((result: SearchResult, index: number) => {\n//                       const globalIndex = Object.entries(groupResults(results))\n//                         .slice(0, Object.keys(groupResults(results)).indexOf(category))\n//                         .reduce((acc, [, items]) => acc + items.length, 0) + index\n//                       const isSelected = selectedIndex === globalIndex\n//                       return (\n//                         <div\n//                           key={result.id}\n//                           ref={isSelected ? selectedItemRef : null}\n//                           className={`px-3 py-3 cursor-pointer transition-colors flex items-start gap-3 ${\n//                             isSelected\n//                               ? 'bg-accent border-l-2 border-primary'\n//                               : 'hover:bg-gray-50'\n//                           }`}\n//                           onClick={() => handleResultClick(result)}\n//                         >\n//                           {result.image ? (\n//                             <img \n//                               src={result.image} \n//                               alt={result.name || ''}\n//                               className=\"h-8 w-8 rounded-full object-cover mt-1\"\n//                             />\n//                           ) : (\n//                             <div className=\"flex-shrink-0 mt-0.5 p-2 rounded-lg bg-gray-100 text-gray-600\">\n//                               {getIconForSource(result)}\n//                             </div>\n//                           )}\n//                           <div className=\"min-w-0 flex-1\">\n//                             <div className=\"flex justify-between items-baseline\">\n//                               <h3 className=\"text-sm font-medium text-gray-900 truncate\">\n//                                 <HighlightedContent \n//                                   content={result.title || result.name} \n//                                   highlight={result.highlight}\n//                                   query={query}\n//                                 />\n//                               </h3>\n//                               {result.publishedAt && (\n//                                 <span className=\"text-xs text-gray-500 ml-2 whitespace-nowrap\">\n//                                   {formatDistanceToNow(new Date(result.publishedAt), { addSuffix: true })}\n//                                 </span>\n//                               )}\n//                             </div>\n//                             {result.content && (\n//                               <p className=\"mt-1 text-xs text-gray-500 line-clamp-2\">\n//                                 <HighlightedContent \n//                                   content={result.content} \n//                                   highlight={result.highlight}\n//                                   query={query}\n//                                 />\n//                               </p>\n//                             )}\n//                             {result.source && (\n//                               <div className=\"mt-1.5\">\n//                                 <span className=\"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800\">\n//                                   {getResultType(result)}\n//                                 </span>\n//                               </div>\n//                             )}\n//                           </div>\n//                         </div>\n//                       )\n//                     })}\n//                   </div>\n//                 )\n//               )}\n//               {/* Loading indicator for subsequent pages */}\n//               {isLoadingMore && (\n//                 <div className=\"py-4 text-center transition-opacity duration-300 ease-in-out\">\n//                   <Loader className=\"inline-block w-4 h-4 text-gray-400 animate-spin\" />\n//                   <span className=\"ml-2 text-sm text-gray-500\">Loading more results...</span>\n//                 </div>\n//               )}\n//               {/* Infinite scroll sentinel - positioned before the final status messages */}\n//               {hasMore && !isLoading && !isLoadingMore && (\n//                 <div \n//                   ref={sentinelRef} \n//                   className=\"h-8 w-full flex items-center justify-center\"\n//                   style={{ minHeight: SENTINEL_HEIGHT }}\n//                 >\n//                   <div className=\"text-xs text-gray-400\">• • •</div>\n//                 </div>\n//               )}\n//               {/* Final status message */}\n//               {!isLoading && !isLoadingMore && !hasMore && results.length > 0 && (\n//                 <div className=\"py-4 text-center text-sm text-gray-500 border-t\">\n//                   {totalItems > 0 ? `Showing all ${totalItems} results` : 'No more results'}\n//                 </div>\n//               )}\n//             </>\n//           )}\n//         </div>\n//       </DialogContent>\n//     </Dialog>\n//   )\n// }\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/common/searchmodal.tsx\n"));

/***/ })

});