import React, { useMemo } from 'react';
import { format, startOfMonth, endOfMonth, startOfWeek, endOfWeek, addDays, isSameDay, isToday } from 'date-fns';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { PlusIcon } from '@heroicons/react/24/outline';
import { ScrollArea } from '@/components/ui/scroll-area';
import { CalendarEvent } from '@/typings/page';
import { useMaybeRecord } from '@/providers/record';
import { CalendarEventItem } from './CalendarEventItem';
import { NoEvents } from './NoEvents';
import { CalendarSideCard } from './CalendarSideCard';
import { useDroppable } from '@dnd-kit/core';


interface MonthViewProps {
  selectedDate: Date;
  events: CalendarEvent[];
  selectedEvent: string | null;
  setSelectedEvent: (id: string) => void;
  setSelectedDate: (date: Date) => void;
  openAddEventForm: (date: Date) => void;
  canEditData: boolean;
  handleEventClick: (event: CalendarEvent) => void;
  activeDragData: any;
}

const DayCell = ({
  date,
  children,
  onClick,
  isCurrentMonth
}: {
  date: Date;
  children: React.ReactNode;
  onClick: () => void;
  isCurrentMonth: boolean;
}) => {
  const { setNodeRef, isOver } = useDroppable({
    id: `daycell-${format(date, 'yyyy-MM-dd')}`,
    data: {
      date: date,
      type: 'daycell'
    }
  });

  return (
    <div
      ref={setNodeRef}
      onClick={onClick}
      className={cn(
        "border-b border-r border-neutral-300 relative cursor-pointer transition-colors group flex flex-col p-2 min-h-[120px] lg:p-3 lg:min-h-[140px]",
        isCurrentMonth
          ? "bg-white hover:bg-neutral-50"
          : "bg-neutral-100 hover:bg-neutral-200",
        isOver && "bg-blue-50 border-blue-200"
      )}
    >
      {children}
    </div>
  );
};

// Helper function to check if an event affects a specific day
const eventAffectsDay = (event: CalendarEvent, day: Date): boolean => {
  const eventStart = new Date(event.start);
  const eventEnd = new Date(event.end);
  const dayStart = new Date(day);
  const dayEnd = new Date(day);
  dayEnd.setHours(23, 59, 59, 999);
  
  return eventStart <= dayEnd && eventEnd >= dayStart;
};

// New helper function to process events for the entire month with proper slot tracking
const useMonthEvents = (weeks: Date[][], events: CalendarEvent[]) => {
  return useMemo(() => {
    const positionedEventsByWeek = new Map<number, any[]>();
    const slotUsageByDay = new Map<string, number>(); // Track slot usage per day

    weeks.forEach((week, weekIndex) => {
      const weekStart = week[0];
      const weekEnd = week[6];

      // Get all events that intersect with this week
      const weekEvents = events.filter(event => {
        const eventStart = new Date(event.start);
        const eventEnd = new Date(event.end);
        return eventStart <= weekEnd && eventEnd >= weekStart;
      });

      const spanningEvents: any[] = [];
      weekEvents.forEach(event => {
        const eventStart = new Date(event.start);
        const eventEnd = new Date(event.end);
        
        // Find which days this event spans in this week
        const startDayIndex = week.findIndex(day => isSameDay(day, eventStart));
        const endDayIndex = week.findIndex(day => isSameDay(day, eventEnd));

        // Calculate actual span within this week
        let actualStart = 0;
        let actualEnd = 6;
        
        if (startDayIndex !== -1) {
          actualStart = startDayIndex;
        }
        if (endDayIndex !== -1) {
          actualEnd = endDayIndex;
        }

        // Check if event intersects with this week
        const eventSpansWeek = startDayIndex !== -1 || endDayIndex !== -1 || (eventStart < weekStart && eventEnd > weekEnd);

        if (eventSpansWeek) {
          spanningEvents.push({
            event,
            startDayIndex: actualStart,
            endDayIndex: actualEnd,
            colSpan: actualEnd - actualStart + 1,
          });
        }
      });
      
      // Sort events by start day, then by span length (longer events first)
      const sortedEvents = spanningEvents.sort((a, b) => {
        if (a.startDayIndex !== b.startDayIndex) {
          return a.startDayIndex - b.startDayIndex;
        }
        return b.colSpan - a.colSpan;
      });

      // Position events and track slot usage
      const positioned: any[] = [];
      const rows: any[][] = [];

      sortedEvents.forEach(eventData => {
        // Check if this event can be placed (all days it spans have available slots)
        const affectedDays = week.slice(eventData.startDayIndex, eventData.endDayIndex + 1);
        const canPlace = affectedDays.every(day => {
          const dayKey = format(day, 'yyyy-MM-dd');
          const currentUsage = slotUsageByDay.get(dayKey) || 0;
          return currentUsage < 4; // Maximum 4 slots per day
        });

        if (!canPlace) {
          // Event cannot be placed, skip it (it will be in the "+more" count)
          return;
        }

        // Find available row
        let assigned = false;
        for (let i = 0; i < rows.length; i++) {
          const row = rows[i];
          const hasConflict = row.some(existingEvent => 
            eventData.startDayIndex <= existingEvent.endDayIndex && 
            eventData.endDayIndex >= existingEvent.startDayIndex
          );
          
          if (!hasConflict) {
            row.push(eventData);
            positioned.push({ ...eventData, row: i });
            assigned = true;
            break;
          }
        }

        if (!assigned) {
          rows.push([eventData]);
          positioned.push({ ...eventData, row: rows.length - 1 });
        }

        // Update slot usage for all affected days
        affectedDays.forEach(day => {
          const dayKey = format(day, 'yyyy-MM-dd');
          const currentUsage = slotUsageByDay.get(dayKey) || 0;
          slotUsageByDay.set(dayKey, currentUsage + 1);
        });
      });

      positionedEventsByWeek.set(weekIndex, positioned);
    });

    return { positionedEventsByWeek, slotUsageByDay };
  }, [weeks, events]);
};

export const MonthView: React.FC<MonthViewProps> = ({
  selectedDate,
  events,
  selectedEvent,
  setSelectedEvent,
  setSelectedDate,
  openAddEventForm,
  canEditData,
  handleEventClick,
  activeDragData,
}) => {
  const maybeRecord = useMaybeRecord();

  const isInRecordTab = !!maybeRecord;

  // Memoize month calculations
  const monthCalculations = useMemo(() => {
    const monthStart = startOfMonth(selectedDate);
    const monthEnd = endOfMonth(selectedDate);
    const startDay = startOfWeek(monthStart, { weekStartsOn: 0 });
    const endDay = endOfWeek(monthEnd, { weekStartsOn: 0 });

    const days = [];
    let day = startDay;
    while (day <= endDay) {
      days.push(day);
      day = addDays(day, 1);
    }

    const weeks = [];
    for (let i = 0; i < days.length; i += 7) {
      weeks.push(days.slice(i, i + 7));
    }

    return { monthStart, monthEnd, startDay, endDay, days, weeks };
  }, [selectedDate]);

  // Memoize month events
  const monthEvents = useMemo(() => 
    events.filter(event => {
      const eventStart = new Date(event.start);
      const eventEnd = new Date(event.end);
      return eventStart <= monthCalculations.endDay && 
             eventEnd >= monthCalculations.startDay;
    }), 
    [events, monthCalculations.startDay, monthCalculations.endDay]
  );

  const { positionedEventsByWeek, slotUsageByDay } = useMonthEvents(monthCalculations.weeks, monthEvents);

  // Render empty state when no events
  const renderEmptyState = () => (
    <div className="flex flex-col h-full bg-background">
      <div className="grid grid-cols-7 border-b border-neutral-300 bg-white">
        {['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'].map(dayName => (
          <div key={dayName} className={cn(
            "text-center font-semibold text-black",
            "py-2 text-xs"
          )}>
            {dayName.substring(0, 3)}
          </div>
        ))}
      </div>

      <NoEvents
        title="No events this month"
        message={`${format(selectedDate, 'MMMM yyyy')} is completely free. Start planning your month!`}
        showCreateButton={canEditData}
        onCreate={() => openAddEventForm(selectedDate)}
      />
    </div>
  );

  // Render day cell content
  const renderDayCellContent = (day: Date, dayEvents: any[]) => {
    const isCurrentMonth = day.getMonth() === selectedDate.getMonth();
    const isCurrentDay = isToday(day);
    const MAX_VISIBLE_EVENTS = 4;
    const ROW_HEIGHT = 28;

    const dayKey = format(day, 'yyyy-MM-dd');
    
    // Get all events that affect this day (for the +more count)
    const allDayEvents = monthEvents.filter(event => eventAffectsDay(event, day));
    const totalEventsForDay = allDayEvents.length;
    
    const sortedEvents = dayEvents.sort((a, b) => a.row - b.row);
    const visibleEvents = sortedEvents.slice(0, MAX_VISIBLE_EVENTS);
    const hasMore = totalEventsForDay > MAX_VISIBLE_EVENTS;
    const hiddenEventsCount = Math.max(0, totalEventsForDay - MAX_VISIBLE_EVENTS);

    // Calculate container height
    const maxRow = visibleEvents.reduce((max, event) => Math.max(max, event.row), -1);
    const containerHeight = hasMore
      ? (MAX_VISIBLE_EVENTS * ROW_HEIGHT) + 20 
      : (maxRow + 1) * ROW_HEIGHT;

    return (
      <>
        <div className="flex items-center justify-between mb-2">
          <span className={cn(
            "inline-flex items-center justify-center rounded-full font-semibold text-xs w-6 h-6",
            isCurrentDay ? "bg-black text-white" : isCurrentMonth ? "text-black hover:bg-neutral-100" : "text-neutral-400"
          )}>
            {format(day, 'd')}
          </span>
          {canEditData && isCurrentMonth && (
            <Button
              variant="ghost"
              className="rounded-full h-6 w-6 p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-neutral-100 hidden lg:inline-flex"
              onClick={(e) => {
                e.stopPropagation();
                setTimeout(() => openAddEventForm(day), 150);
              }}
            >
              <PlusIcon className="h-3 w-3 text-black" />
            </Button>
          )}
        </div>

        <div className="relative" style={{ height: `${containerHeight}px` }}>
          {visibleEvents.map(pe => (
            <div
              key={pe.event.id}
              className="absolute"
              style={{
                top: `${pe.row * ROW_HEIGHT}px`,
                left: '2px',
                width: pe.colSpan > 1 
                  ? `calc(${pe.colSpan * 100}% + ${(pe.colSpan - 1) * 19}px)`
                  : 'calc(100% - 4px)',
                zIndex: 10 + pe.row,
              }}
            >
              <CalendarEventItem
                event={pe.event}
                view="month"
                onClick={(e) => {
                  e.stopPropagation();
                  setSelectedEvent(pe.event.id);
                  handleEventClick(pe.event);
                }}
                isDragging={activeDragData?.payload?.id === pe.event.id}
              />
            </div>
          ))}

          {hasMore && (
            <div 
              className="text-black hover:text-black font-medium text-xs cursor-pointer"
              style={{
                position: 'absolute',
                top: `${MAX_VISIBLE_EVENTS * ROW_HEIGHT}px`,
                left: '2px',
              }}
              onClick={(e) => {
                e.stopPropagation();
                setSelectedDate(day);
              }}
            >
              + {hiddenEventsCount} more
            </div>
          )}
        </div>
      </>
    );
  };

  // Render main view
  return (
    <div className="h-full bg-background flex flex-col lg:flex-row">
      <div className="flex-1 flex flex-col min-h-0">
        {/* Day Headers */}
        <div 
          data-day-headers="true"
          className="grid grid-cols-7 border-b border-neutral-300 sticky top-0 z-10 bg-white"
        >
          {['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'].map(dayName => (
            <div key={dayName} className={cn(
              "text-center font-semibold text-black",
              "py-2 text-xs"
            )}>
              {dayName.substring(0, 3)}
            </div>
          ))}
        </div>

        {/* Month Grid */}
        <ScrollArea className="flex-1">
          <div className="grid grid-cols-7 border-neutral-300 border-b">
            {monthCalculations.weeks.map((week, weekIndex) =>
              week.map((day, dayIndex) => {
                const weekEvents = positionedEventsByWeek.get(weekIndex) || [];
                const dayEvents = weekEvents.filter(pe => pe.startDayIndex === dayIndex);
                const isCurrentMonth = day.getMonth() === selectedDate.getMonth();

                return (
                  <DayCell
                    key={`${weekIndex}-${dayIndex}`}
                    date={day}
                    isCurrentMonth={isCurrentMonth}
                    onClick={() => setSelectedDate(day)}
                  >
                    {renderDayCellContent(day, dayEvents)}
                  </DayCell>
                );
              }),
            )}
          </div>
        </ScrollArea>
      </div>

      <CalendarSideCard
        selectedDate={selectedDate}
        events={events}
        selectedEvent={selectedEvent}
        setSelectedEvent={setSelectedEvent}
        handleEventClick={handleEventClick}
      />
    </div>
  );
};

function isMultiDay(event: CalendarEvent): boolean {
  return !isSameDay(new Date(event.start), new Date(event.end));
}