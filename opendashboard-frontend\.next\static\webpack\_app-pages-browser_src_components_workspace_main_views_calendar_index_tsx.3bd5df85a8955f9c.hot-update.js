"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx":
/*!*******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/MonthView.tsx ***!
  \*******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MonthView: function() { return /* binding */ MonthView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CalendarEventItem */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx\");\n/* harmony import */ var _NoEvents__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./NoEvents */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/NoEvents.tsx\");\n/* harmony import */ var _CalendarSideCard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./CalendarSideCard */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarSideCard.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst DayCell = (param)=>{\n    let { date, children, onClick, isCurrentMonth } = param;\n    _s();\n    const { setNodeRef, isOver } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__.useDroppable)({\n        id: \"daycell-\".concat((0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(date, \"yyyy-MM-dd\")),\n        data: {\n            date: date,\n            type: \"daycell\"\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: setNodeRef,\n        onClick: onClick,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-b border-r border-neutral-300 relative cursor-pointer transition-colors group flex flex-col p-2 min-h-[120px] lg:p-3 lg:min-h-[140px]\", isCurrentMonth ? \"bg-white hover:bg-neutral-50\" : \"bg-neutral-100 hover:bg-neutral-200\", isOver && \"bg-blue-50 border-blue-200\"),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DayCell, \"DmJTTt6A5xWIX/faBiFge3FOLrw=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__.useDroppable\n    ];\n});\n_c = DayCell;\n// Helper function to check if an event affects a specific day\nconst eventAffectsDay = (event, day)=>{\n    const eventStart = new Date(event.start);\n    const eventEnd = new Date(event.end);\n    const dayStart = new Date(day);\n    const dayEnd = new Date(day);\n    dayEnd.setHours(23, 59, 59, 999);\n    return eventStart <= dayEnd && eventEnd >= dayStart;\n};\n// New helper function to process events for the entire month with proper slot tracking\nconst useMonthEvents = (weeks, events)=>{\n    _s1();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const positionedEventsByWeek = new Map();\n        const slotUsageByDay = new Map(); // Track slot usage per day\n        weeks.forEach((week, weekIndex)=>{\n            const weekStart = week[0];\n            const weekEnd = week[6];\n            // Get all events that intersect with this week\n            const weekEvents = events.filter((event)=>{\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                return eventStart <= weekEnd && eventEnd >= weekStart;\n            });\n            const spanningEvents = [];\n            weekEvents.forEach((event)=>{\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                // Find which days this event spans in this week\n                const startDayIndex = week.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(day, eventStart));\n                const endDayIndex = week.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(day, eventEnd));\n                // Calculate actual span within this week\n                let actualStart = 0;\n                let actualEnd = 6;\n                // For events that start in this week\n                if (startDayIndex !== -1) {\n                    actualStart = startDayIndex;\n                }\n                // For events that end in this week\n                if (endDayIndex !== -1) {\n                    actualEnd = endDayIndex;\n                }\n                // For events that span through this week entirely\n                if (startDayIndex === -1 && endDayIndex === -1 && eventStart < weekStart && eventEnd > weekEnd) {\n                    actualStart = 0;\n                    actualEnd = 6;\n                }\n                // Check if event intersects with this week\n                const eventSpansWeek = startDayIndex !== -1 || endDayIndex !== -1 || eventStart < weekStart && eventEnd > weekEnd;\n                if (eventSpansWeek) {\n                    spanningEvents.push({\n                        event,\n                        startDayIndex: actualStart,\n                        endDayIndex: actualEnd,\n                        colSpan: actualEnd - actualStart + 1\n                    });\n                }\n            });\n            // Sort events by start day, then by span length (longer events first)\n            const sortedEvents = spanningEvents.sort((a, b)=>{\n                if (a.startDayIndex !== b.startDayIndex) {\n                    return a.startDayIndex - b.startDayIndex;\n                }\n                return b.colSpan - a.colSpan;\n            });\n            // Position events and track slot usage\n            const positioned = [];\n            const rows = [];\n            sortedEvents.forEach((eventData)=>{\n                // Check if this event can be placed (all days it spans have available slots)\n                const affectedDays = week.slice(eventData.startDayIndex, eventData.endDayIndex + 1);\n                const canPlace = affectedDays.every((day)=>{\n                    const dayKey = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(day, \"yyyy-MM-dd\");\n                    const currentUsage = slotUsageByDay.get(dayKey) || 0;\n                    return currentUsage < 4; // Maximum 4 slots per day\n                });\n                if (!canPlace) {\n                    // Event cannot be placed, skip it (it will be in the \"+more\" count)\n                    return;\n                }\n                // Find available row\n                let assigned = false;\n                for(let i = 0; i < rows.length; i++){\n                    const row = rows[i];\n                    const hasConflict = row.some((existingEvent)=>eventData.startDayIndex <= existingEvent.endDayIndex && eventData.endDayIndex >= existingEvent.startDayIndex);\n                    if (!hasConflict) {\n                        row.push(eventData);\n                        positioned.push({\n                            ...eventData,\n                            row: i\n                        });\n                        assigned = true;\n                        break;\n                    }\n                }\n                if (!assigned) {\n                    rows.push([\n                        eventData\n                    ]);\n                    positioned.push({\n                        ...eventData,\n                        row: rows.length - 1\n                    });\n                }\n                // Update slot usage for all affected days\n                affectedDays.forEach((day)=>{\n                    const dayKey = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(day, \"yyyy-MM-dd\");\n                    const currentUsage = slotUsageByDay.get(dayKey) || 0;\n                    slotUsageByDay.set(dayKey, currentUsage + 1);\n                });\n            });\n            positionedEventsByWeek.set(weekIndex, positioned);\n        });\n        return {\n            positionedEventsByWeek,\n            slotUsageByDay\n        };\n    }, [\n        weeks,\n        events\n    ]);\n};\n_s1(useMonthEvents, \"nwk+m61qLgjDVUp4IGV/072DDN4=\");\nconst MonthView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, setSelectedDate, openAddEventForm, canEditData, handleEventClick, activeDragData } = param;\n    _s2();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_5__.useMaybeRecord)();\n    const isInRecordTab = !!maybeRecord;\n    // Memoize month calculations\n    const monthCalculations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const monthStart = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(selectedDate);\n        const monthEnd = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(selectedDate);\n        const startDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(monthStart, {\n            weekStartsOn: 0\n        });\n        const endDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(monthEnd, {\n            weekStartsOn: 0\n        });\n        const days = [];\n        let day = startDay;\n        while(day <= endDay){\n            days.push(day);\n            day = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(day, 1);\n        }\n        const weeks = [];\n        for(let i = 0; i < days.length; i += 7){\n            weeks.push(days.slice(i, i + 7));\n        }\n        return {\n            monthStart,\n            monthEnd,\n            startDay,\n            endDay,\n            days,\n            weeks\n        };\n    }, [\n        selectedDate\n    ]);\n    // Memoize month events\n    const monthEvents = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>events.filter((event)=>{\n            const eventStart = new Date(event.start);\n            const eventEnd = new Date(event.end);\n            return eventStart <= monthCalculations.endDay && eventEnd >= monthCalculations.startDay;\n        }), [\n        events,\n        monthCalculations.startDay,\n        monthCalculations.endDay\n    ]);\n    const { positionedEventsByWeek, slotUsageByDay } = useMonthEvents(monthCalculations.weeks, monthEvents);\n    // Render empty state when no events\n    const renderEmptyState = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-7 border-b border-neutral-300 bg-white\",\n                    children: [\n                        \"Sunday\",\n                        \"Monday\",\n                        \"Tuesday\",\n                        \"Wednesday\",\n                        \"Thursday\",\n                        \"Friday\",\n                        \"Saturday\"\n                    ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-black\", \"py-2 text-xs\"),\n                            children: dayName.substring(0, 3)\n                        }, dayName, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NoEvents__WEBPACK_IMPORTED_MODULE_7__.NoEvents, {\n                    title: \"No events this month\",\n                    message: \"\".concat((0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate, \"MMMM yyyy\"), \" is completely free. Start planning your month!\"),\n                    showCreateButton: canEditData,\n                    onCreate: ()=>openAddEventForm(selectedDate)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 263,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n            lineNumber: 251,\n            columnNumber: 5\n        }, undefined);\n    // Render day cell content\n    const renderDayCellContent = (day, dayEvents)=>{\n        const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n        const isCurrentDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(day);\n        const MAX_VISIBLE_EVENTS = 4;\n        const ROW_HEIGHT = 28;\n        const dayKey = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(day, \"yyyy-MM-dd\");\n        // Get all events that affect this day (for the +more count)\n        const allDayEvents = monthEvents.filter((event)=>eventAffectsDay(event, day));\n        const totalEventsForDay = allDayEvents.length;\n        const sortedEvents = dayEvents.sort((a, b)=>a.row - b.row);\n        const visibleEvents = sortedEvents.slice(0, MAX_VISIBLE_EVENTS);\n        const hasMore = totalEventsForDay > MAX_VISIBLE_EVENTS;\n        const hiddenEventsCount = Math.max(0, totalEventsForDay - MAX_VISIBLE_EVENTS);\n        // Calculate container height\n        const maxRow = visibleEvents.reduce((max, event)=>Math.max(max, event.row), -1);\n        const containerHeight = hasMore ? MAX_VISIBLE_EVENTS * ROW_HEIGHT + 20 : (maxRow + 1) * ROW_HEIGHT;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center rounded-full font-semibold text-xs w-6 h-6\", isCurrentDay ? \"bg-black text-white\" : isCurrentMonth ? \"text-black hover:bg-neutral-100\" : \"text-neutral-400\"),\n                            children: (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(day, \"d\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 11\n                        }, undefined),\n                        canEditData && isCurrentMonth && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            className: \"rounded-full h-6 w-6 p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-neutral-100 hidden lg:inline-flex\",\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                setTimeout(()=>openAddEventForm(day), 150);\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"h-3 w-3 text-black\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 298,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    style: {\n                        height: \"\".concat(containerHeight, \"px\")\n                    },\n                    children: [\n                        visibleEvents.map((pe)=>{\n                            var _activeDragData_payload;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute\",\n                                style: {\n                                    top: \"\".concat(pe.row * ROW_HEIGHT, \"px\"),\n                                    left: \"2px\",\n                                    width: pe.colSpan > 1 ? \"calc(\".concat(pe.colSpan * 100, \"% + \").concat((pe.colSpan - 1) * 19, \"px)\") : \"calc(100% - 4px)\",\n                                    zIndex: 10 + pe.row\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__.CalendarEventItem, {\n                                    event: pe.event,\n                                    view: \"month\",\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        setSelectedEvent(pe.event.id);\n                                        handleEventClick(pe.event);\n                                    },\n                                    isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === pe.event.id\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, pe.event.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 13\n                            }, undefined);\n                        }),\n                        hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-black hover:text-black font-medium text-xs cursor-pointer\",\n                            style: {\n                                position: \"absolute\",\n                                top: \"\".concat(MAX_VISIBLE_EVENTS * ROW_HEIGHT, \"px\"),\n                                left: \"2px\"\n                            },\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                setSelectedDate(day);\n                            },\n                            children: [\n                                \"+ \",\n                                hiddenEventsCount,\n                                \" more\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 319,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true);\n    };\n    // Render main view\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-background flex flex-col lg:flex-row\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col min-h-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        \"data-day-headers\": \"true\",\n                        className: \"grid grid-cols-7 border-b border-neutral-300 sticky top-0 z-10 bg-white\",\n                        children: [\n                            \"Sunday\",\n                            \"Monday\",\n                            \"Tuesday\",\n                            \"Wednesday\",\n                            \"Thursday\",\n                            \"Friday\",\n                            \"Saturday\"\n                        ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-black\", \"py-2 text-xs\"),\n                                children: dayName.substring(0, 3)\n                            }, dayName, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 372,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n                        className: \"flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-7 border-neutral-300 border-b\",\n                            children: monthCalculations.weeks.map((week, weekIndex)=>week.map((day, dayIndex)=>{\n                                    const weekEvents = positionedEventsByWeek.get(weekIndex) || [];\n                                    // Get events that start on this day OR span through this day\n                                    const dayEvents = weekEvents.filter((pe)=>pe.startDayIndex === dayIndex || pe.startDayIndex < dayIndex && pe.endDayIndex >= dayIndex);\n                                    const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DayCell, {\n                                        date: day,\n                                        isCurrentMonth: isCurrentMonth,\n                                        onClick: ()=>setSelectedDate(day),\n                                        children: renderDayCellContent(day, dayEvents)\n                                    }, \"\".concat(weekIndex, \"-\").concat(dayIndex), false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 19\n                                    }, undefined);\n                                }))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 388,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 387,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 370,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarSideCard__WEBPACK_IMPORTED_MODULE_8__.CalendarSideCard, {\n                selectedDate: selectedDate,\n                events: events,\n                selectedEvent: selectedEvent,\n                setSelectedEvent: setSelectedEvent,\n                handleEventClick: handleEventClick\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 415,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 369,\n        columnNumber: 5\n    }, undefined);\n};\n_s2(MonthView, \"t9SuGQQKM9r/+gjBud8WIP3gfyg=\", false, function() {\n    return [\n        _providers_record__WEBPACK_IMPORTED_MODULE_5__.useMaybeRecord,\n        useMonthEvents\n    ];\n});\n_c1 = MonthView;\nfunction isMultiDay(event) {\n    return !(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(new Date(event.start), new Date(event.end));\n} // import React, { useMemo } from 'react';\n // import { format, startOfMonth, endOfMonth, startOfWeek, endOfWeek, addDays, isSameDay, isToday } from 'date-fns';\n // import { cn } from '@/lib/utils';\n // import { Button } from '@/components/ui/button';\n // import { PlusIcon } from '@heroicons/react/24/outline';\n // import { ScrollArea } from '@/components/ui/scroll-area';\n // import { CalendarEvent } from '@/typings/page';\n // import { useMaybeRecord } from '@/providers/record';\n // import { CalendarEventItem } from './CalendarEventItem';\n // import { NoEvents } from './NoEvents';\n // import { CalendarSideCard } from './CalendarSideCard';\n // import { useDroppable } from '@dnd-kit/core';\n // interface MonthViewProps {\n //   selectedDate: Date;\n //   events: CalendarEvent[];\n //   selectedEvent: string | null;\n //   setSelectedEvent: (id: string) => void;\n //   setSelectedDate: (date: Date) => void;\n //   openAddEventForm: (date: Date) => void;\n //   canEditData: boolean;\n //   handleEventClick: (event: CalendarEvent) => void;\n //   activeDragData: any;\n // }\n // const DayCell = ({\n //   date,\n //   children,\n //   onClick,\n //   isCurrentMonth\n // }: {\n //   date: Date;\n //   children: React.ReactNode;\n //   onClick: () => void;\n //   isCurrentMonth: boolean;\n // }) => {\n //   const { setNodeRef, isOver } = useDroppable({\n //     id: `daycell-${format(date, 'yyyy-MM-dd')}`,\n //     data: {\n //       date: date,\n //       type: 'daycell'\n //     }\n //   });\n //   return (\n //     <div\n //       ref={setNodeRef}\n //       onClick={onClick}\n //       className={cn(\n //         \"border-b border-r border-neutral-300 relative cursor-pointer transition-colors group flex flex-col p-2 min-h-[120px] lg:p-3 lg:min-h-[140px]\",\n //         isCurrentMonth\n //           ? \"bg-white hover:bg-neutral-50\"\n //           : \"bg-neutral-100 hover:bg-neutral-200\",\n //         isOver && \"bg-blue-50 border-blue-200\"\n //       )}\n //     >\n //       {children}\n //     </div>\n //   );\n // };\n // // Helper function to check if an event affects a specific day\n // const eventAffectsDay = (event: CalendarEvent, day: Date): boolean => {\n //   const eventStart = new Date(event.start);\n //   const eventEnd = new Date(event.end);\n //   const dayStart = new Date(day);\n //   const dayEnd = new Date(day);\n //   dayEnd.setHours(23, 59, 59, 999);\n //   return eventStart <= dayEnd && eventEnd >= dayStart;\n // };\n // // New helper function to process events for the entire month with proper slot tracking\n // const useMonthEvents = (weeks: Date[][], events: CalendarEvent[]) => {\n //   return useMemo(() => {\n //     const positionedEventsByWeek = new Map<number, any[]>();\n //     const slotUsageByDay = new Map<string, number>(); // Track slot usage per day\n //     weeks.forEach((week, weekIndex) => {\n //       const weekStart = week[0];\n //       const weekEnd = week[6];\n //       // Get all events that intersect with this week\n //       const weekEvents = events.filter(event => {\n //         const eventStart = new Date(event.start);\n //         const eventEnd = new Date(event.end);\n //         return eventStart <= weekEnd && eventEnd >= weekStart;\n //       });\n //       const spanningEvents: any[] = [];\n //       weekEvents.forEach(event => {\n //         const eventStart = new Date(event.start);\n //         const eventEnd = new Date(event.end);\n //         // Find which days this event spans in this week\n //         const startDayIndex = week.findIndex(day => isSameDay(day, eventStart));\n //         const endDayIndex = week.findIndex(day => isSameDay(day, eventEnd));\n //         // Calculate actual span within this week\n //         let actualStart = 0;\n //         let actualEnd = 6;\n //         if (startDayIndex !== -1) {\n //           actualStart = startDayIndex;\n //         }\n //         if (endDayIndex !== -1) {\n //           actualEnd = endDayIndex;\n //         }\n //         // Check if event intersects with this week\n //         const eventSpansWeek = startDayIndex !== -1 || endDayIndex !== -1 || (eventStart < weekStart && eventEnd > weekEnd);\n //         if (eventSpansWeek) {\n //           spanningEvents.push({\n //             event,\n //             startDayIndex: actualStart,\n //             endDayIndex: actualEnd,\n //             colSpan: actualEnd - actualStart + 1,\n //           });\n //         }\n //       });\n //       // Sort events by start day, then by span length (longer events first)\n //       const sortedEvents = spanningEvents.sort((a, b) => {\n //         if (a.startDayIndex !== b.startDayIndex) {\n //           return a.startDayIndex - b.startDayIndex;\n //         }\n //         return b.colSpan - a.colSpan;\n //       });\n //       // Position events and track slot usage\n //       const positioned: any[] = [];\n //       const rows: any[][] = [];\n //       sortedEvents.forEach(eventData => {\n //         // Check if this event can be placed (all days it spans have available slots)\n //         const affectedDays = week.slice(eventData.startDayIndex, eventData.endDayIndex + 1);\n //         const canPlace = affectedDays.every(day => {\n //           const dayKey = format(day, 'yyyy-MM-dd');\n //           const currentUsage = slotUsageByDay.get(dayKey) || 0;\n //           return currentUsage < 4; // Maximum 4 slots per day\n //         });\n //         if (!canPlace) {\n //           // Event cannot be placed, skip it (it will be in the \"+more\" count)\n //           return;\n //         }\n //         // Find available row\n //         let assigned = false;\n //         for (let i = 0; i < rows.length; i++) {\n //           const row = rows[i];\n //           const hasConflict = row.some(existingEvent => \n //             eventData.startDayIndex <= existingEvent.endDayIndex && \n //             eventData.endDayIndex >= existingEvent.startDayIndex\n //           );\n //           if (!hasConflict) {\n //             row.push(eventData);\n //             positioned.push({ ...eventData, row: i });\n //             assigned = true;\n //             break;\n //           }\n //         }\n //         if (!assigned) {\n //           rows.push([eventData]);\n //           positioned.push({ ...eventData, row: rows.length - 1 });\n //         }\n //         // Update slot usage for all affected days\n //         affectedDays.forEach(day => {\n //           const dayKey = format(day, 'yyyy-MM-dd');\n //           const currentUsage = slotUsageByDay.get(dayKey) || 0;\n //           slotUsageByDay.set(dayKey, currentUsage + 1);\n //         });\n //       });\n //       positionedEventsByWeek.set(weekIndex, positioned);\n //     });\n //     return { positionedEventsByWeek, slotUsageByDay };\n //   }, [weeks, events]);\n // };\n // export const MonthView: React.FC<MonthViewProps> = ({\n //   selectedDate,\n //   events,\n //   selectedEvent,\n //   setSelectedEvent,\n //   setSelectedDate,\n //   openAddEventForm,\n //   canEditData,\n //   handleEventClick,\n //   activeDragData,\n // }) => {\n //   const maybeRecord = useMaybeRecord();\n //   const isInRecordTab = !!maybeRecord;\n //   // Memoize month calculations\n //   const monthCalculations = useMemo(() => {\n //     const monthStart = startOfMonth(selectedDate);\n //     const monthEnd = endOfMonth(selectedDate);\n //     const startDay = startOfWeek(monthStart, { weekStartsOn: 0 });\n //     const endDay = endOfWeek(monthEnd, { weekStartsOn: 0 });\n //     const days = [];\n //     let day = startDay;\n //     while (day <= endDay) {\n //       days.push(day);\n //       day = addDays(day, 1);\n //     }\n //     const weeks = [];\n //     for (let i = 0; i < days.length; i += 7) {\n //       weeks.push(days.slice(i, i + 7));\n //     }\n //     return { monthStart, monthEnd, startDay, endDay, days, weeks };\n //   }, [selectedDate]);\n //   // Memoize month events\n //   const monthEvents = useMemo(() => \n //     events.filter(event => {\n //       const eventStart = new Date(event.start);\n //       const eventEnd = new Date(event.end);\n //       return eventStart <= monthCalculations.endDay && \n //              eventEnd >= monthCalculations.startDay;\n //     }), \n //     [events, monthCalculations.startDay, monthCalculations.endDay]\n //   );\n //   const { positionedEventsByWeek, slotUsageByDay } = useMonthEvents(monthCalculations.weeks, monthEvents);\n //   // Render empty state when no events\n //   const renderEmptyState = () => (\n //     <div className=\"flex flex-col h-full bg-background\">\n //       <div className=\"grid grid-cols-7 border-b border-neutral-300 bg-white\">\n //         {['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'].map(dayName => (\n //           <div key={dayName} className={cn(\n //             \"text-center font-semibold text-black\",\n //             \"py-2 text-xs\"\n //           )}>\n //             {dayName.substring(0, 3)}\n //           </div>\n //         ))}\n //       </div>\n //       <NoEvents\n //         title=\"No events this month\"\n //         message={`${format(selectedDate, 'MMMM yyyy')} is completely free. Start planning your month!`}\n //         showCreateButton={canEditData}\n //         onCreate={() => openAddEventForm(selectedDate)}\n //       />\n //     </div>\n //   );\n //   // Render day cell content\n //   const renderDayCellContent = (day: Date, dayEvents: any[]) => {\n //     const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n //     const isCurrentDay = isToday(day);\n //     const MAX_VISIBLE_EVENTS = 4;\n //     const ROW_HEIGHT = 28;\n //     const dayKey = format(day, 'yyyy-MM-dd');\n //     // Get all events that affect this day (for the +more count)\n //     const allDayEvents = monthEvents.filter(event => eventAffectsDay(event, day));\n //     const totalEventsForDay = allDayEvents.length;\n //     const sortedEvents = dayEvents.sort((a, b) => a.row - b.row);\n //     const visibleEvents = sortedEvents.slice(0, MAX_VISIBLE_EVENTS);\n //     const hasMore = totalEventsForDay > MAX_VISIBLE_EVENTS;\n //     const hiddenEventsCount = Math.max(0, totalEventsForDay - MAX_VISIBLE_EVENTS);\n //     // Calculate container height\n //     const maxRow = visibleEvents.reduce((max, event) => Math.max(max, event.row), -1);\n //     const containerHeight = hasMore\n //       ? (MAX_VISIBLE_EVENTS * ROW_HEIGHT) + 20 \n //       : (maxRow + 1) * ROW_HEIGHT;\n //     return (\n //       <>\n //         <div className=\"flex items-center justify-between mb-2\">\n //           <span className={cn(\n //             \"inline-flex items-center justify-center rounded-full font-semibold text-xs w-6 h-6\",\n //             isCurrentDay ? \"bg-black text-white\" : isCurrentMonth ? \"text-black hover:bg-neutral-100\" : \"text-neutral-400\"\n //           )}>\n //             {format(day, 'd')}\n //           </span>\n //           {canEditData && isCurrentMonth && (\n //             <Button\n //               variant=\"ghost\"\n //               className=\"rounded-full h-6 w-6 p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-neutral-100 hidden lg:inline-flex\"\n //               onClick={(e) => {\n //                 e.stopPropagation();\n //                 openAddEventForm(day);\n //               }}\n //             >\n //               <PlusIcon className=\"h-3 w-3 text-black\" />\n //             </Button>\n //           )}\n //         </div>\n //         <div className=\"relative\" style={{ height: `${containerHeight}px` }}>\n //           {visibleEvents.map(pe => (\n //             <div\n //               key={pe.event.id}\n //               className=\"absolute\"\n //               style={{\n //                 top: `${pe.row * ROW_HEIGHT}px`,\n //                 left: '2px',\n //                 width: pe.colSpan > 1 \n //                   ? `calc(${pe.colSpan * 100}% + ${(pe.colSpan - 1) * 19}px)`\n //                   : 'calc(100% - 4px)',\n //                 zIndex: 10 + pe.row,\n //               }}\n //             >\n //               <CalendarEventItem\n //                 event={pe.event}\n //                 view=\"month\"\n //                 onClick={(e) => {\n //                   e.stopPropagation();\n //                   setSelectedEvent(pe.event.id);\n //                   handleEventClick(pe.event);\n //                 }}\n //                 isDragging={activeDragData?.payload?.id === pe.event.id}\n //               />\n //             </div>\n //           ))}\n //           {hasMore && (\n //             <div \n //               className=\"text-black hover:text-black font-medium text-xs cursor-pointer\"\n //               style={{\n //                 position: 'absolute',\n //                 top: `${MAX_VISIBLE_EVENTS * ROW_HEIGHT}px`,\n //                 left: '2px',\n //               }}\n //               onClick={(e) => {\n //                 e.stopPropagation();\n //                 setSelectedDate(day);\n //               }}\n //             >\n //               + {hiddenEventsCount} more\n //             </div>\n //           )}\n //         </div>\n //       </>\n //     );\n //   };\n //   // Render main view\n //   return (\n //     <div className=\"h-full bg-background flex flex-col lg:flex-row\">\n //       <div className=\"flex-1 flex flex-col min-h-0\">\n //         {/* Day Headers */}\n //         <div \n //           data-day-headers=\"true\"\n //           className=\"grid grid-cols-7 border-b border-neutral-300 sticky top-0 z-10 bg-white\"\n //         >\n //           {['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'].map(dayName => (\n //             <div key={dayName} className={cn(\n //               \"text-center font-semibold text-black\",\n //               \"py-2 text-xs\"\n //             )}>\n //               {dayName.substring(0, 3)}\n //             </div>\n //           ))}\n //         </div>\n //         {/* Month Grid */}\n //         <ScrollArea className=\"flex-1\">\n //           <div className=\"grid grid-cols-7 border-neutral-300 border-b\">\n //             {monthCalculations.weeks.map((week, weekIndex) =>\n //               week.map((day, dayIndex) => {\n //                 const weekEvents = positionedEventsByWeek.get(weekIndex) || [];\n //                 const dayEvents = weekEvents.filter(pe => pe.startDayIndex === dayIndex);\n //                 const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n //                 return (\n //                   <DayCell\n //                     key={`${weekIndex}-${dayIndex}`}\n //                     date={day}\n //                     isCurrentMonth={isCurrentMonth}\n //                     onClick={() => setSelectedDate(day)}\n //                   >\n //                     {renderDayCellContent(day, dayEvents)}\n //                   </DayCell>\n //                 );\n //               }),\n //             )}\n //           </div>\n //         </ScrollArea>\n //       </div>\n //       <CalendarSideCard\n //         selectedDate={selectedDate}\n //         events={events}\n //         selectedEvent={selectedEvent}\n //         setSelectedEvent={setSelectedEvent}\n //         handleEventClick={handleEventClick}\n //       />\n //     </div>\n //   );\n // };\n // function isMultiDay(event: CalendarEvent): boolean {\n //   return !isSameDay(new Date(event.start), new Date(event.end));\n // }\nvar _c, _c1;\n$RefreshReg$(_c, \"DayCell\");\n$RefreshReg$(_c1, \"MonthView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx\n"));

/***/ })

});