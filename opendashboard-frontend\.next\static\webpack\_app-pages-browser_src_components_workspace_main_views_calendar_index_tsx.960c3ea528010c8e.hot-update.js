"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx":
/*!*******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/MonthView.tsx ***!
  \*******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MonthView: function() { return /* binding */ MonthView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CalendarEventItem */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx\");\n/* harmony import */ var _NoEvents__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./NoEvents */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/NoEvents.tsx\");\n/* harmony import */ var _CalendarSideCard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./CalendarSideCard */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarSideCard.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst DayCell = (param)=>{\n    let { date, children, onClick, isCurrentMonth } = param;\n    _s();\n    const { setNodeRef, isOver } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__.useDroppable)({\n        id: \"daycell-\".concat((0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(date, \"yyyy-MM-dd\")),\n        data: {\n            date: date,\n            type: \"daycell\"\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: setNodeRef,\n        onClick: onClick,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-b border-r border-neutral-300 relative cursor-pointer transition-colors group flex flex-col p-2 min-h-[120px] lg:p-3 lg:min-h-[140px]\", isCurrentMonth ? \"bg-white hover:bg-neutral-50\" : \"bg-neutral-100 hover:bg-neutral-200\", isOver && \"bg-blue-50 border-blue-200\"),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DayCell, \"DmJTTt6A5xWIX/faBiFge3FOLrw=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__.useDroppable\n    ];\n});\n_c = DayCell;\n// Helper function to check if an event affects a specific day\nconst eventAffectsDay = (event, day)=>{\n    const eventStart = new Date(event.start);\n    const eventEnd = new Date(event.end);\n    const dayStart = new Date(day);\n    const dayEnd = new Date(day);\n    dayEnd.setHours(23, 59, 59, 999);\n    return eventStart <= dayEnd && eventEnd >= dayStart;\n};\n// New helper function to process events for the entire month with proper slot tracking\nconst useMonthEvents = (weeks, events)=>{\n    _s1();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const positionedEventsByWeek = new Map();\n        const slotUsageByDay = new Map(); // Track slot usage per day\n        weeks.forEach((week, weekIndex)=>{\n            const weekStart = week[0];\n            const weekEnd = week[6];\n            // Get all events that intersect with this week\n            const weekEvents = events.filter((event)=>{\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                return eventStart <= weekEnd && eventEnd >= weekStart;\n            });\n            const spanningEvents = [];\n            weekEvents.forEach((event)=>{\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                // Find which days this event spans in this week\n                const startDayIndex = week.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(day, eventStart));\n                const endDayIndex = week.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(day, eventEnd));\n                // Calculate actual span within this week\n                let actualStart = 0;\n                let actualEnd = 6;\n                if (startDayIndex !== -1) {\n                    actualStart = startDayIndex;\n                }\n                if (endDayIndex !== -1) {\n                    actualEnd = endDayIndex;\n                }\n                // Check if event intersects with this week\n                const eventSpansWeek = startDayIndex !== -1 || endDayIndex !== -1 || eventStart < weekStart && eventEnd > weekEnd;\n                if (eventSpansWeek) {\n                    spanningEvents.push({\n                        event,\n                        startDayIndex: actualStart,\n                        endDayIndex: actualEnd,\n                        colSpan: actualEnd - actualStart + 1\n                    });\n                }\n            });\n            // Sort events by start day, then by span length (longer events first)\n            const sortedEvents = spanningEvents.sort((a, b)=>{\n                if (a.startDayIndex !== b.startDayIndex) {\n                    return a.startDayIndex - b.startDayIndex;\n                }\n                return b.colSpan - a.colSpan;\n            });\n            // Position events and track slot usage\n            const positioned = [];\n            const rows = [];\n            sortedEvents.forEach((eventData)=>{\n                // Check if this event can be placed (all days it spans have available slots)\n                const affectedDays = week.slice(eventData.startDayIndex, eventData.endDayIndex + 1);\n                const canPlace = affectedDays.every((day)=>{\n                    const dayKey = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(day, \"yyyy-MM-dd\");\n                    const currentUsage = slotUsageByDay.get(dayKey) || 0;\n                    return currentUsage < 4; // Maximum 4 slots per day\n                });\n                if (!canPlace) {\n                    // Event cannot be placed, skip it (it will be in the \"+more\" count)\n                    return;\n                }\n                // Find available row\n                let assigned = false;\n                for(let i = 0; i < rows.length; i++){\n                    const row = rows[i];\n                    const hasConflict = row.some((existingEvent)=>eventData.startDayIndex <= existingEvent.endDayIndex && eventData.endDayIndex >= existingEvent.startDayIndex);\n                    if (!hasConflict) {\n                        row.push(eventData);\n                        positioned.push({\n                            ...eventData,\n                            row: i\n                        });\n                        assigned = true;\n                        break;\n                    }\n                }\n                if (!assigned) {\n                    rows.push([\n                        eventData\n                    ]);\n                    positioned.push({\n                        ...eventData,\n                        row: rows.length - 1\n                    });\n                }\n                // Update slot usage for all affected days\n                affectedDays.forEach((day)=>{\n                    const dayKey = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(day, \"yyyy-MM-dd\");\n                    const currentUsage = slotUsageByDay.get(dayKey) || 0;\n                    slotUsageByDay.set(dayKey, currentUsage + 1);\n                });\n            });\n            positionedEventsByWeek.set(weekIndex, positioned);\n        });\n        return {\n            positionedEventsByWeek,\n            slotUsageByDay\n        };\n    }, [\n        weeks,\n        events\n    ]);\n};\n_s1(useMonthEvents, \"nwk+m61qLgjDVUp4IGV/072DDN4=\");\nconst MonthView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, setSelectedDate, openAddEventForm, canEditData, handleEventClick, activeDragData } = param;\n    _s2();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_5__.useMaybeRecord)();\n    const isInRecordTab = !!maybeRecord;\n    // Memoize month calculations\n    const monthCalculations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const monthStart = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(selectedDate);\n        const monthEnd = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(selectedDate);\n        const startDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(monthStart, {\n            weekStartsOn: 0\n        });\n        const endDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(monthEnd, {\n            weekStartsOn: 0\n        });\n        const days = [];\n        let day = startDay;\n        while(day <= endDay){\n            days.push(day);\n            day = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(day, 1);\n        }\n        const weeks = [];\n        for(let i = 0; i < days.length; i += 7){\n            weeks.push(days.slice(i, i + 7));\n        }\n        return {\n            monthStart,\n            monthEnd,\n            startDay,\n            endDay,\n            days,\n            weeks\n        };\n    }, [\n        selectedDate\n    ]);\n    // Memoize month events\n    const monthEvents = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>events.filter((event)=>{\n            const eventStart = new Date(event.start);\n            const eventEnd = new Date(event.end);\n            return eventStart <= monthCalculations.endDay && eventEnd >= monthCalculations.startDay;\n        }), [\n        events,\n        monthCalculations.startDay,\n        monthCalculations.endDay\n    ]);\n    const { positionedEventsByWeek, slotUsageByDay } = useMonthEvents(monthCalculations.weeks, monthEvents);\n    // Render empty state when no events\n    const renderEmptyState = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-7 border-b border-neutral-300 bg-white\",\n                    children: [\n                        \"Sunday\",\n                        \"Monday\",\n                        \"Tuesday\",\n                        \"Wednesday\",\n                        \"Thursday\",\n                        \"Friday\",\n                        \"Saturday\"\n                    ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-black\", \"py-2 text-xs\"),\n                            children: dayName.substring(0, 3)\n                        }, dayName, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NoEvents__WEBPACK_IMPORTED_MODULE_7__.NoEvents, {\n                    title: \"No events this month\",\n                    message: \"\".concat((0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate, \"MMMM yyyy\"), \" is completely free. Start planning your month!\"),\n                    showCreateButton: canEditData,\n                    onCreate: ()=>openAddEventForm(selectedDate)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n            lineNumber: 239,\n            columnNumber: 5\n        }, undefined);\n    // Render day cell content\n    const renderDayCellContent = (day, dayEvents)=>{\n        const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n        const isCurrentDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(day);\n        const MAX_VISIBLE_EVENTS = 4;\n        const ROW_HEIGHT = 28;\n        const dayKey = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(day, \"yyyy-MM-dd\");\n        // Get all events that affect this day (for the +more count)\n        const allDayEvents = monthEvents.filter((event)=>eventAffectsDay(event, day));\n        const totalEventsForDay = allDayEvents.length;\n        const sortedEvents = dayEvents.sort((a, b)=>a.row - b.row);\n        const visibleEvents = sortedEvents.slice(0, MAX_VISIBLE_EVENTS);\n        const hasMore = totalEventsForDay > MAX_VISIBLE_EVENTS;\n        const hiddenEventsCount = Math.max(0, totalEventsForDay - MAX_VISIBLE_EVENTS);\n        // Calculate container height\n        const maxRow = visibleEvents.reduce((max, event)=>Math.max(max, event.row), -1);\n        const containerHeight = hasMore ? MAX_VISIBLE_EVENTS * ROW_HEIGHT + 20 : (maxRow + 1) * ROW_HEIGHT;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center rounded-full font-semibold text-xs w-6 h-6\", isCurrentDay ? \"bg-black text-white\" : isCurrentMonth ? \"text-black hover:bg-neutral-100\" : \"text-neutral-400\"),\n                            children: (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(day, \"d\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, undefined),\n                        canEditData && isCurrentMonth && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            className: \"rounded-full h-6 w-6 p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-neutral-100 hidden lg:inline-flex\",\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                setTimeout(()=>openAddEventForm(day), 150);\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"h-3 w-3 text-black\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 286,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    style: {\n                        height: \"\".concat(containerHeight, \"px\")\n                    },\n                    children: [\n                        visibleEvents.map((pe)=>{\n                            var _activeDragData_payload;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute\",\n                                style: {\n                                    top: \"\".concat(pe.row * ROW_HEIGHT, \"px\"),\n                                    left: \"2px\",\n                                    width: pe.colSpan > 1 ? \"calc(\".concat(pe.colSpan * 100, \"% + \").concat((pe.colSpan - 1) * 19, \"px)\") : \"calc(100% - 4px)\",\n                                    zIndex: 10 + pe.row\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__.CalendarEventItem, {\n                                    event: pe.event,\n                                    view: \"month\",\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        setSelectedEvent(pe.event.id);\n                                        handleEventClick(pe.event);\n                                    },\n                                    isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === pe.event.id\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, pe.event.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 13\n                            }, undefined);\n                        }),\n                        hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-black hover:text-black font-medium text-xs cursor-pointer\",\n                            style: {\n                                position: \"absolute\",\n                                top: \"\".concat(MAX_VISIBLE_EVENTS * ROW_HEIGHT, \"px\"),\n                                left: \"2px\"\n                            },\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                setSelectedDate(day);\n                            },\n                            children: [\n                                \"+ \",\n                                hiddenEventsCount,\n                                \" more\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 307,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true);\n    };\n    // Render main view\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-background flex flex-col lg:flex-row\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col min-h-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        \"data-day-headers\": \"true\",\n                        className: \"grid grid-cols-7 border-b border-neutral-300 sticky top-0 z-10 bg-white\",\n                        children: [\n                            \"Sunday\",\n                            \"Monday\",\n                            \"Tuesday\",\n                            \"Wednesday\",\n                            \"Thursday\",\n                            \"Friday\",\n                            \"Saturday\"\n                        ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-black\", \"py-2 text-xs\"),\n                                children: dayName.substring(0, 3)\n                            }, dayName, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n                        className: \"flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-7 border-neutral-300 border-b\",\n                            children: monthCalculations.weeks.map((week, weekIndex)=>week.map((day, dayIndex)=>{\n                                    const weekEvents = positionedEventsByWeek.get(weekIndex) || [];\n                                    const dayEvents = weekEvents.filter((pe)=>pe.startDayIndex === dayIndex);\n                                    const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DayCell, {\n                                        date: day,\n                                        isCurrentMonth: isCurrentMonth,\n                                        onClick: ()=>setSelectedDate(day),\n                                        children: renderDayCellContent(day, dayEvents)\n                                    }, \"\".concat(weekIndex, \"-\").concat(dayIndex), false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 19\n                                    }, undefined);\n                                }))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 376,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 375,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 358,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarSideCard__WEBPACK_IMPORTED_MODULE_8__.CalendarSideCard, {\n                selectedDate: selectedDate,\n                events: events,\n                selectedEvent: selectedEvent,\n                setSelectedEvent: setSelectedEvent,\n                handleEventClick: handleEventClick\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 399,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 357,\n        columnNumber: 5\n    }, undefined);\n};\n_s2(MonthView, \"t9SuGQQKM9r/+gjBud8WIP3gfyg=\", false, function() {\n    return [\n        _providers_record__WEBPACK_IMPORTED_MODULE_5__.useMaybeRecord,\n        useMonthEvents\n    ];\n});\n_c1 = MonthView;\nfunction isMultiDay(event) {\n    return !(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(new Date(event.start), new Date(event.end));\n}\nvar _c, _c1;\n$RefreshReg$(_c, \"DayCell\");\n$RefreshReg$(_c1, \"MonthView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx\n"));

/***/ })

});