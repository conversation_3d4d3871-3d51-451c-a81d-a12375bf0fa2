/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/AllDayRow.tsx":
/*!*******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/AllDayRow.tsx ***!
  \*******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AllDayRow: function() { return /* binding */ AllDayRow; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,format,isSameDay,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,format,isSameDay,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,format,isSameDay,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,format,isSameDay,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CalendarEventSegment */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx\");\n/* harmony import */ var _CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_eventCollisionUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/eventCollisionUtils */ \"(app-pages-browser)/./src/utils/eventCollisionUtils.ts\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst AllDayRow = (param)=>{\n    let { selectedDate, segments, selectedEvent, setSelectedEvent, handleEventClick, canEditData, openAddEventForm, view, activeDragData } = param;\n    _s();\n    // Create individual droppable hooks for each possible day\n    // IMPORTANT: All hooks must be called before any conditional returns\n    const dayViewHook = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable)({\n        id: \"allday-\".concat((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(selectedDate, \"yyyy-MM-dd\")),\n        data: {\n            date: selectedDate,\n            type: \"allday-day\"\n        }\n    });\n    // Create hooks for week view days\n    const weekDay1 = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable)({\n        id: \"allday-\".concat((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        }), 0), \"yyyy-MM-dd\")),\n        data: {\n            date: (0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n                weekStartsOn: 0\n            }), 0),\n            type: \"allday-week\"\n        }\n    });\n    const weekDay2 = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable)({\n        id: \"allday-\".concat((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        }), 1), \"yyyy-MM-dd\")),\n        data: {\n            date: (0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n                weekStartsOn: 0\n            }), 1),\n            type: \"allday-week\"\n        }\n    });\n    const weekDay3 = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable)({\n        id: \"allday-\".concat((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        }), 2), \"yyyy-MM-dd\")),\n        data: {\n            date: (0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n                weekStartsOn: 0\n            }), 2),\n            type: \"allday-week\"\n        }\n    });\n    const weekDay4 = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable)({\n        id: \"allday-\".concat((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        }), 3), \"yyyy-MM-dd\")),\n        data: {\n            date: (0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n                weekStartsOn: 0\n            }), 3),\n            type: \"allday-week\"\n        }\n    });\n    const weekDay5 = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable)({\n        id: \"allday-\".concat((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        }), 4), \"yyyy-MM-dd\")),\n        data: {\n            date: (0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n                weekStartsOn: 0\n            }), 4),\n            type: \"allday-week\"\n        }\n    });\n    const weekDay6 = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable)({\n        id: \"allday-\".concat((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        }), 5), \"yyyy-MM-dd\")),\n        data: {\n            date: (0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n                weekStartsOn: 0\n            }), 5),\n            type: \"allday-week\"\n        }\n    });\n    const weekDay7 = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable)({\n        id: \"allday-\".concat((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        }), 6), \"yyyy-MM-dd\")),\n        data: {\n            date: (0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n                weekStartsOn: 0\n            }), 6),\n            type: \"allday-week\"\n        }\n    });\n    const weekViewHooks = [\n        weekDay1,\n        weekDay2,\n        weekDay3,\n        weekDay4,\n        weekDay5,\n        weekDay6,\n        weekDay7\n    ];\n    if (segments.length === 0 && !activeDragData) {\n        return null;\n    }\n    const renderDayView = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"border-b border-neutral-300 bg-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sticky left-0 bg-white z-10 w-14 lg:w-20 border-r border-neutral-300 flex items-start justify-end pr-4 pt-2 text-xs font-medium text-neutral-500\",\n                        children: \"All-day\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: dayViewHook.setNodeRef,\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-1 relative p-2 space-y-1\", dayViewHook.isOver && \"bg-blue-50\"),\n                        children: [\n                            segments.slice(0, 3).map((segment)=>{\n                                var _activeDragData_payload;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__.CalendarEventSegment, {\n                                    segment: segment,\n                                    style: {\n                                        height: \"24px\",\n                                        width: \"100%\"\n                                    },\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        setSelectedEvent(segment.originalEventId);\n                                        handleEventClick(segment.originalEvent);\n                                    },\n                                    view: \"day\",\n                                    isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === segment.id\n                                }, segment.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, undefined);\n                            }),\n                            segments.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-neutral-600 font-medium cursor-pointer hover:text-neutral-800\",\n                                children: [\n                                    \"+ \",\n                                    segments.length - 3,\n                                    \" more\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n            lineNumber: 117,\n            columnNumber: 5\n        }, undefined);\n    const renderWeekView = ()=>{\n        // Week view specific logic starts here\n        const weekDays = Array.from({\n            length: 7\n        }, (_, i)=>(0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n                weekStartsOn: 0\n            }), i));\n        const spanningEvents = (()=>{\n            const eventGroups = new Map();\n            segments.forEach((segment)=>{\n                if (segment.isMultiDay || segment.isAllDay) {\n                    const eventId = segment.originalEventId;\n                    if (!eventGroups.has(eventId)) eventGroups.set(eventId, []);\n                    eventGroups.get(eventId).push(segment);\n                }\n            });\n            const spanning = [];\n            eventGroups.forEach((eventSegments)=>{\n                eventSegments.sort((a, b)=>a.date.getTime() - b.date.getTime());\n                const firstSegmentInWeek = eventSegments[0];\n                const lastSegmentInWeek = eventSegments[eventSegments.length - 1];\n                const startDayIndex = weekDays.findIndex((day)=>(0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day, firstSegmentInWeek.date));\n                const endDayIndex = weekDays.findIndex((day)=>(0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day, lastSegmentInWeek.date));\n                if (startDayIndex >= 0 && endDayIndex >= 0) {\n                    spanning.push({\n                        segment: firstSegmentInWeek,\n                        startDayIndex,\n                        endDayIndex,\n                        colSpan: endDayIndex - startDayIndex + 1,\n                        isEndOfEvent: lastSegmentInWeek.isLastSegment\n                    });\n                }\n            });\n            return spanning;\n        })();\n        const positionedEvents = (()=>{\n            const positioned = [];\n            const rows = [];\n            const sortedEvents = [\n                ...spanningEvents\n            ].sort((a, b)=>a.startDayIndex - b.startDayIndex || b.colSpan - a.colSpan);\n            sortedEvents.forEach((event)=>{\n                let assigned = false;\n                for(let i = 0; i < rows.length; i++){\n                    const row = rows[i];\n                    if (row.every((rowEvent)=>event.startDayIndex > rowEvent.endDayIndex || event.endDayIndex < rowEvent.startDayIndex)) {\n                        row.push(event);\n                        positioned.push({\n                            ...event,\n                            row: i\n                        });\n                        assigned = true;\n                        break;\n                    }\n                }\n                if (!assigned) {\n                    rows.push([\n                        event\n                    ]);\n                    positioned.push({\n                        ...event,\n                        row: rows.length - 1\n                    });\n                }\n            });\n            return positioned;\n        })();\n        // Use the new layout calculator\n        const { visibleSegments, moreCount } = (0,_utils_eventCollisionUtils__WEBPACK_IMPORTED_MODULE_4__.calculateAllDayLayout)(positionedEvents.map((e)=>e.segment), 3);\n        const visibleEvents = positionedEvents.filter((p)=>visibleSegments.some((s)=>s.id === p.segment.id));\n        const hasMore = moreCount > 0;\n        const firstEventDayIndex = positionedEvents.length > 0 ? Math.min(...positionedEvents.map((e)=>e.startDayIndex)) : 0;\n        if (positionedEvents.length === 0) return null;\n        const maxRows = positionedEvents.length > 0 ? Math.max(...positionedEvents.map((e)=>e.row)) + 1 : 0;\n        const rowHeight = 28;\n        const displayRows = hasMore ? 3.5 : Math.max(1, maxRows);\n        const totalHeight = displayRows * rowHeight + 16;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            \"data-all-day-row\": \"true\",\n            className: \"border-b border-neutral-300 bg-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sticky left-0 bg-white z-10 w-14 lg:w-20 border-r border-neutral-300 flex items-center justify-end pr-4 pt-2 text-xs font-medium text-neutral-500\",\n                        children: \"All-day\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 relative p-2\",\n                        style: {\n                            height: \"\".concat(totalHeight, \"px\")\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-7 gap-1 h-full\",\n                            children: weekDays.map((day, dayIndex)=>{\n                                const hook = weekViewHooks[dayIndex];\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: hook.setNodeRef,\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative cursor-pointer hover:bg-neutral-50 rounded-sm transition-colors\", hook.isOver && \"bg-blue-50\"),\n                                    onDoubleClick: ()=>{\n                                        if (canEditData) {\n                                            const newDate = new Date(day);\n                                            newDate.setHours(9, 0, 0, 0);\n                                            openAddEventForm(newDate);\n                                        }\n                                    },\n                                    children: [\n                                        visibleEvents.filter((spanningEvent)=>spanningEvent.startDayIndex === dayIndex).map((spanningEvent)=>{\n                                            var _activeDragData_payload;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute z-10\",\n                                                style: {\n                                                    top: \"\".concat(spanningEvent.row * rowHeight + 2, \"px\"),\n                                                    left: \"0px\",\n                                                    width: \"calc(\".concat(spanningEvent.colSpan * 100, \"% + \").concat((spanningEvent.colSpan - 1) * 4, \"px)\"),\n                                                    height: \"24px\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__.CalendarEventSegment, {\n                                                    segment: spanningEvent.segment,\n                                                    isEndOfEvent: spanningEvent.isEndOfEvent,\n                                                    style: {\n                                                        height: \"24px\",\n                                                        width: \"100%\"\n                                                    },\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        setSelectedEvent(spanningEvent.segment.originalEventId);\n                                                        handleEventClick(spanningEvent.segment.originalEvent);\n                                                    },\n                                                    view: view,\n                                                    isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === spanningEvent.segment.id\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, spanningEvent.segment.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 23\n                                            }, undefined);\n                                        }),\n                                        hasMore && dayIndex === firstEventDayIndex && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute z-10 cursor-pointer text-xs text-gray-600 hover:underline\",\n                                            style: {\n                                                top: \"\".concat(3 * rowHeight + 2, \"px\"),\n                                                left: \"4px\",\n                                                right: \"4px\"\n                                            },\n                                            onClick: ()=>console.log(\"More clicked\"),\n                                            children: [\n                                                \"+\",\n                                                moreCount,\n                                                \" more\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, dayIndex, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                lineNumber: 234,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n            lineNumber: 230,\n            columnNumber: 5\n        }, undefined);\n    };\n    return view === \"day\" ? renderDayView() : renderWeekView();\n};\n_s(AllDayRow, \"7+zBFPGq0SU8Pt7cRj6jYFJX7rY=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable\n    ];\n});\n_c = AllDayRow;\nvar _c;\n$RefreshReg$(_c, \"AllDayRow\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/AllDayRow.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx":
/*!******************************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx ***!
  \******************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {



;
    // Wrapped in an IIFE to avoid polluting the global scope
    ;
    (function () {
        var _a, _b;
        // Legacy CSS implementations will `eval` browser code in a Node.js context
        // to extract CSS. For backwards compatibility, we need to check we're in a
        // browser context before continuing.
        if (typeof self !== 'undefined' &&
            // AMP / No-JS mode does not inject these helpers:
            '$RefreshHelpers$' in self) {
            // @ts-ignore __webpack_module__ is global
            var currentExports = module.exports;
            // @ts-ignore __webpack_module__ is global
            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;
            // This cannot happen in MainTemplate because the exports mismatch between
            // templating and execution.
            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);
            // A module can be accepted automatically based on its exports, e.g. when
            // it is a Refresh Boundary.
            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {
                // Save the previous exports signature on update so we can compare the boundary
                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)
                module.hot.dispose(function (data) {
                    data.prevSignature =
                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);
                });
                // Unconditionally accept an update to this module, we'll check if it's
                // still a Refresh Boundary later.
                // @ts-ignore importMeta is replaced in the loader
                module.hot.accept();
                // This field is set when the previous version of this module was a
                // Refresh Boundary, letting us know we need to check for invalidation or
                // enqueue an update.
                if (prevSignature !== null) {
                    // A boundary can become ineligible if its exports are incompatible
                    // with the previous exports.
                    //
                    // For example, if you add/remove/change exports, we'll want to
                    // re-execute the importing modules, and force those components to
                    // re-render. Similarly, if you convert a class component to a
                    // function, we want to invalidate the boundary.
                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {
                        module.hot.invalidate();
                    }
                    else {
                        self.$RefreshHelpers$.scheduleUpdate();
                    }
                }
            }
            else {
                // Since we just executed the code for the module, it's possible that the
                // new exports made it ineligible for being a boundary.
                // We only care about the case when we were _previously_ a boundary,
                // because we already accepted this update (accidental side effect).
                var isNoLongerABoundary = prevSignature !== null;
                if (isNoLongerABoundary) {
                    module.hot.invalidate();
                }
            }
        }
    })();


/***/ }),

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/DayView.tsx":
/*!*****************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/DayView.tsx ***!
  \*****************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DayView: function() { return /* binding */ DayView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=format,isToday,setHours!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=format,isToday,setHours!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=format,isToday,setHours!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/setHours/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CalendarEventSegment */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx\");\n/* harmony import */ var _CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _NoEvents__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./NoEvents */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/NoEvents.tsx\");\n/* harmony import */ var _utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/multiDayEventUtils */ \"(app-pages-browser)/./src/utils/multiDayEventUtils.ts\");\n/* harmony import */ var _utils_eventCollisionUtils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/eventCollisionUtils */ \"(app-pages-browser)/./src/utils/eventCollisionUtils.ts\");\n/* harmony import */ var _AllDayRow__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./AllDayRow */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/AllDayRow.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst TimeSlot = (param)=>{\n    let { hour, date, onDoubleClick, children } = param;\n    // Create 60 minute segments for precise dropping\n    const minuteSegments = Array.from({\n        length: 60\n    }, (_, i)=>i);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-1 relative min-h-[60px] cursor-pointer\"),\n        style: {\n            height: \"60px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex flex-col\",\n                children: minuteSegments.map((minute)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MinuteSegment, {\n                        date: date,\n                        hour: hour,\n                        minute: minute,\n                        style: {\n                            height: \"\".concat(100 / 60, \"%\"),\n                            top: \"\".concat(minute / 60 * 100, \"%\")\n                        },\n                        onDoubleClick: ()=>onDoubleClick(minute)\n                    }, minute, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, undefined);\n};\n_c = TimeSlot;\n// New component for minute-level droppable segments\nconst MinuteSegment = (param)=>{\n    let { date, hour, minute, style, onDoubleClick } = param;\n    _s();\n    const { setNodeRef, isOver } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__.useDroppable)({\n        id: \"timeslot-\".concat((0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(date, \"yyyy-MM-dd\"), \"-\").concat(hour, \"-\").concat(minute),\n        data: {\n            date: date,\n            hour,\n            minute,\n            type: \"timeslot-minute\"\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: setNodeRef,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"absolute w-full\", isOver && \"bg-blue-50\"),\n        style: style,\n        onDoubleClick: onDoubleClick\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n        lineNumber: 99,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MinuteSegment, \"DmJTTt6A5xWIX/faBiFge3FOLrw=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__.useDroppable\n    ];\n});\n_c1 = MinuteSegment;\nconst DayView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, openAddEventForm, canEditData, savedScrollTop, handleEventClick, activeDragData } = param;\n    _s1();\n    const hours = Array.from({\n        length: 24\n    }, (_, i)=>i);\n    // Memoize event segments to prevent unnecessary recalculations\n    const daySegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const allSegments = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.eventsToSegments)(events);\n        return (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.getSegmentsForDay)(allSegments, selectedDate);\n    }, [\n        events,\n        selectedDate\n    ]);\n    // Separate all-day and time-slot segments\n    const allDaySegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.getAllDaySegments)(daySegments), [\n        daySegments\n    ]);\n    const timeSlotSegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.getTimeSlotSegments)(daySegments), [\n        daySegments\n    ]);\n    // Calculate layout for overlapping segments\n    const { segmentLayouts } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return (0,_utils_eventCollisionUtils__WEBPACK_IMPORTED_MODULE_6__.calculateLayout)(timeSlotSegments);\n    }, [\n        timeSlotSegments\n    ]);\n    // Memoize current time position\n    const currentTimePosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate) ? {\n            hour: new Date().getHours(),\n            minutes: new Date().getMinutes()\n        } : null, [\n        selectedDate\n    ]);\n    // Render empty state when no events\n    const renderEmptyState = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NoEvents__WEBPACK_IMPORTED_MODULE_4__.NoEvents, {\n            title: \"No events scheduled\",\n            message: (0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate) ? \"You have a free day ahead! Add an event to get started.\" : \"\".concat((0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(selectedDate, \"EEEE, MMMM d\"), \" is completely free.\"),\n            showCreateButton: canEditData,\n            onCreate: ()=>{\n                const newDate = new Date(selectedDate);\n                newDate.setHours(9, 0, 0, 0);\n                openAddEventForm(newDate);\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n            lineNumber: 152,\n            columnNumber: 5\n        }, undefined);\n    // Render time slots with events\n    const renderTimeSlots = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 overflow-auto relative bg-white\",\n            id: \"day-view-container\",\n            children: [\n                hours.map((hour, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex border-b border-neutral-300 hover:bg-neutral-50 transition-colors\", i === hours.length - 1 && \"border-b-neutral-300\"),\n                        style: {\n                            height: \"60px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                \"data-time-labels\": \"true\",\n                                className: \"flex items-start justify-end pr-4 pt-2 text-xs font-medium text-black border-r border-neutral-300 sticky left-0 bg-white z-10 w-14 lg:w-20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs font-semibold\",\n                                            children: (0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(selectedDate, hour), \"h\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-black opacity-60\",\n                                            children: (0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(selectedDate, hour), \"a\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TimeSlot, {\n                                hour: hour,\n                                date: selectedDate,\n                                onDoubleClick: (minute)=>{\n                                    if (canEditData) {\n                                        const newDate = new Date(selectedDate);\n                                        newDate.setHours(hour, minute, 0, 0);\n                                        openAddEventForm(newDate);\n                                    }\n                                },\n                                children: segmentLayouts.map((layout)=>{\n                                    var _activeDragData_payload;\n                                    const segmentStart = layout.segment.startTime;\n                                    const isFirstHour = segmentStart.getHours() === hour;\n                                    if (!isFirstHour) return null;\n                                    const segmentHeight = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.getSegmentHeight)(layout.segment);\n                                    const topOffset = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.getSegmentTopOffset)(layout.segment);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__.CalendarEventSegment, {\n                                        segment: layout.segment,\n                                        style: {\n                                            height: \"\".concat(segmentHeight, \"px\"),\n                                            position: \"absolute\",\n                                            top: \"\".concat(topOffset, \"px\"),\n                                            left: \"\".concat(layout.left, \"%\"),\n                                            width: \"\".concat(layout.width, \"%\"),\n                                            zIndex: selectedEvent === layout.segment.originalEventId ? 20 : layout.zIndex,\n                                            paddingRight: \"2px\",\n                                            border: layout.hasOverlap ? \"1px solid white\" : \"none\"\n                                        },\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            const container = document.getElementById(\"day-view-container\");\n                                            if (container) {\n                                                savedScrollTop.current = container.scrollTop;\n                                            }\n                                            setSelectedEvent(layout.segment.originalEventId);\n                                            handleEventClick(layout.segment.originalEvent);\n                                        },\n                                        view: \"day\",\n                                        isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === layout.segment.id\n                                    }, layout.segment.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 17\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, i, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, undefined)),\n                currentTimePosition && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute flex items-center z-30 pointer-events-none left-14 lg:left-20\",\n                    style: {\n                        top: \"\".concat((currentTimePosition.hour + currentTimePosition.minutes / 60) * 60, \"px\"),\n                        right: \"4px\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-3 h-3 rounded-full bg-red-500 border-2 border-white shadow-lg -ml-1.5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 border-t-2 border-red-500 shadow-sm\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n            lineNumber: 168,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center bg-white border-b border-neutral-300 py-2 px-2 lg:px-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"font-semibold text-black mb-1 text-xs\",\n                        children: (0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(selectedDate, \"EEEE\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center font-medium text-xs w-6 h-6 rounded-full\", (0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate) ? \"bg-black text-white\" : \"text-black hover:bg-neutral-100\"),\n                        children: (0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(selectedDate, \"d\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                lineNumber: 265,\n                columnNumber: 7\n            }, undefined),\n            daySegments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AllDayRow__WEBPACK_IMPORTED_MODULE_7__.AllDayRow, {\n                selectedDate: selectedDate,\n                segments: allDaySegments,\n                selectedEvent: selectedEvent,\n                setSelectedEvent: setSelectedEvent,\n                handleEventClick: handleEventClick,\n                canEditData: canEditData,\n                openAddEventForm: openAddEventForm,\n                view: \"day\",\n                activeDragData: activeDragData\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                lineNumber: 281,\n                columnNumber: 9\n            }, undefined),\n            daySegments.length === 0 ? renderEmptyState() : renderTimeSlots()\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n        lineNumber: 263,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(DayView, \"rKQaW4qzJjohXAHqKTEXOPEYpLE=\");\n_c2 = DayView;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"TimeSlot\");\n$RefreshReg$(_c1, \"MinuteSegment\");\n$RefreshReg$(_c2, \"DayView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/DayView.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/WeekView.tsx":
/*!******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/WeekView.tsx ***!
  \******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WeekView: function() { return /* binding */ WeekView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/setHours/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CalendarEventSegment */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx\");\n/* harmony import */ var _CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _AllDayRow__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./AllDayRow */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/AllDayRow.tsx\");\n/* harmony import */ var _NoEvents__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./NoEvents */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/NoEvents.tsx\");\n/* harmony import */ var _utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/multiDayEventUtils */ \"(app-pages-browser)/./src/utils/multiDayEventUtils.ts\");\n/* harmony import */ var _utils_eventCollisionUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/eventCollisionUtils */ \"(app-pages-browser)/./src/utils/eventCollisionUtils.ts\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst TimeSlot = (param)=>{\n    let { day, hour, children, onDoubleClick } = param;\n    // Create 60 minute segments for precise dropping\n    const minuteSegments = Array.from({\n        length: 60\n    }, (_, i)=>i);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-1 border-r border-neutral-300 last:border-r-0 relative min-h-[60px] cursor-pointer\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex flex-col\",\n                children: minuteSegments.map((minute)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MinuteSegment, {\n                        day: day,\n                        hour: hour,\n                        minute: minute,\n                        style: {\n                            height: \"\".concat(100 / 60, \"%\"),\n                            top: \"\".concat(minute / 60 * 100, \"%\")\n                        },\n                        onDoubleClick: ()=>onDoubleClick(minute)\n                    }, minute, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, undefined);\n};\n_c = TimeSlot;\n// New component for minute-level droppable segments\nconst MinuteSegment = (param)=>{\n    let { day, hour, minute, style, onDoubleClick } = param;\n    _s();\n    const { setNodeRef, isOver } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__.useDroppable)({\n        id: \"timeslot-\".concat((0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day, \"yyyy-MM-dd\"), \"-\").concat(hour, \"-\").concat(minute),\n        data: {\n            date: day,\n            hour,\n            minute,\n            type: \"timeslot-minute\"\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: setNodeRef,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"absolute w-full\", isOver && \"bg-blue-50\"),\n        style: style,\n        onDoubleClick: onDoubleClick\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MinuteSegment, \"DmJTTt6A5xWIX/faBiFge3FOLrw=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__.useDroppable\n    ];\n});\n_c1 = MinuteSegment;\nconst WeekView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, setSelectedDate, openAddEventForm, canEditData, savedScrollTop, handleEventClick, activeDragData } = param;\n    _s1();\n    // Memoize week-related calculations\n    const weekCalculations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const weekStart = (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        });\n        const weekEnd = (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        });\n        const days = Array.from({\n            length: 7\n        }, (_, i)=>(0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(weekStart, i));\n        const todayIndex = days.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(day));\n        return {\n            weekStart,\n            weekEnd,\n            days,\n            todayIndex\n        };\n    }, [\n        selectedDate\n    ]);\n    const { days, todayIndex } = weekCalculations;\n    const hours = Array.from({\n        length: 24\n    }, (_, i)=>i);\n    // Memoize week segments\n    const weekSegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const allSegments = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.eventsToSegments)(events);\n        return (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getSegmentsForWeek)(allSegments, weekCalculations.weekStart, weekCalculations.weekEnd);\n    }, [\n        events,\n        weekCalculations.weekStart,\n        weekCalculations.weekEnd\n    ]);\n    // Separate all-day and time-slot segments\n    const allDaySegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getAllDaySegments)(weekSegments), [\n        weekSegments\n    ]);\n    const timeSlotSegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getTimeSlotSegments)(weekSegments), [\n        weekSegments\n    ]);\n    // Memoize current time position\n    const currentTimePosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>todayIndex !== -1 ? {\n            dayIndex: todayIndex,\n            hour: new Date().getHours(),\n            minutes: new Date().getMinutes()\n        } : null, [\n        todayIndex\n    ]);\n    // Helper to get event duration in minutes\n    const getEventDurationInMinutes = (event)=>{\n        const start = new Date(event.start);\n        const end = new Date(event.end);\n        return Math.max(20, (end.getTime() - start.getTime()) / (1000 * 60));\n    };\n    // Render empty state when no events\n    const renderEmptyState = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NoEvents__WEBPACK_IMPORTED_MODULE_5__.NoEvents, {\n            title: \"No events this week\",\n            message: \"Your week is completely free. Add some events to get organized!\",\n            showCreateButton: canEditData,\n            onCreate: ()=>openAddEventForm(selectedDate)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n            lineNumber: 176,\n            columnNumber: 5\n        }, undefined);\n    // Render time slots with events\n    const renderTimeSlots = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 relative bg-white border-b border-neutral-300 overflow-y-auto lg:overflow-auto\",\n            id: \"week-view-container\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative overflow-x-auto lg:overflow-x-visible\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col min-w-[700px] lg:min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: hours.map((hour, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex border-b border-neutral-300 hover:bg-neutral-50 transition-colors\", i === hours.length - 1 && \"border-b-neutral-300\"),\n                                    style: {\n                                        height: \"60px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            \"data-time-labels\": \"true\",\n                                            className: \"sticky left-0 flex items-start justify-end pr-4 pt-2 text-xs font-medium text-black border-r border-neutral-200 bg-white z-20 w-14 lg:w-20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs font-semibold\",\n                                                    children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(new Date(), hour), \"h a\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        days.map((day)=>{\n                                            const daySegments = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getSegmentsForDay)(timeSlotSegments, day);\n                                            const { segmentLayouts } = (0,_utils_eventCollisionUtils__WEBPACK_IMPORTED_MODULE_7__.calculateLayout)(daySegments);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TimeSlot, {\n                                                day: day,\n                                                hour: hour,\n                                                onDoubleClick: (minute)=>{\n                                                    if (canEditData) {\n                                                        const newDate = new Date(day);\n                                                        newDate.setHours(hour, minute, 0, 0);\n                                                        openAddEventForm(newDate);\n                                                    }\n                                                },\n                                                children: segmentLayouts.map((layout)=>{\n                                                    var _activeDragData_payload;\n                                                    const segmentStart = layout.segment.startTime;\n                                                    const isFirstHour = segmentStart.getHours() === hour;\n                                                    if (!isFirstHour) return null;\n                                                    const segmentHeight = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getSegmentHeight)(layout.segment);\n                                                    const topOffset = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getSegmentTopOffset)(layout.segment);\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__.CalendarEventSegment, {\n                                                        segment: layout.segment,\n                                                        style: {\n                                                            height: \"\".concat(segmentHeight, \"px\"),\n                                                            position: \"absolute\",\n                                                            top: \"\".concat(topOffset, \"px\"),\n                                                            left: \"\".concat(layout.left, \"%\"),\n                                                            width: \"\".concat(layout.width, \"%\"),\n                                                            zIndex: selectedEvent === layout.segment.originalEventId ? 20 : layout.zIndex,\n                                                            paddingRight: \"2px\",\n                                                            border: layout.hasOverlap ? \"1px solid white\" : \"none\"\n                                                        },\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            const container = document.getElementById(\"week-view-container\");\n                                                            if (container) {\n                                                                savedScrollTop.current = container.scrollTop;\n                                                            }\n                                                            setSelectedEvent(layout.segment.originalEventId);\n                                                            handleEventClick(layout.segment.originalEvent);\n                                                        },\n                                                        view: \"week\",\n                                                        isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === layout.segment.id\n                                                    }, layout.segment.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 27\n                                                    }, undefined);\n                                                })\n                                            }, \"\".concat(day.toISOString(), \"-\").concat(hour), false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 21\n                                            }, undefined);\n                                        })\n                                    ]\n                                }, hour, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, undefined),\n                        currentTimePosition && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-0 bottom-0 left-14 lg:left-20 right-0 pointer-events-none z-30\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative h-full w-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute flex items-center\",\n                                    style: {\n                                        top: \"\".concat((currentTimePosition.hour + currentTimePosition.minutes / 60) * 60, \"px\"),\n                                        left: \"\".concat(currentTimePosition.dayIndex / 7 * 100, \"%\"),\n                                        width: \"\".concat(1 / 7 * 100, \"%\")\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 rounded-full bg-red-500 border-2 border-white shadow-lg -ml-1.5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 border-t-2 border-red-500 shadow-sm\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n            lineNumber: 186,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                \"data-day-headers\": \"true\",\n                className: \"border-b border-neutral-300 bg-white sticky top-0 z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex overflow-x-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"sticky left-0 bg-white z-10 w-14 lg:w-20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-1 min-w-[calc(100vw-3.5rem)] lg:min-w-0\",\n                            children: days.map((day, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 text-center cursor-pointer py-3 px-0 lg:py-4\",\n                                    onClick: ()=>setSelectedDate(day),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-semibold text-black mb-1\", \"text-xs\"),\n                                            children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day, \"EEE\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center font-medium text-xs w-6 h-6 rounded-full\", (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(day) ? \"bg-black text-white\" : \"text-black hover:bg-neutral-100\"),\n                                            children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day, \"d\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                    lineNumber: 303,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 299,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AllDayRow__WEBPACK_IMPORTED_MODULE_4__.AllDayRow, {\n                selectedDate: selectedDate,\n                segments: allDaySegments,\n                selectedEvent: selectedEvent,\n                setSelectedEvent: setSelectedEvent,\n                handleEventClick: handleEventClick,\n                canEditData: canEditData,\n                openAddEventForm: openAddEventForm,\n                view: \"week\",\n                activeDragData: activeDragData\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 333,\n                columnNumber: 7\n            }, undefined),\n            weekSegments.length === 0 ? renderEmptyState() : renderTimeSlots()\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n        lineNumber: 297,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(WeekView, \"OfgQ1j/ZHWAQ0Q+mdykk0ntfinw=\");\n_c2 = WeekView;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"TimeSlot\");\n$RefreshReg$(_c1, \"MinuteSegment\");\n$RefreshReg$(_c2, \"WeekView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/WeekView.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/index.tsx":
/*!****************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/index.tsx ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CalendarView: function() { return /* binding */ CalendarView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! opendb-app-db-utils/lib/typings/db */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/typings/db.js\");\n/* harmony import */ var _components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/custom-ui/loader */ \"(app-pages-browser)/./src/components/custom-ui/loader.tsx\");\n/* harmony import */ var _providers_page__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/page */ \"(app-pages-browser)/./src/providers/page.tsx\");\n/* harmony import */ var _providers_shared__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/providers/shared */ \"(app-pages-browser)/./src/providers/shared.tsx\");\n/* harmony import */ var _providers_template__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/providers/template */ \"(app-pages-browser)/./src/providers/template.tsx\");\n/* harmony import */ var _providers_views__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/providers/views */ \"(app-pages-browser)/./src/providers/views.tsx\");\n/* harmony import */ var _components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/workspace/main/views/table */ \"(app-pages-browser)/./src/components/workspace/main/views/table/index.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/icons/FontAwesomeRegular */ \"(app-pages-browser)/./src/components/icons/FontAwesomeRegular.tsx\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/subWeeks/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/subMonths/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addWeeks/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addMonths/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _providers_screenSize__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/providers/screenSize */ \"(app-pages-browser)/./src/providers/screenSize.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _providers_stackedPeek__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/providers/stackedPeek */ \"(app-pages-browser)/./src/providers/stackedPeek.tsx\");\n/* harmony import */ var _components_DayView__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./components/DayView */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/DayView.tsx\");\n/* harmony import */ var _components_WeekView__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./components/WeekView */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/WeekView.tsx\");\n/* harmony import */ var _components_MonthView__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./components/MonthView */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx\");\n/* harmony import */ var _components_CalendarEventItem__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./components/CalendarEventItem */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx\");\n/* harmony import */ var _components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/workspace/main/views/form/components/element/linked */ \"(app-pages-browser)/./src/components/workspace/main/views/form/components/element/linked.tsx\");\n/* harmony import */ var _components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/custom-ui/customSelect */ \"(app-pages-browser)/./src/components/custom-ui/customSelect.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n/* harmony import */ var _components_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./components/CalendarEventSegment */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx\");\n/* harmony import */ var _components_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_26___default = /*#__PURE__*/__webpack_require__.n(_components_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_26__);\n/* harmony import */ var _barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=differenceInDays,differenceInMilliseconds!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/differenceInMilliseconds/index.js\");\n/* harmony import */ var _barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=differenceInDays,differenceInMilliseconds!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/differenceInDays/index.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Custom modifier to restrict dragging to the calendar main content area only\nconst restrictToCalendarContainer = (param)=>{\n    let { transform, draggingNodeRect, windowRect } = param;\n    if (!draggingNodeRect || !windowRect) {\n        return transform;\n    }\n    // Find the calendar main content container (the div that contains the views)\n    const calendarContainer = document.querySelector('[data-calendar-content=\"true\"]');\n    if (!calendarContainer) {\n        return transform;\n    }\n    const containerRect = calendarContainer.getBoundingClientRect();\n    // For month view, we need to account for the side card\n    // The side card is positioned on the right side of the calendar\n    const sideCard = document.querySelector('[data-side-card=\"true\"]');\n    let maxX = containerRect.right - draggingNodeRect.width;\n    if (sideCard) {\n        const sideCardRect = sideCard.getBoundingClientRect();\n        // Restrict dragging to not go past the side card\n        maxX = Math.min(maxX, sideCardRect.left - draggingNodeRect.width);\n    }\n    // Find header areas to prevent dragging over them\n    const timeLabels = document.querySelector('[data-time-labels=\"true\"]');\n    const dayHeaders = document.querySelector('[data-day-headers=\"true\"]');\n    const allDayRow = document.querySelector('[data-all-day-row=\"true\"]');\n    // Calculate the boundaries relative to the window\n    let minX = containerRect.left;\n    let minY = containerRect.top;\n    const maxY = containerRect.bottom - draggingNodeRect.height;\n    // Prevent dragging over time labels (left side in day/week view)\n    if (timeLabels) {\n        const timeLabelsRect = timeLabels.getBoundingClientRect();\n        minX = Math.max(minX, timeLabelsRect.right);\n    }\n    // Prevent dragging over day headers (top of calendar)\n    if (dayHeaders) {\n        const dayHeadersRect = dayHeaders.getBoundingClientRect();\n        minY = Math.max(minY, dayHeadersRect.bottom);\n    }\n    // Prevent dragging over all-day row (if present)\n    if (allDayRow) {\n        const allDayRowRect = allDayRow.getBoundingClientRect();\n        minY = Math.max(minY, allDayRowRect.bottom);\n    }\n    // Get current pointer position\n    const currentX = transform.x + draggingNodeRect.left;\n    const currentY = transform.y + draggingNodeRect.top;\n    // Constrain the position\n    const constrainedX = Math.min(Math.max(currentX, minX), maxX);\n    const constrainedY = Math.min(Math.max(currentY, minY), maxY);\n    return {\n        ...transform,\n        x: constrainedX - draggingNodeRect.left,\n        y: constrainedY - draggingNodeRect.top\n    };\n};\n// Custom hook to track previous value\nconst usePrevious = (value)=>{\n    _s();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        ref.current = value;\n    });\n    return ref.current;\n};\n_s(usePrevious, \"8uVE59eA/r6b92xF80p7sH8rXLk=\");\nconst CalendarView = (props)=>{\n    _s1();\n    const { databaseStore, members, workspace } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace)();\n    const { definition } = props;\n    const { accessLevel } = (0,_providers_page__WEBPACK_IMPORTED_MODULE_5__.usePage)();\n    const { isMobile } = (0,_providers_screenSize__WEBPACK_IMPORTED_MODULE_16__.useScreenSize)();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_17__.useMaybeRecord)();\n    const maybeShared = (0,_providers_shared__WEBPACK_IMPORTED_MODULE_6__.useMaybeShared)();\n    const maybeTemplate = (0,_providers_template__WEBPACK_IMPORTED_MODULE_7__.useMaybeTemplate)();\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [viewType, setViewType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"week\");\n    const [selectedEvent, setSelectedEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showSideCalendar, setShowSideCalendar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!isMobile);\n    const [activeDragData, setActiveDragData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const savedScrollTop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const pointerCoordinates = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        x: 0,\n        y: 0\n    });\n    const isInRecordTab = !!maybeRecord;\n    definition.filter = definition.filter || {\n        conditions: [],\n        match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Match.All\n    };\n    definition.sorts = definition.sorts || [];\n    const databaseId = definition.databaseId;\n    const database = databaseStore[definition.databaseId];\n    const isPublishedView = !!maybeShared;\n    const editable = !definition.lockContent && !isPublishedView && !!accessLevel;\n    let canEditStructure = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    let canEditData = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    const { createRecords, updateRecordValues, setPeekRecordId, peekRecordId, refreshDatabase, deleteRecords } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViews)();\n    const { sorts, filter, search } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewFiltering)();\n    const { selectedIds, setSelectedIds } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewSelection)();\n    const prevPeekRecordId = usePrevious(peekRecordId);\n    const { openRecord } = (0,_providers_stackedPeek__WEBPACK_IMPORTED_MODULE_18__.useStackedPeek)();\n    const sensors = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensors)((0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.PointerSensor, {\n        activationConstraint: {\n            distance: 8\n        }\n    }), (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.TouchSensor, {\n        activationConstraint: {\n            delay: 150,\n            tolerance: 5\n        }\n    }));\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const containerId = viewType === \"day\" ? \"day-view-container\" : \"week-view-container\";\n        const container = document.getElementById(containerId);\n        if (container) {\n            requestAnimationFrame(()=>{\n                container.scrollTop = savedScrollTop.current;\n            });\n        }\n    }, [\n        selectedEvent,\n        viewType\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setShowSideCalendar(!isMobile);\n    }, [\n        isMobile\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only refresh if the peek view was open and is now closed.\n        if (prevPeekRecordId && !peekRecordId) {\n            console.log(\"Peek view was closed, refreshing calendar data\");\n            refreshDatabase(definition.databaseId);\n        }\n    }, [\n        peekRecordId,\n        prevPeekRecordId,\n        definition.databaseId,\n        refreshDatabase\n    ]);\n    if (!database) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_4__.PageLoader, {\n        size: \"full\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n        lineNumber: 211,\n        columnNumber: 25\n    }, undefined);\n    const getEvents = ()=>{\n        var _workspace_workspaceMember, _database_database;\n        if (!database) return [];\n        const { rows } = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__.filterAndSortRecords)(database, members, databaseStore, definition.filter || {\n            match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Match.All,\n            conditions: []\n        }, filter, sorts.length ? sorts : definition.sorts || [], (workspace === null || workspace === void 0 ? void 0 : (_workspace_workspaceMember = workspace.workspaceMember) === null || _workspace_workspaceMember === void 0 ? void 0 : _workspace_workspaceMember.userId) || \"\", (database === null || database === void 0 ? void 0 : (_database_database = database.database) === null || _database_database === void 0 ? void 0 : _database_database.id) || \"\");\n        const filteredRows = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__.searchFilteredRecords)(search || \"\", rows);\n        const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getDatabaseTitleCol)(database.database);\n        return filteredRows.map((row)=>{\n            const startValue = row.processedRecord.processedRecordValues[definition.eventStartColumnId];\n            let startDate;\n            if (startValue && typeof startValue === \"string\") {\n                startDate = new Date(startValue);\n            } else {\n                startDate = new Date();\n            }\n            let endDate;\n            if (definition.eventEndColumnId) {\n                const endValue = row.processedRecord.processedRecordValues[definition.eventEndColumnId];\n                if (endValue && typeof endValue === \"string\") {\n                    endDate = new Date(endValue);\n                } else {\n                    endDate = new Date(startDate.getTime() + (definition.defaultDuration || 30) * 60000);\n                }\n            } else {\n                endDate = new Date(startDate.getTime() + (definition.defaultDuration || 30) * 60000);\n            }\n            const title = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getRecordTitle)(row.record, titleColOpts.titleColId, titleColOpts.defaultTitle, titleColOpts.isContacts);\n            return {\n                id: row.id,\n                title,\n                start: startDate,\n                end: endDate,\n                record: row.record,\n                processedRecord: row.processedRecord\n            };\n        });\n    };\n    const getFilteredEvents = ()=>{\n        const baseEvents = getEvents();\n        if (!searchTerm.trim()) {\n            return baseEvents;\n        }\n        return baseEvents.filter((event)=>{\n            const searchLower = searchTerm.toLowerCase();\n            return event.title.toLowerCase().includes(searchLower);\n        });\n    };\n    const onDragStart = (event)=>{\n        if (event.active.data.current) {\n            var _event_active_rect_current_translated, _event_active_rect_current, _event_active_rect_current_translated1, _event_active_rect_current_translated2;\n            const initialPointerY = pointerCoordinates.current.y;\n            var _event_active_rect_current_translated_top;\n            const initialEventTop = (_event_active_rect_current_translated_top = (_event_active_rect_current = event.active.rect.current) === null || _event_active_rect_current === void 0 ? void 0 : (_event_active_rect_current_translated = _event_active_rect_current.translated) === null || _event_active_rect_current_translated === void 0 ? void 0 : _event_active_rect_current_translated.top) !== null && _event_active_rect_current_translated_top !== void 0 ? _event_active_rect_current_translated_top : 0;\n            const grabOffsetY = initialPointerY - initialEventTop;\n            // Get the exact dimensions from the DOM element\n            // Handle both event and segment types\n            const { payload, type } = event.active.data.current;\n            const eventId = type === \"segment\" ? payload.originalEvent.id : payload.id;\n            const draggedElement = document.getElementById(\"event-\".concat(eventId));\n            const width = draggedElement ? draggedElement.offsetWidth : (_event_active_rect_current_translated1 = event.active.rect.current.translated) === null || _event_active_rect_current_translated1 === void 0 ? void 0 : _event_active_rect_current_translated1.width;\n            const height = draggedElement ? draggedElement.offsetHeight : (_event_active_rect_current_translated2 = event.active.rect.current.translated) === null || _event_active_rect_current_translated2 === void 0 ? void 0 : _event_active_rect_current_translated2.height;\n            setActiveDragData({\n                ...event.active.data.current,\n                grabOffsetY,\n                width,\n                height\n            });\n            document.addEventListener(\"mousemove\", handleMouseMove);\n            document.addEventListener(\"touchmove\", handleTouchMove);\n        }\n    };\n    const onDragEnd = async (param)=>{\n        let { active, over } = param;\n        document.removeEventListener(\"mousemove\", handleMouseMove);\n        document.removeEventListener(\"touchmove\", handleTouchMove);\n        setActiveDragData(null);\n        if (!over || !active || !canEditData || active.id === over.id) {\n            return;\n        }\n        const activeData = active.data.current;\n        const overData = over.data.current;\n        if (!activeData || !overData) {\n            return;\n        }\n        const { payload, type } = activeData;\n        const eventToUpdate = type === \"segment\" ? payload.originalEvent : payload;\n        const originalStart = new Date(eventToUpdate.start);\n        const originalEnd = new Date(eventToUpdate.end);\n        const duration = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_27__[\"default\"])(originalEnd, originalStart);\n        let newStart;\n        if (overData.type.startsWith(\"allday\")) {\n            const dayDifference = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_28__[\"default\"])(overData.date, (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_29__[\"default\"])(originalStart));\n            newStart = (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(originalStart, dayDifference);\n            newStart.setHours(0, 0, 0, 0);\n        } else if (overData.type === \"timeslot-minute\") {\n            // Handle precise minute-based drops\n            newStart = new Date(overData.date);\n            newStart.setHours(overData.hour, overData.minute, 0, 0);\n        } else if (overData.type === \"daycell\") {\n            const dayDifference = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_28__[\"default\"])(overData.date, (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_29__[\"default\"])(originalStart));\n            newStart = (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(originalStart, dayDifference);\n        } else {\n            return;\n        }\n        const newEnd = new Date(newStart.getTime() + duration);\n        const recordId = eventToUpdate.record.id;\n        const newValues = {\n            [definition.eventStartColumnId]: newStart.toISOString(),\n            ...definition.eventEndColumnId && {\n                [definition.eventEndColumnId]: newEnd.toISOString()\n            }\n        };\n        try {\n            await updateRecordValues(definition.databaseId, [\n                recordId\n            ], newValues);\n            sonner__WEBPACK_IMPORTED_MODULE_14__.toast.success('Event \"'.concat(eventToUpdate.title, '\" updated.'));\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Failed to update event.\");\n            console.error(\"Error updating event:\", error);\n        }\n    };\n    const handleMouseMove = (event)=>{\n        pointerCoordinates.current = {\n            x: event.clientX,\n            y: event.clientY\n        };\n    };\n    const handleTouchMove = (event)=>{\n        const touch = event.touches[0];\n        pointerCoordinates.current = {\n            x: touch.clientX,\n            y: touch.clientY\n        };\n    };\n    const events = getFilteredEvents();\n    const goToToday = ()=>setSelectedDate(new Date());\n    const goToPrevious = ()=>{\n        switch(viewType){\n            case \"day\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(prevDate, -1));\n                break;\n            case \"week\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_31__[\"default\"])(prevDate, 1));\n                break;\n            case \"month\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_32__[\"default\"])(prevDate, 1));\n                break;\n        }\n    };\n    const goToNext = ()=>{\n        switch(viewType){\n            case \"day\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(prevDate, 1));\n                break;\n            case \"week\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_33__[\"default\"])(prevDate, 1));\n                break;\n            case \"month\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_34__[\"default\"])(prevDate, 1));\n                break;\n        }\n    };\n    const handleRequestCreateEvent = function(date) {\n        let useSystemTime = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (useSystemTime) {\n            const now = new Date();\n            const newDate = new Date(date);\n            newDate.setHours(now.getHours(), now.getMinutes(), now.getSeconds(), now.getMilliseconds());\n            handleCreateEvent(newDate);\n        } else {\n            handleCreateEvent(date);\n        }\n    };\n    const handleCreateEvent = async function() {\n        let date = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : new Date();\n        if (!canEditData) return;\n        const startTime = new Date(date);\n        const endTime = new Date(startTime.getTime() + (definition.defaultDuration || 30) * 60000);\n        const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getDatabaseTitleCol)(database.database);\n        try {\n            const recordValues = {\n                [definition.eventStartColumnId]: startTime.toISOString(),\n                ...definition.eventEndColumnId && {\n                    [definition.eventEndColumnId]: endTime.toISOString()\n                }\n            };\n            if (titleColOpts.titleColId) {\n                recordValues[titleColOpts.titleColId] = \"New Event\";\n            }\n            const result = await createRecords(definition.databaseId, [\n                recordValues\n            ]);\n            if (result && result.records && result.records.length > 0) {\n                const newRecordId = result.records[0].id;\n                if (newRecordId) {\n                    await refreshDatabase(definition.databaseId);\n                    setPeekRecordId(newRecordId);\n                    sonner__WEBPACK_IMPORTED_MODULE_14__.toast.success(\"New event created\");\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Error accessing the new event\");\n                }\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Failed to create event properly\");\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Failed to create event\");\n        }\n    };\n    const handleEventClick = (event)=>{\n        if (event && event.id) {\n            const containerId = viewType === \"day\" ? \"day-view-container\" : \"week-view-container\";\n            const container = document.getElementById(containerId);\n            if (container) {\n                savedScrollTop.current = container.scrollTop;\n            }\n            openRecord(event.id, event.record.databaseId);\n            setSelectedEvent(event.id);\n        }\n    };\n    const getHeaderDateDisplay = ()=>{\n        switch(viewType){\n            case \"day\":\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(selectedDate, \"MMMM d, yyyy\");\n            case \"week\":\n                return \"\".concat((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(selectedDate, -selectedDate.getDay()), \"MMM d\"), \" - \").concat((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(selectedDate, 6 - selectedDate.getDay()), \"MMM d, yyyy\"));\n            case \"month\":\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(selectedDate, \"MMMM yyyy\");\n            default:\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(selectedDate, \"MMMM d, yyyy\");\n        }\n    };\n    const handleEventDelete = async (event)=>{\n        if (!canEditData) return;\n        try {\n            await deleteRecords(database.database.id, [\n                event.record.id\n            ]);\n        } catch (error) {\n            console.error(\"Error deleting event:\", error);\n        }\n    };\n    const viewTypeOptions = [\n        {\n            id: \"day\",\n            value: \"day\",\n            title: \"Day\",\n            data: \"day\"\n        },\n        {\n            id: \"week\",\n            value: \"week\",\n            title: \"Week\",\n            data: \"week\"\n        },\n        {\n            id: \"month\",\n            value: \"month\",\n            title: \"Month\",\n            data: \"month\"\n        }\n    ];\n    const selectedViewOption = viewType === \"day\" ? [\n        \"day\"\n    ] : viewType === \"week\" ? [\n        \"week\"\n    ] : [\n        \"month\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"border-b border-neutral-300 bg-white\", isInRecordTab && \"py-1\"),\n                children: [\n                    isMobile ? /* Mobile Header Layout */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"p-2\", isInRecordTab && \"py-1\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xs font-semibold text-black truncate flex-1 mr-2\",\n                                        children: getHeaderDateDisplay()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: ()=>setShowSideCalendar(!showSideCalendar),\n                                        className: \"rounded-full h-8 px-3 text-xs text-black hover:bg-gray-50\",\n                                        children: \"Calendar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 509,\n                                columnNumber: 12\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToToday,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs text-black hover:bg-gray-50\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                children: \"Today\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        variant: \"ghost\",\n                                                        onClick: goToPrevious,\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleLeftIcon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 20\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 18\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        variant: \"ghost\",\n                                                        onClick: goToNext,\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleRightIcon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 20\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 18\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 16\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__.CustomSelect, {\n                                                options: viewTypeOptions,\n                                                selectedIds: selectedViewOption,\n                                                onChange: (selected)=>{\n                                                    if (selected.length > 0) {\n                                                        setViewType(selected[0]);\n                                                    }\n                                                },\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"px-3 text-xs border-neutral-300 rounded-full bg-white hover:bg-gray-50 text-black w-20\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                placeholder: \"View\",\n                                                hideSearch: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                onClick: ()=>handleRequestCreateEvent(selectedDate, true),\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs border border-neutral-300 bg-white hover:bg-gray-50 text-black\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.PlusIcon, {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 584,\n                                                    columnNumber: 20\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 577,\n                                                columnNumber: 18\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 558,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 522,\n                                columnNumber: 12\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 508,\n                        columnNumber: 10\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"flex items-center justify-between px-4\", isInRecordTab ? \"py-1\" : \"py-2\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: goToToday,\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs text-black hover:bg-gray-50\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                        children: \"Today\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 596,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToPrevious,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleLeftIcon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 615,\n                                                    columnNumber: 16\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 14\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToNext,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleRightIcon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 625,\n                                                    columnNumber: 16\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 617,\n                                                columnNumber: 14\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 606,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xs font-semibold text-black\",\n                                        children: getHeaderDateDisplay()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 629,\n                                        columnNumber: 12\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 595,\n                                columnNumber: 10\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                                placeholder: \"Search events...\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"w-48 pr-8 h-8 text-xs border-neutral-300 bg-transparent shadow-none\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 637,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.MagnifyingGlassIcon, {\n                                                className: \"h-3 w-3 absolute right-2 top-1/2 transform -translate-y-1/2 text-neutral-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 643,\n                                                columnNumber: 16\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 636,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__.CustomSelect, {\n                                        options: viewTypeOptions,\n                                        selectedIds: selectedViewOption,\n                                        onChange: (selected)=>{\n                                            if (selected.length > 0) {\n                                                setViewType(selected[0]);\n                                            }\n                                        },\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"px-3 text-xs border-neutral-300 rounded-full bg-white hover:bg-gray-50 text-black\", isInRecordTab ? \"h-6 w-20\" : \"h-8 w-28\"),\n                                        placeholder: \"View\",\n                                        hideSearch: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 647,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        onClick: ()=>handleRequestCreateEvent(selectedDate, true),\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs border border-neutral-300 bg-white hover:bg-gray-50 text-black gap-1\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.PlusIcon, {\n                                                className: \"h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 671,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Add Event\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 672,\n                                                columnNumber: 35\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 664,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 634,\n                                columnNumber: 10\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 591,\n                        columnNumber: 8\n                    }, undefined),\n                    isMobile && !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-2 pb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                    placeholder: \"Search events...\",\n                                    value: searchTerm,\n                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                    className: \"w-full pr-8 h-8 text-xs border-neutral-300 bg-transparent shadow-none\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 682,\n                                    columnNumber: 12\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.MagnifyingGlassIcon, {\n                                    className: \"h-3 w-3 absolute right-2 top-1/2 transform -translate-y-1/2 text-neutral-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 688,\n                                    columnNumber: 12\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                            lineNumber: 681,\n                            columnNumber: 10\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 680,\n                        columnNumber: 8\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                lineNumber: 502,\n                columnNumber: 6\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex min-h-0\",\n                children: [\n                    showSideCalendar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"flex-none bg-white\", isMobile ? \"w-full absolute z-50 backdrop-blur-sm h-full shadow-lg\" : \"w-fit border-r border-neutral-300\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center p-2 border-b border-neutral-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xs font-semibold text-black\",\n                                        children: \"Calendar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 702,\n                                        columnNumber: 10\n                                    }, undefined),\n                                    isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: ()=>setShowSideCalendar(false),\n                                        className: \"rounded-full h-8 w-8 p-1 text-black hover:bg-neutral-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Close\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 709,\n                                                columnNumber: 14\n                                            }, undefined),\n                                            \"\\xd7\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 704,\n                                        columnNumber: 12\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 701,\n                                columnNumber: 8\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                mode: \"single\",\n                                selected: selectedDate,\n                                onSelect: (date)=>{\n                                    if (date) {\n                                        setSelectedDate(date);\n                                        if (isMobile) {\n                                            setShowSideCalendar(false);\n                                        }\n                                    }\n                                },\n                                className: \"rounded-md border-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 714,\n                                columnNumber: 8\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 697,\n                        columnNumber: 6\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.DndContext, {\n                        sensors: sensors,\n                        onDragStart: onDragStart,\n                        onDragEnd: onDragEnd,\n                        modifiers: [\n                            restrictToCalendarContainer\n                        ],\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                \"data-calendar-content\": \"true\",\n                                children: [\n                                    viewType === \"day\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DayView__WEBPACK_IMPORTED_MODULE_19__.DayView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        openAddEventForm: handleRequestCreateEvent,\n                                        canEditData: canEditData,\n                                        savedScrollTop: savedScrollTop,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 738,\n                                        columnNumber: 7\n                                    }, undefined),\n                                    viewType === \"week\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_WeekView__WEBPACK_IMPORTED_MODULE_20__.WeekView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        setSelectedDate: setSelectedDate,\n                                        openAddEventForm: handleRequestCreateEvent,\n                                        canEditData: canEditData,\n                                        savedScrollTop: savedScrollTop,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 751,\n                                        columnNumber: 7\n                                    }, undefined),\n                                    viewType === \"month\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MonthView__WEBPACK_IMPORTED_MODULE_21__.MonthView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        setSelectedDate: setSelectedDate,\n                                        openAddEventForm: (date)=>handleRequestCreateEvent(date, true),\n                                        canEditData: canEditData,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 765,\n                                        columnNumber: 7\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 736,\n                                columnNumber: 6\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.DragOverlay, {\n                                dropAnimation: null,\n                                children: activeDragData && activeDragData.type === \"segment\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_26__.CalendarEventSegment, {\n                                    segment: activeDragData.payload,\n                                    view: viewType === \"day\" ? \"day\" : \"week\",\n                                    onClick: ()=>{},\n                                    style: {\n                                        width: activeDragData.width,\n                                        height: activeDragData.height\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 781,\n                                    columnNumber: 11\n                                }, undefined) : activeDragData && activeDragData.type === \"event\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CalendarEventItem__WEBPACK_IMPORTED_MODULE_22__.CalendarEventItem, {\n                                    event: activeDragData.payload,\n                                    view: viewType,\n                                    onClick: ()=>{},\n                                    style: {\n                                        width: activeDragData.width,\n                                        height: activeDragData.height\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 791,\n                                    columnNumber: 11\n                                }, undefined) : null\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 779,\n                                columnNumber: 6\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 730,\n                        columnNumber: 6\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                lineNumber: 695,\n                columnNumber: 4\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n        lineNumber: 501,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(CalendarView, \"gC+3ORgbOSOWj17lR2zBDE1vWrs=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace,\n        _providers_page__WEBPACK_IMPORTED_MODULE_5__.usePage,\n        _providers_screenSize__WEBPACK_IMPORTED_MODULE_16__.useScreenSize,\n        _providers_record__WEBPACK_IMPORTED_MODULE_17__.useMaybeRecord,\n        _providers_shared__WEBPACK_IMPORTED_MODULE_6__.useMaybeShared,\n        _providers_template__WEBPACK_IMPORTED_MODULE_7__.useMaybeTemplate,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViews,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewFiltering,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewSelection,\n        usePrevious,\n        _providers_stackedPeek__WEBPACK_IMPORTED_MODULE_18__.useStackedPeek,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensors\n    ];\n});\n_c = CalendarView;\nvar _c;\n$RefreshReg$(_c, \"CalendarView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/index.tsx\n"));

/***/ })

});