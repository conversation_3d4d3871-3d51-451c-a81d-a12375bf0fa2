/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/WeekView.tsx":
/*!******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/WeekView.tsx ***!
  \******************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// import React, { useMemo } from 'react';\n// import { format, startOfWeek, endOfWeek, isToday, isSameDay, addDays, setHours } from 'date-fns';\n// import { cn } from '@/lib/utils';\n// import { Button } from '@/components/ui/button';\n// import { CalendarEvent } from '@/typings/page';\n// import { CalendarEventItem } from './CalendarEventItem';\n// import { CalendarEventSegment } from './CalendarEventSegment';\n// import { AllDayRow } from './AllDayRow';\n// import { NoEvents } from './NoEvents';\n// import { eventsToSegments, \n//   getSegmentsForWeek, \n//   getSegmentsForDay, \n//   getAllDaySegments, \n//   getTimeSlotSegments, \n//   getSegmentHeight, \n//   getSegmentTopOffset,\n//   EventSegment\n// } from '@/utils/multiDayEventUtils';\n// import { calculateLayout } from '@/utils/eventCollisionUtils';\n// import { useDroppable } from '@dnd-kit/core';\n// import { MoreIndicator } from './MoreIndicator';\n// interface WeekViewProps {\n//   selectedDate: Date;\n//   events: CalendarEvent[];\n//   selectedEvent: string | null;\n//   setSelectedEvent: (id: string) => void;\n//   setSelectedDate: (date: Date) => void;\n//   openAddEventForm: (date: Date) => void;\n//   canEditData: boolean;\n//   savedScrollTop: React.MutableRefObject<number>;\n//   handleEventClick: (event: CalendarEvent) => void;\n//   activeDragData: any;\n// }\n// const TimeSlot = ({\n//   day,\n//   hour,\n//   children,\n//   onDoubleClick\n// }: {\n//   day: Date;\n//   hour: number;\n//   children: React.ReactNode;\n//   onDoubleClick: (minute: number) => void;\n// }) => {\n//   // Create 60 minute segments for precise dropping\n//   const minuteSegments = Array.from({ length: 60 }, (_, i) => i);\n//   return (\n//     <div\n//       className={cn(\n//         \"flex-1 border-r border-neutral-300 last:border-r-0 relative min-h-[60px] cursor-pointer\",\n//       )}\n//     >\n//       {/* Render invisible minute segments */}\n//       <div className=\"absolute inset-0 flex flex-col\">\n//         {minuteSegments.map((minute) => (\n//           <MinuteSegment\n//             key={minute}\n//             day={day}\n//             hour={hour}\n//             minute={minute}\n//             style={{\n//               height: `${100/60}%`,  // Each segment takes 1/60th of the hour cell\n//               top: `${(minute/60) * 100}%`\n//             }}\n//             onDoubleClick={() => onDoubleClick(minute)}\n//           />\n//         ))}\n//       </div>\n//       {children}\n//     </div>\n//   );\n// };\n// // New component for minute-level droppable segments\n// const MinuteSegment = ({\n//   day,\n//   hour,\n//   minute,\n//   style,\n//   onDoubleClick\n// }: {\n//   day: Date;\n//   hour: number;\n//   minute: number;\n//   style: React.CSSProperties;\n//   onDoubleClick: () => void;\n// }) => {\n//   const { setNodeRef, isOver } = useDroppable({\n//     id: `timeslot-${format(day, 'yyyy-MM-dd')}-${hour}-${minute}`,\n//     data: {\n//       date: day,\n//       hour,\n//       minute,\n//       type: 'timeslot-minute'\n//     }\n//   });\n//   return (\n//     <div\n//       ref={setNodeRef}\n//       className={cn(\n//         \"absolute w-full\",\n//         isOver && \"bg-blue-50\"\n//       )}\n//       style={style}\n//       onDoubleClick={onDoubleClick}\n//     />\n//   );\n// };\n// export const WeekView: React.FC<WeekViewProps> = ({\n//   selectedDate,\n//   events,\n//   selectedEvent,\n//   setSelectedEvent,\n//   setSelectedDate,\n//   openAddEventForm,\n//   canEditData,\n//   savedScrollTop,\n//   handleEventClick,\n//   activeDragData,\n// }) => {\n//   // Memoize week-related calculations\n//   const weekCalculations = useMemo(() => {\n//     const weekStart = startOfWeek(selectedDate, { weekStartsOn: 0 });\n//     const weekEnd = endOfWeek(selectedDate, { weekStartsOn: 0 });\n//     const days = Array.from({ length: 7 }, (_, i) => addDays(weekStart, i));\n//     const todayIndex = days.findIndex(day => isToday(day));\n//     return { \n//       weekStart, \n//       weekEnd, \n//       days, \n//       todayIndex \n//     };\n//   }, [selectedDate]);\n//   const { days, todayIndex } = weekCalculations;\n//   const hours = Array.from({ length: 24 }, (_, i) => i);\n//   // Memoize week segments\n//   const weekSegments = useMemo(() => {\n//     const allSegments = eventsToSegments(events);\n//     return getSegmentsForWeek(allSegments, weekCalculations.weekStart, weekCalculations.weekEnd);\n//   }, [events, weekCalculations.weekStart, weekCalculations.weekEnd]);\n//   // Separate all-day and time-slot segments\n//   const allDaySegments = useMemo(() => getAllDaySegments(weekSegments), [weekSegments]);\n//   const timeSlotSegments = useMemo(() => getTimeSlotSegments(weekSegments), [weekSegments]);\n//   // Memoize current time position\n//   const currentTimePosition = useMemo(() => \n//     todayIndex !== -1 \n//       ? {\n//           dayIndex: todayIndex,\n//           hour: new Date().getHours(),\n//           minutes: new Date().getMinutes()\n//         } \n//       : null, \n//     [todayIndex]\n//   );\n//   // Helper to get event duration in minutes\n//   const getEventDurationInMinutes = (event: CalendarEvent): number => {\n//     const start = new Date(event.start);\n//     const end = new Date(event.end);\n//     return Math.max(20, (end.getTime() - start.getTime()) / (1000 * 60));\n//   };\n//   // Render empty state when no events\n//   const renderEmptyState = () => (\n//     <NoEvents\n//       title=\"No events this week\"\n//       message=\"Your week is completely free. Add some events to get organized!\"\n//       showCreateButton={canEditData}\n//       onCreate={() => openAddEventForm(selectedDate)}\n//     />\n//   );\n//   // Render time slots with events\n//   const renderTimeSlots = () => (\n//     <div className=\"flex-1 relative bg-white border-b border-neutral-300 overflow-y-auto lg:overflow-auto\" id=\"week-view-container\">\n//       <div className=\"relative overflow-x-auto lg:overflow-x-visible\">\n//         <div className=\"flex flex-col min-w-[700px] lg:min-w-0\">\n//           <div className=\"relative\">\n//             {hours.map((hour, i) => (\n//               <div\n//                 key={hour}\n//                 className={cn(\n//                   \"flex border-b border-neutral-300 hover:bg-neutral-50 transition-colors\",\n//                   i === hours.length - 1 && \"border-b-neutral-300\"\n//                 )}\n//                 style={{ height: '60px' }}\n//               >\n//                 {/* Time Label */}\n//                 <div \n//                   data-time-labels=\"true\"\n//                   className=\"sticky left-0 flex items-start justify-end pr-4 pt-2 text-xs font-medium text-black border-r border-neutral-200 bg-white z-20 w-14 lg:w-20\"\n//                 >\n//                   <div className=\"text-right\">\n//                     <div className=\"text-xs font-semibold\">\n//                       {format(setHours(new Date(), hour), 'h a')}\n//                     </div>\n//                   </div>\n//                 </div>\n//                 {/* Time Slots */}\n//                 {days.map((day) => {\n//                   const daySegments = getSegmentsForDay(timeSlotSegments, day);\n//                   const { segmentLayouts } = calculateLayout(daySegments);\n//                   return (\n//                     <TimeSlot\n//                       key={`${day.toISOString()}-${hour}`}\n//                       day={day}\n//                       hour={hour}\n//                       onDoubleClick={(minute) => {\n//                         if (canEditData) {\n//                           const newDate = new Date(day);\n//                           newDate.setHours(hour, minute, 0, 0);\n//                           openAddEventForm(newDate);\n//                         }\n//                       }}\n//                     >\n//                       {segmentLayouts.map((layout) => {\n//                         const segmentStart = layout.segment.startTime;\n//                         const isFirstHour = segmentStart.getHours() === hour;\n//                         if (!isFirstHour) return null;\n//                         const segmentHeight = getSegmentHeight(layout.segment);\n//                         const topOffset = getSegmentTopOffset(layout.segment);\n//                         return (\n//                           <CalendarEventSegment\n//                             key={layout.segment.id}\n//                             segment={layout.segment}\n//                             style={{\n//                               height: `${segmentHeight}px`,\n//                               position: 'absolute',\n//                               top: `${topOffset}px`,\n//                               left: `${layout.left}%`,\n//                               width: `${layout.width}%`,\n//                               zIndex: selectedEvent === layout.segment.originalEventId ? 20 : layout.zIndex,\n//                               paddingRight: '2px',\n//                               border: layout.hasOverlap ? '1px solid white' : 'none',\n//                             }}\n//                             onClick={(e) => {\n//                               e.stopPropagation();\n//                               const container = document.getElementById('week-view-container');\n//                               if (container) {\n//                                 savedScrollTop.current = container.scrollTop;\n//                               }\n//                               setSelectedEvent(layout.segment.originalEventId);\n//                               handleEventClick(layout.segment.originalEvent);\n//                             }}\n//                             view=\"week\"\n//                             isDragging={activeDragData?.payload?.id === layout.segment.id}\n//                           />\n//                         );\n//                       })}\n//                     </TimeSlot>\n//                   );\n//                 })}\n//               </div>\n//             ))}\n//           </div>\n//           {/* Current Time Indicator */}\n//           {currentTimePosition && (\n//             <div className=\"absolute top-0 bottom-0 left-14 lg:left-20 right-0 pointer-events-none z-30\">\n//               <div className=\"relative h-full w-full\">\n//                 <div\n//                   className=\"absolute flex items-center\"\n//                   style={{\n//                     top: `${(currentTimePosition.hour + currentTimePosition.minutes / 60) * 60}px`,\n//                     left: `${(currentTimePosition.dayIndex / 7) * 100}%`,\n//                     width: `${(1 / 7) * 100}%`,\n//                   }}\n//                 >\n//                   <div className=\"w-3 h-3 rounded-full bg-red-500 border-2 border-white shadow-lg -ml-1.5\" />\n//                   <div className=\"flex-1 border-t-2 border-red-500 shadow-sm\" />\n//                 </div>\n//               </div>\n//             </div>\n//           )}\n//         </div>\n//       </div>\n//     </div>\n//   );\n//   return (\n//     <div className=\"h-full flex flex-col bg-white\">\n//       {/* Header */}\n//       <div \n//         data-day-headers=\"true\"\n//         className=\"border-b border-neutral-300 bg-white sticky top-0 z-20\"\n//       >\n//         <div className=\"flex overflow-x-hidden\">\n//           <div className=\"sticky left-0 bg-white z-10 w-14 lg:w-20\"></div>\n//           <div className=\"flex flex-1 min-w-[calc(100vw-3.5rem)] lg:min-w-0\">\n//             {days.map((day, i) => (\n//               <div\n//                 key={i}\n//                 className=\"flex-1 text-center cursor-pointer py-3 px-0 lg:py-4\"\n//                 onClick={() => setSelectedDate(day)}\n//               >\n//                 <div className={cn(\n//                   \"font-semibold text-black mb-1\",\n//                   \"text-xs\"\n//                 )}>\n//                   {format(day, 'EEE')}\n//                 </div>\n//                 <div className={cn(\n//                   \"inline-flex items-center justify-center font-medium text-xs w-6 h-6 rounded-full\",\n//                   isToday(day)\n//                     ? \"bg-black text-white\"\n//                     : \"text-black hover:bg-neutral-100\"\n//                 )}>\n//                   {format(day, 'd')}\n//                 </div>\n//               </div>\n//             ))}\n//           </div>\n//         </div>\n//       </div>\n//       {/* All-Day Row */}\n//       <AllDayRow\n//         selectedDate={selectedDate}\n//         segments={allDaySegments}\n//         selectedEvent={selectedEvent}\n//         setSelectedEvent={setSelectedEvent}\n//         handleEventClick={handleEventClick}\n//         canEditData={canEditData}\n//         openAddEventForm={openAddEventForm}\n//         view=\"week\"\n//         activeDragData={activeDragData}\n//       />\n//       {/* Main Content */}\n//       {weekSegments.length === 0 \n//         ? renderEmptyState() \n//         : renderTimeSlots()}\n//     </div>\n//   );\n// };\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/WeekView.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/index.tsx":
/*!****************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/index.tsx ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CalendarView: function() { return /* binding */ CalendarView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! opendb-app-db-utils/lib/typings/db */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/typings/db.js\");\n/* harmony import */ var _components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/custom-ui/loader */ \"(app-pages-browser)/./src/components/custom-ui/loader.tsx\");\n/* harmony import */ var _providers_page__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/page */ \"(app-pages-browser)/./src/providers/page.tsx\");\n/* harmony import */ var _providers_shared__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/providers/shared */ \"(app-pages-browser)/./src/providers/shared.tsx\");\n/* harmony import */ var _providers_template__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/providers/template */ \"(app-pages-browser)/./src/providers/template.tsx\");\n/* harmony import */ var _providers_views__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/providers/views */ \"(app-pages-browser)/./src/providers/views.tsx\");\n/* harmony import */ var _components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/workspace/main/views/table */ \"(app-pages-browser)/./src/components/workspace/main/views/table/index.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/icons/FontAwesomeRegular */ \"(app-pages-browser)/./src/components/icons/FontAwesomeRegular.tsx\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/subWeeks/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/subMonths/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addWeeks/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addMonths/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _providers_screenSize__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/providers/screenSize */ \"(app-pages-browser)/./src/providers/screenSize.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _providers_stackedPeek__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/providers/stackedPeek */ \"(app-pages-browser)/./src/providers/stackedPeek.tsx\");\n/* harmony import */ var _components_DayView__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./components/DayView */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/DayView.tsx\");\n/* harmony import */ var _components_WeekView__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./components/WeekView */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/WeekView.tsx\");\n/* harmony import */ var _components_WeekView__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(_components_WeekView__WEBPACK_IMPORTED_MODULE_20__);\n/* harmony import */ var _components_MonthView__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./components/MonthView */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx\");\n/* harmony import */ var _components_MonthView__WEBPACK_IMPORTED_MODULE_21___default = /*#__PURE__*/__webpack_require__.n(_components_MonthView__WEBPACK_IMPORTED_MODULE_21__);\n/* harmony import */ var _components_CalendarEventItem__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./components/CalendarEventItem */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx\");\n/* harmony import */ var _components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/workspace/main/views/form/components/element/linked */ \"(app-pages-browser)/./src/components/workspace/main/views/form/components/element/linked.tsx\");\n/* harmony import */ var _components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/custom-ui/customSelect */ \"(app-pages-browser)/./src/components/custom-ui/customSelect.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n/* harmony import */ var _components_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./components/CalendarEventSegment */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx\");\n/* harmony import */ var _barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=differenceInDays,differenceInMilliseconds!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/differenceInMilliseconds/index.js\");\n/* harmony import */ var _barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=differenceInDays,differenceInMilliseconds!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/differenceInDays/index.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Custom modifier to restrict dragging to the calendar main content area only\nconst restrictToCalendarContainer = (param)=>{\n    let { transform, draggingNodeRect, windowRect } = param;\n    if (!draggingNodeRect || !windowRect) {\n        return transform;\n    }\n    // Find the calendar main content container (the div that contains the views)\n    const calendarContainer = document.querySelector('[data-calendar-content=\"true\"]');\n    if (!calendarContainer) {\n        return transform;\n    }\n    const containerRect = calendarContainer.getBoundingClientRect();\n    // For month view, we need to account for the side card\n    // The side card is positioned on the right side of the calendar\n    const sideCard = document.querySelector('[data-side-card=\"true\"]');\n    let maxX = containerRect.right - draggingNodeRect.width;\n    if (sideCard) {\n        const sideCardRect = sideCard.getBoundingClientRect();\n        // Restrict dragging to not go past the side card\n        maxX = Math.min(maxX, sideCardRect.left - draggingNodeRect.width);\n    }\n    // Find header areas to prevent dragging over them\n    const timeLabels = document.querySelector('[data-time-labels=\"true\"]');\n    const dayHeaders = document.querySelector('[data-day-headers=\"true\"]');\n    const allDayRow = document.querySelector('[data-all-day-row=\"true\"]');\n    // Calculate the boundaries relative to the window\n    let minX = containerRect.left;\n    let minY = containerRect.top;\n    const maxY = containerRect.bottom - draggingNodeRect.height;\n    // Prevent dragging over time labels (left side in day/week view)\n    if (timeLabels) {\n        const timeLabelsRect = timeLabels.getBoundingClientRect();\n        minX = Math.max(minX, timeLabelsRect.right);\n    }\n    // Prevent dragging over day headers (top of calendar)\n    if (dayHeaders) {\n        const dayHeadersRect = dayHeaders.getBoundingClientRect();\n        minY = Math.max(minY, dayHeadersRect.bottom);\n    }\n    // Prevent dragging over all-day row (if present)\n    if (allDayRow) {\n        const allDayRowRect = allDayRow.getBoundingClientRect();\n        minY = Math.max(minY, allDayRowRect.bottom);\n    }\n    // Get current pointer position\n    const currentX = transform.x + draggingNodeRect.left;\n    const currentY = transform.y + draggingNodeRect.top;\n    // Constrain the position\n    const constrainedX = Math.min(Math.max(currentX, minX), maxX);\n    const constrainedY = Math.min(Math.max(currentY, minY), maxY);\n    return {\n        ...transform,\n        x: constrainedX - draggingNodeRect.left,\n        y: constrainedY - draggingNodeRect.top\n    };\n};\n// Custom hook to track previous value\nconst usePrevious = (value)=>{\n    _s();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        ref.current = value;\n    });\n    return ref.current;\n};\n_s(usePrevious, \"8uVE59eA/r6b92xF80p7sH8rXLk=\");\nconst CalendarView = (props)=>{\n    _s1();\n    const { databaseStore, members, workspace } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace)();\n    const { definition } = props;\n    const { accessLevel } = (0,_providers_page__WEBPACK_IMPORTED_MODULE_5__.usePage)();\n    const { isMobile } = (0,_providers_screenSize__WEBPACK_IMPORTED_MODULE_16__.useScreenSize)();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_17__.useMaybeRecord)();\n    const maybeShared = (0,_providers_shared__WEBPACK_IMPORTED_MODULE_6__.useMaybeShared)();\n    const maybeTemplate = (0,_providers_template__WEBPACK_IMPORTED_MODULE_7__.useMaybeTemplate)();\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [viewType, setViewType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"week\");\n    const [selectedEvent, setSelectedEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showSideCalendar, setShowSideCalendar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!isMobile);\n    const [activeDragData, setActiveDragData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const savedScrollTop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const pointerCoordinates = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        x: 0,\n        y: 0\n    });\n    const isInRecordTab = !!maybeRecord;\n    definition.filter = definition.filter || {\n        conditions: [],\n        match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Match.All\n    };\n    definition.sorts = definition.sorts || [];\n    const databaseId = definition.databaseId;\n    const database = databaseStore[definition.databaseId];\n    const isPublishedView = !!maybeShared;\n    const editable = !definition.lockContent && !isPublishedView && !!accessLevel;\n    let canEditStructure = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    let canEditData = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    const { createRecords, updateRecordValues, setPeekRecordId, peekRecordId, refreshDatabase, deleteRecords } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViews)();\n    const { sorts, filter, search } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewFiltering)();\n    const { selectedIds, setSelectedIds } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewSelection)();\n    const prevPeekRecordId = usePrevious(peekRecordId);\n    const { openRecord } = (0,_providers_stackedPeek__WEBPACK_IMPORTED_MODULE_18__.useStackedPeek)();\n    const sensors = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensors)((0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.PointerSensor, {\n        activationConstraint: {\n            distance: 8\n        }\n    }), (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.TouchSensor, {\n        activationConstraint: {\n            delay: 150,\n            tolerance: 5\n        }\n    }));\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const containerId = viewType === \"day\" ? \"day-view-container\" : \"week-view-container\";\n        const container = document.getElementById(containerId);\n        if (container) {\n            requestAnimationFrame(()=>{\n                container.scrollTop = savedScrollTop.current;\n            });\n        }\n    }, [\n        selectedEvent,\n        viewType\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setShowSideCalendar(!isMobile);\n    }, [\n        isMobile\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only refresh if the peek view was open and is now closed.\n        if (prevPeekRecordId && !peekRecordId) {\n            console.log(\"Peek view was closed, refreshing calendar data\");\n            refreshDatabase(definition.databaseId);\n        }\n    }, [\n        peekRecordId,\n        prevPeekRecordId,\n        definition.databaseId,\n        refreshDatabase\n    ]);\n    if (!database) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_4__.PageLoader, {\n        size: \"full\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n        lineNumber: 211,\n        columnNumber: 25\n    }, undefined);\n    const getEvents = ()=>{\n        var _workspace_workspaceMember, _database_database;\n        if (!database) return [];\n        const { rows } = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__.filterAndSortRecords)(database, members, databaseStore, definition.filter || {\n            match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Match.All,\n            conditions: []\n        }, filter, sorts.length ? sorts : definition.sorts || [], (workspace === null || workspace === void 0 ? void 0 : (_workspace_workspaceMember = workspace.workspaceMember) === null || _workspace_workspaceMember === void 0 ? void 0 : _workspace_workspaceMember.userId) || \"\", (database === null || database === void 0 ? void 0 : (_database_database = database.database) === null || _database_database === void 0 ? void 0 : _database_database.id) || \"\");\n        const filteredRows = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__.searchFilteredRecords)(search || \"\", rows);\n        const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getDatabaseTitleCol)(database.database);\n        return filteredRows.map((row)=>{\n            const startValue = row.processedRecord.processedRecordValues[definition.eventStartColumnId];\n            let startDate;\n            if (startValue && typeof startValue === \"string\") {\n                startDate = new Date(startValue);\n            } else {\n                startDate = new Date();\n            }\n            let endDate;\n            if (definition.eventEndColumnId) {\n                const endValue = row.processedRecord.processedRecordValues[definition.eventEndColumnId];\n                if (endValue && typeof endValue === \"string\") {\n                    endDate = new Date(endValue);\n                } else {\n                    endDate = new Date(startDate.getTime() + (definition.defaultDuration || 30) * 60000);\n                }\n            } else {\n                endDate = new Date(startDate.getTime() + (definition.defaultDuration || 30) * 60000);\n            }\n            const title = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getRecordTitle)(row.record, titleColOpts.titleColId, titleColOpts.defaultTitle, titleColOpts.isContacts);\n            return {\n                id: row.id,\n                title,\n                start: startDate,\n                end: endDate,\n                record: row.record,\n                processedRecord: row.processedRecord\n            };\n        });\n    };\n    const getFilteredEvents = ()=>{\n        const baseEvents = getEvents();\n        if (!searchTerm.trim()) {\n            return baseEvents;\n        }\n        return baseEvents.filter((event)=>{\n            const searchLower = searchTerm.toLowerCase();\n            return event.title.toLowerCase().includes(searchLower);\n        });\n    };\n    const onDragStart = (event)=>{\n        if (event.active.data.current) {\n            var _event_active_rect_current_translated, _event_active_rect_current, _event_active_rect_current_translated1, _event_active_rect_current_translated2;\n            const initialPointerY = pointerCoordinates.current.y;\n            var _event_active_rect_current_translated_top;\n            const initialEventTop = (_event_active_rect_current_translated_top = (_event_active_rect_current = event.active.rect.current) === null || _event_active_rect_current === void 0 ? void 0 : (_event_active_rect_current_translated = _event_active_rect_current.translated) === null || _event_active_rect_current_translated === void 0 ? void 0 : _event_active_rect_current_translated.top) !== null && _event_active_rect_current_translated_top !== void 0 ? _event_active_rect_current_translated_top : 0;\n            const grabOffsetY = initialPointerY - initialEventTop;\n            // Get the exact dimensions from the DOM element\n            // Handle both event and segment types\n            const { payload, type } = event.active.data.current;\n            const eventId = type === \"segment\" ? payload.originalEvent.id : payload.id;\n            const draggedElement = document.getElementById(\"event-\".concat(eventId));\n            const width = draggedElement ? draggedElement.offsetWidth : (_event_active_rect_current_translated1 = event.active.rect.current.translated) === null || _event_active_rect_current_translated1 === void 0 ? void 0 : _event_active_rect_current_translated1.width;\n            const height = draggedElement ? draggedElement.offsetHeight : (_event_active_rect_current_translated2 = event.active.rect.current.translated) === null || _event_active_rect_current_translated2 === void 0 ? void 0 : _event_active_rect_current_translated2.height;\n            setActiveDragData({\n                ...event.active.data.current,\n                grabOffsetY,\n                width,\n                height\n            });\n            document.addEventListener(\"mousemove\", handleMouseMove);\n            document.addEventListener(\"touchmove\", handleTouchMove);\n        }\n    };\n    const onDragEnd = async (param)=>{\n        let { active, over } = param;\n        document.removeEventListener(\"mousemove\", handleMouseMove);\n        document.removeEventListener(\"touchmove\", handleTouchMove);\n        setActiveDragData(null);\n        if (!over || !active || !canEditData || active.id === over.id) {\n            return;\n        }\n        const activeData = active.data.current;\n        const overData = over.data.current;\n        if (!activeData || !overData) {\n            return;\n        }\n        const { payload, type } = activeData;\n        const eventToUpdate = type === \"segment\" ? payload.originalEvent : payload;\n        const originalStart = new Date(eventToUpdate.start);\n        const originalEnd = new Date(eventToUpdate.end);\n        const duration = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_27__[\"default\"])(originalEnd, originalStart);\n        let newStart;\n        if (overData.type.startsWith(\"allday\")) {\n            const dayDifference = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_28__[\"default\"])(overData.date, (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_29__[\"default\"])(originalStart));\n            newStart = (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(originalStart, dayDifference);\n            newStart.setHours(0, 0, 0, 0);\n        } else if (overData.type === \"timeslot-minute\") {\n            // Handle precise minute-based drops\n            newStart = new Date(overData.date);\n            newStart.setHours(overData.hour, overData.minute, 0, 0);\n        } else if (overData.type === \"daycell\") {\n            const dayDifference = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_28__[\"default\"])(overData.date, (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_29__[\"default\"])(originalStart));\n            newStart = (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(originalStart, dayDifference);\n        } else {\n            return;\n        }\n        const newEnd = new Date(newStart.getTime() + duration);\n        const recordId = eventToUpdate.record.id;\n        const newValues = {\n            [definition.eventStartColumnId]: newStart.toISOString(),\n            ...definition.eventEndColumnId && {\n                [definition.eventEndColumnId]: newEnd.toISOString()\n            }\n        };\n        try {\n            await updateRecordValues(definition.databaseId, [\n                recordId\n            ], newValues);\n            sonner__WEBPACK_IMPORTED_MODULE_14__.toast.success('Event \"'.concat(eventToUpdate.title, '\" updated.'));\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Failed to update event.\");\n            console.error(\"Error updating event:\", error);\n        }\n    };\n    const handleMouseMove = (event)=>{\n        pointerCoordinates.current = {\n            x: event.clientX,\n            y: event.clientY\n        };\n    };\n    const handleTouchMove = (event)=>{\n        const touch = event.touches[0];\n        pointerCoordinates.current = {\n            x: touch.clientX,\n            y: touch.clientY\n        };\n    };\n    const events = getFilteredEvents();\n    const goToToday = ()=>setSelectedDate(new Date());\n    const goToPrevious = ()=>{\n        switch(viewType){\n            case \"day\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(prevDate, -1));\n                break;\n            case \"week\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_31__[\"default\"])(prevDate, 1));\n                break;\n            case \"month\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_32__[\"default\"])(prevDate, 1));\n                break;\n        }\n    };\n    const goToNext = ()=>{\n        switch(viewType){\n            case \"day\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(prevDate, 1));\n                break;\n            case \"week\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_33__[\"default\"])(prevDate, 1));\n                break;\n            case \"month\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_34__[\"default\"])(prevDate, 1));\n                break;\n        }\n    };\n    const handleRequestCreateEvent = function(date) {\n        let useSystemTime = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (useSystemTime) {\n            const now = new Date();\n            const newDate = new Date(date);\n            newDate.setHours(now.getHours(), now.getMinutes(), now.getSeconds(), now.getMilliseconds());\n            handleCreateEvent(newDate);\n        } else {\n            handleCreateEvent(date);\n        }\n    };\n    const handleCreateEvent = async function() {\n        let date = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : new Date();\n        if (!canEditData) return;\n        const startTime = new Date(date);\n        const endTime = new Date(startTime.getTime() + (definition.defaultDuration || 30) * 60000);\n        const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getDatabaseTitleCol)(database.database);\n        try {\n            const recordValues = {\n                [definition.eventStartColumnId]: startTime.toISOString(),\n                ...definition.eventEndColumnId && {\n                    [definition.eventEndColumnId]: endTime.toISOString()\n                }\n            };\n            if (titleColOpts.titleColId) {\n                recordValues[titleColOpts.titleColId] = \"New Event\";\n            }\n            const result = await createRecords(definition.databaseId, [\n                recordValues\n            ]);\n            if (result && result.records && result.records.length > 0) {\n                const newRecordId = result.records[0].id;\n                if (newRecordId) {\n                    await refreshDatabase(definition.databaseId);\n                    setPeekRecordId(newRecordId);\n                    sonner__WEBPACK_IMPORTED_MODULE_14__.toast.success(\"New event created\");\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Error accessing the new event\");\n                }\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Failed to create event properly\");\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Failed to create event\");\n        }\n    };\n    const handleEventClick = (event)=>{\n        if (event && event.id) {\n            const containerId = viewType === \"day\" ? \"day-view-container\" : \"week-view-container\";\n            const container = document.getElementById(containerId);\n            if (container) {\n                savedScrollTop.current = container.scrollTop;\n            }\n            openRecord(event.id, event.record.databaseId);\n            setSelectedEvent(event.id);\n        }\n    };\n    const getHeaderDateDisplay = ()=>{\n        switch(viewType){\n            case \"day\":\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(selectedDate, \"MMMM d, yyyy\");\n            case \"week\":\n                return \"\".concat((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(selectedDate, -selectedDate.getDay()), \"MMM d\"), \" - \").concat((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(selectedDate, 6 - selectedDate.getDay()), \"MMM d, yyyy\"));\n            case \"month\":\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(selectedDate, \"MMMM yyyy\");\n            default:\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(selectedDate, \"MMMM d, yyyy\");\n        }\n    };\n    const handleEventDelete = async (event)=>{\n        if (!canEditData) return;\n        try {\n            await deleteRecords(database.database.id, [\n                event.record.id\n            ]);\n        } catch (error) {\n            console.error(\"Error deleting event:\", error);\n        }\n    };\n    const viewTypeOptions = [\n        {\n            id: \"day\",\n            value: \"day\",\n            title: \"Day\",\n            data: \"day\"\n        },\n        {\n            id: \"week\",\n            value: \"week\",\n            title: \"Week\",\n            data: \"week\"\n        },\n        {\n            id: \"month\",\n            value: \"month\",\n            title: \"Month\",\n            data: \"month\"\n        }\n    ];\n    const selectedViewOption = viewType === \"day\" ? [\n        \"day\"\n    ] : viewType === \"week\" ? [\n        \"week\"\n    ] : [\n        \"month\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"border-b border-neutral-300 bg-white\", isInRecordTab && \"py-1\"),\n                children: [\n                    isMobile ? /* Mobile Header Layout */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"p-2\", isInRecordTab && \"py-1\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xs font-semibold text-black truncate flex-1 mr-2\",\n                                        children: getHeaderDateDisplay()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: ()=>setShowSideCalendar(!showSideCalendar),\n                                        className: \"rounded-full h-8 px-3 text-xs text-black hover:bg-gray-50\",\n                                        children: \"Calendar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 509,\n                                columnNumber: 12\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToToday,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs text-black hover:bg-gray-50\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                children: \"Today\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        variant: \"ghost\",\n                                                        onClick: goToPrevious,\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleLeftIcon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 20\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 18\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        variant: \"ghost\",\n                                                        onClick: goToNext,\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleRightIcon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 20\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 18\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 16\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__.CustomSelect, {\n                                                options: viewTypeOptions,\n                                                selectedIds: selectedViewOption,\n                                                onChange: (selected)=>{\n                                                    if (selected.length > 0) {\n                                                        setViewType(selected[0]);\n                                                    }\n                                                },\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"px-3 text-xs border-neutral-300 rounded-full bg-white hover:bg-gray-50 text-black w-20\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                placeholder: \"View\",\n                                                hideSearch: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                onClick: ()=>handleRequestCreateEvent(selectedDate, true),\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs border border-neutral-300 bg-white hover:bg-gray-50 text-black\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.PlusIcon, {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 584,\n                                                    columnNumber: 20\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 577,\n                                                columnNumber: 18\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 558,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 522,\n                                columnNumber: 12\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 508,\n                        columnNumber: 10\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"flex items-center justify-between px-4\", isInRecordTab ? \"py-1\" : \"py-2\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: goToToday,\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs text-black hover:bg-gray-50\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                        children: \"Today\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 596,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToPrevious,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleLeftIcon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 615,\n                                                    columnNumber: 16\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 14\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToNext,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleRightIcon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 625,\n                                                    columnNumber: 16\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 617,\n                                                columnNumber: 14\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 606,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xs font-semibold text-black\",\n                                        children: getHeaderDateDisplay()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 629,\n                                        columnNumber: 12\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 595,\n                                columnNumber: 10\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                                placeholder: \"Search events...\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"w-48 pr-8 h-8 text-xs border-neutral-300 bg-transparent shadow-none\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 637,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.MagnifyingGlassIcon, {\n                                                className: \"h-3 w-3 absolute right-2 top-1/2 transform -translate-y-1/2 text-neutral-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 643,\n                                                columnNumber: 16\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 636,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__.CustomSelect, {\n                                        options: viewTypeOptions,\n                                        selectedIds: selectedViewOption,\n                                        onChange: (selected)=>{\n                                            if (selected.length > 0) {\n                                                setViewType(selected[0]);\n                                            }\n                                        },\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"px-3 text-xs border-neutral-300 rounded-full bg-white hover:bg-gray-50 text-black\", isInRecordTab ? \"h-6 w-20\" : \"h-8 w-28\"),\n                                        placeholder: \"View\",\n                                        hideSearch: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 647,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        onClick: ()=>handleRequestCreateEvent(selectedDate, true),\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs border border-neutral-300 bg-white hover:bg-gray-50 text-black gap-1\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.PlusIcon, {\n                                                className: \"h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 671,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Add Event\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 672,\n                                                columnNumber: 35\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 664,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 634,\n                                columnNumber: 10\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 591,\n                        columnNumber: 8\n                    }, undefined),\n                    isMobile && !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-2 pb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                    placeholder: \"Search events...\",\n                                    value: searchTerm,\n                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                    className: \"w-full pr-8 h-8 text-xs border-neutral-300 bg-transparent shadow-none\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 682,\n                                    columnNumber: 12\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.MagnifyingGlassIcon, {\n                                    className: \"h-3 w-3 absolute right-2 top-1/2 transform -translate-y-1/2 text-neutral-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 688,\n                                    columnNumber: 12\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                            lineNumber: 681,\n                            columnNumber: 10\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 680,\n                        columnNumber: 8\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                lineNumber: 502,\n                columnNumber: 6\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex min-h-0\",\n                children: [\n                    showSideCalendar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"flex-none bg-white\", isMobile ? \"w-full absolute z-50 backdrop-blur-sm h-full shadow-lg\" : \"w-fit border-r border-neutral-300\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center p-2 border-b border-neutral-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xs font-semibold text-black\",\n                                        children: \"Calendar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 702,\n                                        columnNumber: 10\n                                    }, undefined),\n                                    isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: ()=>setShowSideCalendar(false),\n                                        className: \"rounded-full h-8 w-8 p-1 text-black hover:bg-neutral-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Close\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 709,\n                                                columnNumber: 14\n                                            }, undefined),\n                                            \"\\xd7\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 704,\n                                        columnNumber: 12\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 701,\n                                columnNumber: 8\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                mode: \"single\",\n                                selected: selectedDate,\n                                onSelect: (date)=>{\n                                    if (date) {\n                                        setSelectedDate(date);\n                                        if (isMobile) {\n                                            setShowSideCalendar(false);\n                                        }\n                                    }\n                                },\n                                className: \"rounded-md border-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 714,\n                                columnNumber: 8\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 697,\n                        columnNumber: 6\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.DndContext, {\n                        sensors: sensors,\n                        onDragStart: onDragStart,\n                        onDragEnd: onDragEnd,\n                        modifiers: [\n                            restrictToCalendarContainer\n                        ],\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                \"data-calendar-content\": \"true\",\n                                children: [\n                                    viewType === \"day\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DayView__WEBPACK_IMPORTED_MODULE_19__.DayView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        openAddEventForm: handleRequestCreateEvent,\n                                        canEditData: canEditData,\n                                        savedScrollTop: savedScrollTop,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 738,\n                                        columnNumber: 7\n                                    }, undefined),\n                                    viewType === \"week\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_WeekView__WEBPACK_IMPORTED_MODULE_20__.WeekView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        setSelectedDate: setSelectedDate,\n                                        openAddEventForm: handleRequestCreateEvent,\n                                        canEditData: canEditData,\n                                        savedScrollTop: savedScrollTop,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 751,\n                                        columnNumber: 7\n                                    }, undefined),\n                                    viewType === \"month\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MonthView__WEBPACK_IMPORTED_MODULE_21__.MonthView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        setSelectedDate: setSelectedDate,\n                                        openAddEventForm: (date)=>handleRequestCreateEvent(date, true),\n                                        canEditData: canEditData,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 765,\n                                        columnNumber: 7\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 736,\n                                columnNumber: 6\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.DragOverlay, {\n                                dropAnimation: null,\n                                children: activeDragData && activeDragData.type === \"segment\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_26__.CalendarEventSegment, {\n                                    segment: activeDragData.payload,\n                                    view: viewType === \"day\" ? \"day\" : \"week\",\n                                    onClick: ()=>{},\n                                    style: {\n                                        width: activeDragData.width,\n                                        height: activeDragData.height\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 781,\n                                    columnNumber: 11\n                                }, undefined) : activeDragData && activeDragData.type === \"event\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CalendarEventItem__WEBPACK_IMPORTED_MODULE_22__.CalendarEventItem, {\n                                    event: activeDragData.payload,\n                                    view: viewType,\n                                    onClick: ()=>{},\n                                    style: {\n                                        width: activeDragData.width,\n                                        height: activeDragData.height\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 791,\n                                    columnNumber: 11\n                                }, undefined) : null\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 779,\n                                columnNumber: 6\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 730,\n                        columnNumber: 6\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                lineNumber: 695,\n                columnNumber: 4\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n        lineNumber: 501,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(CalendarView, \"gC+3ORgbOSOWj17lR2zBDE1vWrs=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace,\n        _providers_page__WEBPACK_IMPORTED_MODULE_5__.usePage,\n        _providers_screenSize__WEBPACK_IMPORTED_MODULE_16__.useScreenSize,\n        _providers_record__WEBPACK_IMPORTED_MODULE_17__.useMaybeRecord,\n        _providers_shared__WEBPACK_IMPORTED_MODULE_6__.useMaybeShared,\n        _providers_template__WEBPACK_IMPORTED_MODULE_7__.useMaybeTemplate,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViews,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewFiltering,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewSelection,\n        usePrevious,\n        _providers_stackedPeek__WEBPACK_IMPORTED_MODULE_18__.useStackedPeek,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensors\n    ];\n});\n_c = CalendarView;\nvar _c;\n$RefreshReg$(_c, \"CalendarView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/index.tsx\n"));

/***/ })

});