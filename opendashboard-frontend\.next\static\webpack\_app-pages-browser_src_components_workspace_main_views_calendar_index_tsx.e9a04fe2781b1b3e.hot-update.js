/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx":
/*!*******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/MonthView.tsx ***!
  \*******************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// import React, { useMemo } from 'react';\n// import { format, startOfMonth, endOfMonth, startOfWeek, endOfWeek, addDays, isSameDay, isToday } from 'date-fns';\n// import { cn } from '@/lib/utils';\n// import { Button } from '@/components/ui/button';\n// import { PlusIcon } from '@heroicons/react/24/outline';\n// import { ScrollArea } from '@/components/ui/scroll-area';\n// import { CalendarEvent } from '@/typings/page';\n// import { useMaybeRecord } from '@/providers/record';\n// import { CalendarEventItem } from './CalendarEventItem';\n// import { NoEvents } from './NoEvents';\n// import { CalendarSideCard } from './CalendarSideCard';\n// import { useDroppable } from '@dnd-kit/core';\n// interface MonthViewProps {\n//   selectedDate: Date;\n//   events: CalendarEvent[];\n//   selectedEvent: string | null;\n//   setSelectedEvent: (id: string) => void;\n//   setSelectedDate: (date: Date) => void;\n//   openAddEventForm: (date: Date) => void;\n//   canEditData: boolean;\n//   handleEventClick: (event: CalendarEvent) => void;\n//   activeDragData: any;\n// }\n// const DayCell = ({\n//   date,\n//   children,\n//   onClick,\n//   isCurrentMonth\n// }: {\n//   date: Date;\n//   children: React.ReactNode;\n//   onClick: () => void;\n//   isCurrentMonth: boolean;\n// }) => {\n//   const { setNodeRef, isOver } = useDroppable({\n//     id: `daycell-${format(date, 'yyyy-MM-dd')}`,\n//     data: {\n//       date: date,\n//       type: 'daycell'\n//     }\n//   });\n//   return (\n//     <div\n//       ref={setNodeRef}\n//       onClick={onClick}\n//       className={cn(\n//         \"border-b border-r border-neutral-300 relative cursor-pointer transition-colors group flex flex-col p-2 min-h-[120px] lg:p-3 lg:min-h-[140px]\",\n//         isCurrentMonth\n//           ? \"bg-white hover:bg-neutral-50\"\n//           : \"bg-neutral-100 hover:bg-neutral-200\",\n//         isOver && \"bg-blue-50 border-blue-200\"\n//       )}\n//     >\n//       {children}\n//     </div>\n//   );\n// };\n// // Helper function to check if an event affects a specific day\n// const eventAffectsDay = (event: CalendarEvent, day: Date): boolean => {\n//   const eventStart = new Date(event.start);\n//   const eventEnd = new Date(event.end);\n//   const dayStart = new Date(day);\n//   const dayEnd = new Date(day);\n//   dayEnd.setHours(23, 59, 59, 999);\n//   return eventStart <= dayEnd && eventEnd >= dayStart;\n// };\n// // New helper function to process events for the entire month with proper slot tracking\n// const useMonthEvents = (weeks: Date[][], events: CalendarEvent[]) => {\n//   return useMemo(() => {\n//     const positionedEventsByWeek = new Map<number, any[]>();\n//     const slotUsageByDay = new Map<string, number>(); // Track slot usage per day\n//     weeks.forEach((week, weekIndex) => {\n//       const weekStart = week[0];\n//       const weekEnd = week[6];\n//       // Get all events that intersect with this week\n//       const weekEvents = events.filter(event => {\n//         const eventStart = new Date(event.start);\n//         const eventEnd = new Date(event.end);\n//         return eventStart <= weekEnd && eventEnd >= weekStart;\n//       });\n//       const spanningEvents: any[] = [];\n//       weekEvents.forEach(event => {\n//         const eventStart = new Date(event.start);\n//         const eventEnd = new Date(event.end);\n//         // Find which days this event spans in this week\n//         const startDayIndex = week.findIndex(day => isSameDay(day, eventStart));\n//         const endDayIndex = week.findIndex(day => isSameDay(day, eventEnd));\n//         // Calculate actual span within this week\n//         let actualStart = 0;\n//         let actualEnd = 6;\n//         if (startDayIndex !== -1) {\n//           actualStart = startDayIndex;\n//         }\n//         if (endDayIndex !== -1) {\n//           actualEnd = endDayIndex;\n//         }\n//         // Check if event intersects with this week\n//         const eventSpansWeek = startDayIndex !== -1 || endDayIndex !== -1 || (eventStart < weekStart && eventEnd > weekEnd);\n//         if (eventSpansWeek) {\n//           spanningEvents.push({\n//             event,\n//             startDayIndex: actualStart,\n//             endDayIndex: actualEnd,\n//             colSpan: actualEnd - actualStart + 1,\n//           });\n//         }\n//       });\n//       // Sort events by start day, then by span length (longer events first)\n//       const sortedEvents = spanningEvents.sort((a, b) => {\n//         if (a.startDayIndex !== b.startDayIndex) {\n//           return a.startDayIndex - b.startDayIndex;\n//         }\n//         return b.colSpan - a.colSpan;\n//       });\n//       // Position events and track slot usage\n//       const positioned: any[] = [];\n//       const rows: any[][] = [];\n//       sortedEvents.forEach(eventData => {\n//         // Check if this event can be placed (all days it spans have available slots)\n//         const affectedDays = week.slice(eventData.startDayIndex, eventData.endDayIndex + 1);\n//         const canPlace = affectedDays.every(day => {\n//           const dayKey = format(day, 'yyyy-MM-dd');\n//           const currentUsage = slotUsageByDay.get(dayKey) || 0;\n//           return currentUsage < 4; // Maximum 4 slots per day\n//         });\n//         if (!canPlace) {\n//           // Event cannot be placed, skip it (it will be in the \"+more\" count)\n//           return;\n//         }\n//         // Find available row\n//         let assigned = false;\n//         for (let i = 0; i < rows.length; i++) {\n//           const row = rows[i];\n//           const hasConflict = row.some(existingEvent => \n//             eventData.startDayIndex <= existingEvent.endDayIndex && \n//             eventData.endDayIndex >= existingEvent.startDayIndex\n//           );\n//           if (!hasConflict) {\n//             row.push(eventData);\n//             positioned.push({ ...eventData, row: i });\n//             assigned = true;\n//             break;\n//           }\n//         }\n//         if (!assigned) {\n//           rows.push([eventData]);\n//           positioned.push({ ...eventData, row: rows.length - 1 });\n//         }\n//         // Update slot usage for all affected days\n//         affectedDays.forEach(day => {\n//           const dayKey = format(day, 'yyyy-MM-dd');\n//           const currentUsage = slotUsageByDay.get(dayKey) || 0;\n//           slotUsageByDay.set(dayKey, currentUsage + 1);\n//         });\n//       });\n//       positionedEventsByWeek.set(weekIndex, positioned);\n//     });\n//     return { positionedEventsByWeek, slotUsageByDay };\n//   }, [weeks, events]);\n// };\n// export const MonthView: React.FC<MonthViewProps> = ({\n//   selectedDate,\n//   events,\n//   selectedEvent,\n//   setSelectedEvent,\n//   setSelectedDate,\n//   openAddEventForm,\n//   canEditData,\n//   handleEventClick,\n//   activeDragData,\n// }) => {\n//   const maybeRecord = useMaybeRecord();\n//   const isInRecordTab = !!maybeRecord;\n//   // Memoize month calculations\n//   const monthCalculations = useMemo(() => {\n//     const monthStart = startOfMonth(selectedDate);\n//     const monthEnd = endOfMonth(selectedDate);\n//     const startDay = startOfWeek(monthStart, { weekStartsOn: 0 });\n//     const endDay = endOfWeek(monthEnd, { weekStartsOn: 0 });\n//     const days = [];\n//     let day = startDay;\n//     while (day <= endDay) {\n//       days.push(day);\n//       day = addDays(day, 1);\n//     }\n//     const weeks = [];\n//     for (let i = 0; i < days.length; i += 7) {\n//       weeks.push(days.slice(i, i + 7));\n//     }\n//     return { monthStart, monthEnd, startDay, endDay, days, weeks };\n//   }, [selectedDate]);\n//   // Memoize month events\n//   const monthEvents = useMemo(() => \n//     events.filter(event => {\n//       const eventStart = new Date(event.start);\n//       const eventEnd = new Date(event.end);\n//       return eventStart <= monthCalculations.endDay && \n//              eventEnd >= monthCalculations.startDay;\n//     }), \n//     [events, monthCalculations.startDay, monthCalculations.endDay]\n//   );\n//   const { positionedEventsByWeek, slotUsageByDay } = useMonthEvents(monthCalculations.weeks, monthEvents);\n//   // Render empty state when no events\n//   const renderEmptyState = () => (\n//     <div className=\"flex flex-col h-full bg-background\">\n//       <div className=\"grid grid-cols-7 border-b border-neutral-300 bg-white\">\n//         {['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'].map(dayName => (\n//           <div key={dayName} className={cn(\n//             \"text-center font-semibold text-black\",\n//             \"py-2 text-xs\"\n//           )}>\n//             {dayName.substring(0, 3)}\n//           </div>\n//         ))}\n//       </div>\n//       <NoEvents\n//         title=\"No events this month\"\n//         message={`${format(selectedDate, 'MMMM yyyy')} is completely free. Start planning your month!`}\n//         showCreateButton={canEditData}\n//         onCreate={() => openAddEventForm(selectedDate)}\n//       />\n//     </div>\n//   );\n//   // Render day cell content\n//   const renderDayCellContent = (day: Date, dayEvents: any[]) => {\n//     const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n//     const isCurrentDay = isToday(day);\n//     const MAX_VISIBLE_EVENTS = 4;\n//     const ROW_HEIGHT = 28;\n//     const dayKey = format(day, 'yyyy-MM-dd');\n//     // Get all events that affect this day (for the +more count)\n//     const allDayEvents = monthEvents.filter(event => eventAffectsDay(event, day));\n//     const totalEventsForDay = allDayEvents.length;\n//     const sortedEvents = dayEvents.sort((a, b) => a.row - b.row);\n//     const visibleEvents = sortedEvents.slice(0, MAX_VISIBLE_EVENTS);\n//     const hasMore = totalEventsForDay > MAX_VISIBLE_EVENTS;\n//     const hiddenEventsCount = Math.max(0, totalEventsForDay - MAX_VISIBLE_EVENTS);\n//     // Calculate container height\n//     const maxRow = visibleEvents.reduce((max, event) => Math.max(max, event.row), -1);\n//     const containerHeight = hasMore\n//       ? (MAX_VISIBLE_EVENTS * ROW_HEIGHT) + 20 \n//       : (maxRow + 1) * ROW_HEIGHT;\n//     return (\n//       <>\n//         <div className=\"flex items-center justify-between mb-2\">\n//           <span className={cn(\n//             \"inline-flex items-center justify-center rounded-full font-semibold text-xs w-6 h-6\",\n//             isCurrentDay ? \"bg-black text-white\" : isCurrentMonth ? \"text-black hover:bg-neutral-100\" : \"text-neutral-400\"\n//           )}>\n//             {format(day, 'd')}\n//           </span>\n//           {canEditData && isCurrentMonth && (\n//             <Button\n//               variant=\"ghost\"\n//               className=\"rounded-full h-6 w-6 p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-neutral-100 hidden lg:inline-flex\"\n//               onClick={(e) => {\n//                 e.stopPropagation();\n//                 setTimeout(() => openAddEventForm(day), 150);\n//               }}\n//             >\n//               <PlusIcon className=\"h-3 w-3 text-black\" />\n//             </Button>\n//           )}\n//         </div>\n//         <div className=\"relative\" style={{ height: `${containerHeight}px` }}>\n//           {visibleEvents.map(pe => (\n//             <div\n//               key={pe.event.id}\n//               className=\"absolute\"\n//               style={{\n//                 top: `${pe.row * ROW_HEIGHT}px`,\n//                 left: '2px',\n//                 width: pe.colSpan > 1 \n//                   ? `calc(${pe.colSpan * 100}% + ${(pe.colSpan - 1) * 19}px)`\n//                   : 'calc(100% - 4px)',\n//                 zIndex: 10 + pe.row,\n//               }}\n//             >\n//               <CalendarEventItem\n//                 event={pe.event}\n//                 view=\"month\"\n//                 onClick={(e) => {\n//                   e.stopPropagation();\n//                   setSelectedEvent(pe.event.id);\n//                   handleEventClick(pe.event);\n//                 }}\n//                 isDragging={activeDragData?.payload?.id === pe.event.id}\n//               />\n//             </div>\n//           ))}\n//           {hasMore && (\n//             <div \n//               className=\"text-black hover:text-black font-medium text-xs cursor-pointer\"\n//               style={{\n//                 position: 'absolute',\n//                 top: `${MAX_VISIBLE_EVENTS * ROW_HEIGHT}px`,\n//                 left: '2px',\n//               }}\n//               onClick={(e) => {\n//                 e.stopPropagation();\n//                 setSelectedDate(day);\n//               }}\n//             >\n//               + {hiddenEventsCount} more\n//             </div>\n//           )}\n//         </div>\n//       </>\n//     );\n//   };\n//   // Render main view\n//   return (\n//     <div className=\"h-full bg-background flex flex-col lg:flex-row\">\n//       <div className=\"flex-1 flex flex-col min-h-0\">\n//         {/* Day Headers */}\n//         <div \n//           data-day-headers=\"true\"\n//           className=\"grid grid-cols-7 border-b border-neutral-300 sticky top-0 z-10 bg-white\"\n//         >\n//           {['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'].map(dayName => (\n//             <div key={dayName} className={cn(\n//               \"text-center font-semibold text-black\",\n//               \"py-2 text-xs\"\n//             )}>\n//               {dayName.substring(0, 3)}\n//             </div>\n//           ))}\n//         </div>\n//         {/* Month Grid */}\n//         <ScrollArea className=\"flex-1\">\n//           <div className=\"grid grid-cols-7 border-neutral-300 border-b\">\n//             {monthCalculations.weeks.map((week, weekIndex) =>\n//               week.map((day, dayIndex) => {\n//                 const weekEvents = positionedEventsByWeek.get(weekIndex) || [];\n//                 const dayEvents = weekEvents.filter(pe => pe.startDayIndex === dayIndex);\n//                 const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n//                 return (\n//                   <DayCell\n//                     key={`${weekIndex}-${dayIndex}`}\n//                     date={day}\n//                     isCurrentMonth={isCurrentMonth}\n//                     onClick={() => setSelectedDate(day)}\n//                   >\n//                     {renderDayCellContent(day, dayEvents)}\n//                   </DayCell>\n//                 );\n//               }),\n//             )}\n//           </div>\n//         </ScrollArea>\n//       </div>\n//       <CalendarSideCard\n//         selectedDate={selectedDate}\n//         events={events}\n//         selectedEvent={selectedEvent}\n//         setSelectedEvent={setSelectedEvent}\n//         handleEventClick={handleEventClick}\n//       />\n//     </div>\n//   );\n// };\n// function isMultiDay(event: CalendarEvent): boolean {\n//   return !isSameDay(new Date(event.start), new Date(event.end));\n// }\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/index.tsx":
/*!****************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/index.tsx ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CalendarView: function() { return /* binding */ CalendarView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! opendb-app-db-utils/lib/typings/db */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/typings/db.js\");\n/* harmony import */ var _components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/custom-ui/loader */ \"(app-pages-browser)/./src/components/custom-ui/loader.tsx\");\n/* harmony import */ var _providers_page__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/page */ \"(app-pages-browser)/./src/providers/page.tsx\");\n/* harmony import */ var _providers_shared__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/providers/shared */ \"(app-pages-browser)/./src/providers/shared.tsx\");\n/* harmony import */ var _providers_template__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/providers/template */ \"(app-pages-browser)/./src/providers/template.tsx\");\n/* harmony import */ var _providers_views__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/providers/views */ \"(app-pages-browser)/./src/providers/views.tsx\");\n/* harmony import */ var _components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/workspace/main/views/table */ \"(app-pages-browser)/./src/components/workspace/main/views/table/index.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/icons/FontAwesomeRegular */ \"(app-pages-browser)/./src/components/icons/FontAwesomeRegular.tsx\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/subWeeks/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/subMonths/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addWeeks/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addMonths/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _providers_screenSize__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/providers/screenSize */ \"(app-pages-browser)/./src/providers/screenSize.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _providers_stackedPeek__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/providers/stackedPeek */ \"(app-pages-browser)/./src/providers/stackedPeek.tsx\");\n/* harmony import */ var _components_DayView__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./components/DayView */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/DayView.tsx\");\n/* harmony import */ var _components_WeekView__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./components/WeekView */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/WeekView.tsx\");\n/* harmony import */ var _components_MonthView__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./components/MonthView */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx\");\n/* harmony import */ var _components_MonthView__WEBPACK_IMPORTED_MODULE_21___default = /*#__PURE__*/__webpack_require__.n(_components_MonthView__WEBPACK_IMPORTED_MODULE_21__);\n/* harmony import */ var _components_CalendarEventItem__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./components/CalendarEventItem */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx\");\n/* harmony import */ var _components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/workspace/main/views/form/components/element/linked */ \"(app-pages-browser)/./src/components/workspace/main/views/form/components/element/linked.tsx\");\n/* harmony import */ var _components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/custom-ui/customSelect */ \"(app-pages-browser)/./src/components/custom-ui/customSelect.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n/* harmony import */ var _components_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./components/CalendarEventSegment */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx\");\n/* harmony import */ var _barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=differenceInDays,differenceInMilliseconds!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/differenceInMilliseconds/index.js\");\n/* harmony import */ var _barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=differenceInDays,differenceInMilliseconds!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/differenceInDays/index.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Custom modifier to restrict dragging to the calendar main content area only\nconst restrictToCalendarContainer = (param)=>{\n    let { transform, draggingNodeRect, windowRect } = param;\n    if (!draggingNodeRect || !windowRect) {\n        return transform;\n    }\n    // Find the calendar main content container (the div that contains the views)\n    const calendarContainer = document.querySelector('[data-calendar-content=\"true\"]');\n    if (!calendarContainer) {\n        return transform;\n    }\n    const containerRect = calendarContainer.getBoundingClientRect();\n    // For month view, we need to account for the side card\n    // The side card is positioned on the right side of the calendar\n    const sideCard = document.querySelector('[data-side-card=\"true\"]');\n    let maxX = containerRect.right - draggingNodeRect.width;\n    if (sideCard) {\n        const sideCardRect = sideCard.getBoundingClientRect();\n        // Restrict dragging to not go past the side card\n        maxX = Math.min(maxX, sideCardRect.left - draggingNodeRect.width);\n    }\n    // Find header areas to prevent dragging over them\n    const timeLabels = document.querySelector('[data-time-labels=\"true\"]');\n    const dayHeaders = document.querySelector('[data-day-headers=\"true\"]');\n    const allDayRow = document.querySelector('[data-all-day-row=\"true\"]');\n    // Calculate the boundaries relative to the window\n    let minX = containerRect.left;\n    let minY = containerRect.top;\n    const maxY = containerRect.bottom - draggingNodeRect.height;\n    // Prevent dragging over time labels (left side in day/week view)\n    if (timeLabels) {\n        const timeLabelsRect = timeLabels.getBoundingClientRect();\n        minX = Math.max(minX, timeLabelsRect.right);\n    }\n    // Prevent dragging over day headers (top of calendar)\n    if (dayHeaders) {\n        const dayHeadersRect = dayHeaders.getBoundingClientRect();\n        minY = Math.max(minY, dayHeadersRect.bottom);\n    }\n    // Prevent dragging over all-day row (if present)\n    if (allDayRow) {\n        const allDayRowRect = allDayRow.getBoundingClientRect();\n        minY = Math.max(minY, allDayRowRect.bottom);\n    }\n    // Get current pointer position\n    const currentX = transform.x + draggingNodeRect.left;\n    const currentY = transform.y + draggingNodeRect.top;\n    // Constrain the position\n    const constrainedX = Math.min(Math.max(currentX, minX), maxX);\n    const constrainedY = Math.min(Math.max(currentY, minY), maxY);\n    return {\n        ...transform,\n        x: constrainedX - draggingNodeRect.left,\n        y: constrainedY - draggingNodeRect.top\n    };\n};\n// Custom hook to track previous value\nconst usePrevious = (value)=>{\n    _s();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        ref.current = value;\n    });\n    return ref.current;\n};\n_s(usePrevious, \"8uVE59eA/r6b92xF80p7sH8rXLk=\");\nconst CalendarView = (props)=>{\n    _s1();\n    const { databaseStore, members, workspace } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace)();\n    const { definition } = props;\n    const { accessLevel } = (0,_providers_page__WEBPACK_IMPORTED_MODULE_5__.usePage)();\n    const { isMobile } = (0,_providers_screenSize__WEBPACK_IMPORTED_MODULE_16__.useScreenSize)();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_17__.useMaybeRecord)();\n    const maybeShared = (0,_providers_shared__WEBPACK_IMPORTED_MODULE_6__.useMaybeShared)();\n    const maybeTemplate = (0,_providers_template__WEBPACK_IMPORTED_MODULE_7__.useMaybeTemplate)();\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [viewType, setViewType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"week\");\n    const [selectedEvent, setSelectedEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showSideCalendar, setShowSideCalendar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!isMobile);\n    const [activeDragData, setActiveDragData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const savedScrollTop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const pointerCoordinates = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        x: 0,\n        y: 0\n    });\n    const isInRecordTab = !!maybeRecord;\n    definition.filter = definition.filter || {\n        conditions: [],\n        match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Match.All\n    };\n    definition.sorts = definition.sorts || [];\n    const databaseId = definition.databaseId;\n    const database = databaseStore[definition.databaseId];\n    const isPublishedView = !!maybeShared;\n    const editable = !definition.lockContent && !isPublishedView && !!accessLevel;\n    let canEditStructure = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    let canEditData = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    const { createRecords, updateRecordValues, setPeekRecordId, peekRecordId, refreshDatabase, deleteRecords } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViews)();\n    const { sorts, filter, search } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewFiltering)();\n    const { selectedIds, setSelectedIds } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewSelection)();\n    const prevPeekRecordId = usePrevious(peekRecordId);\n    const { openRecord } = (0,_providers_stackedPeek__WEBPACK_IMPORTED_MODULE_18__.useStackedPeek)();\n    const sensors = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensors)((0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.PointerSensor, {\n        activationConstraint: {\n            distance: 8\n        }\n    }), (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.TouchSensor, {\n        activationConstraint: {\n            delay: 150,\n            tolerance: 5\n        }\n    }));\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const containerId = viewType === \"day\" ? \"day-view-container\" : \"week-view-container\";\n        const container = document.getElementById(containerId);\n        if (container) {\n            requestAnimationFrame(()=>{\n                container.scrollTop = savedScrollTop.current;\n            });\n        }\n    }, [\n        selectedEvent,\n        viewType\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setShowSideCalendar(!isMobile);\n    }, [\n        isMobile\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only refresh if the peek view was open and is now closed.\n        if (prevPeekRecordId && !peekRecordId) {\n            console.log(\"Peek view was closed, refreshing calendar data\");\n            refreshDatabase(definition.databaseId);\n        }\n    }, [\n        peekRecordId,\n        prevPeekRecordId,\n        definition.databaseId,\n        refreshDatabase\n    ]);\n    if (!database) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_4__.PageLoader, {\n        size: \"full\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n        lineNumber: 211,\n        columnNumber: 25\n    }, undefined);\n    const getEvents = ()=>{\n        var _workspace_workspaceMember, _database_database;\n        if (!database) return [];\n        const { rows } = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__.filterAndSortRecords)(database, members, databaseStore, definition.filter || {\n            match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Match.All,\n            conditions: []\n        }, filter, sorts.length ? sorts : definition.sorts || [], (workspace === null || workspace === void 0 ? void 0 : (_workspace_workspaceMember = workspace.workspaceMember) === null || _workspace_workspaceMember === void 0 ? void 0 : _workspace_workspaceMember.userId) || \"\", (database === null || database === void 0 ? void 0 : (_database_database = database.database) === null || _database_database === void 0 ? void 0 : _database_database.id) || \"\");\n        const filteredRows = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__.searchFilteredRecords)(search || \"\", rows);\n        const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getDatabaseTitleCol)(database.database);\n        return filteredRows.map((row)=>{\n            const startValue = row.processedRecord.processedRecordValues[definition.eventStartColumnId];\n            let startDate;\n            if (startValue && typeof startValue === \"string\") {\n                startDate = new Date(startValue);\n            } else {\n                startDate = new Date();\n            }\n            let endDate;\n            if (definition.eventEndColumnId) {\n                const endValue = row.processedRecord.processedRecordValues[definition.eventEndColumnId];\n                if (endValue && typeof endValue === \"string\") {\n                    endDate = new Date(endValue);\n                } else {\n                    endDate = new Date(startDate.getTime() + (definition.defaultDuration || 30) * 60000);\n                }\n            } else {\n                endDate = new Date(startDate.getTime() + (definition.defaultDuration || 30) * 60000);\n            }\n            const title = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getRecordTitle)(row.record, titleColOpts.titleColId, titleColOpts.defaultTitle, titleColOpts.isContacts);\n            return {\n                id: row.id,\n                title,\n                start: startDate,\n                end: endDate,\n                record: row.record,\n                processedRecord: row.processedRecord\n            };\n        });\n    };\n    const getFilteredEvents = ()=>{\n        const baseEvents = getEvents();\n        if (!searchTerm.trim()) {\n            return baseEvents;\n        }\n        return baseEvents.filter((event)=>{\n            const searchLower = searchTerm.toLowerCase();\n            return event.title.toLowerCase().includes(searchLower);\n        });\n    };\n    const onDragStart = (event)=>{\n        if (event.active.data.current) {\n            var _event_active_rect_current_translated, _event_active_rect_current, _event_active_rect_current_translated1, _event_active_rect_current_translated2;\n            const initialPointerY = pointerCoordinates.current.y;\n            var _event_active_rect_current_translated_top;\n            const initialEventTop = (_event_active_rect_current_translated_top = (_event_active_rect_current = event.active.rect.current) === null || _event_active_rect_current === void 0 ? void 0 : (_event_active_rect_current_translated = _event_active_rect_current.translated) === null || _event_active_rect_current_translated === void 0 ? void 0 : _event_active_rect_current_translated.top) !== null && _event_active_rect_current_translated_top !== void 0 ? _event_active_rect_current_translated_top : 0;\n            const grabOffsetY = initialPointerY - initialEventTop;\n            // Get the exact dimensions from the DOM element\n            // Handle both event and segment types\n            const { payload, type } = event.active.data.current;\n            const eventId = type === \"segment\" ? payload.originalEvent.id : payload.id;\n            const draggedElement = document.getElementById(\"event-\".concat(eventId));\n            const width = draggedElement ? draggedElement.offsetWidth : (_event_active_rect_current_translated1 = event.active.rect.current.translated) === null || _event_active_rect_current_translated1 === void 0 ? void 0 : _event_active_rect_current_translated1.width;\n            const height = draggedElement ? draggedElement.offsetHeight : (_event_active_rect_current_translated2 = event.active.rect.current.translated) === null || _event_active_rect_current_translated2 === void 0 ? void 0 : _event_active_rect_current_translated2.height;\n            setActiveDragData({\n                ...event.active.data.current,\n                grabOffsetY,\n                width,\n                height\n            });\n            document.addEventListener(\"mousemove\", handleMouseMove);\n            document.addEventListener(\"touchmove\", handleTouchMove);\n        }\n    };\n    const onDragEnd = async (param)=>{\n        let { active, over } = param;\n        document.removeEventListener(\"mousemove\", handleMouseMove);\n        document.removeEventListener(\"touchmove\", handleTouchMove);\n        setActiveDragData(null);\n        if (!over || !active || !canEditData || active.id === over.id) {\n            return;\n        }\n        const activeData = active.data.current;\n        const overData = over.data.current;\n        if (!activeData || !overData) {\n            return;\n        }\n        const { payload, type } = activeData;\n        const eventToUpdate = type === \"segment\" ? payload.originalEvent : payload;\n        const originalStart = new Date(eventToUpdate.start);\n        const originalEnd = new Date(eventToUpdate.end);\n        const duration = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_27__[\"default\"])(originalEnd, originalStart);\n        let newStart;\n        if (overData.type.startsWith(\"allday\")) {\n            const dayDifference = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_28__[\"default\"])(overData.date, (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_29__[\"default\"])(originalStart));\n            newStart = (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(originalStart, dayDifference);\n            newStart.setHours(0, 0, 0, 0);\n        } else if (overData.type === \"timeslot-minute\") {\n            // Handle precise minute-based drops\n            newStart = new Date(overData.date);\n            newStart.setHours(overData.hour, overData.minute, 0, 0);\n        } else if (overData.type === \"daycell\") {\n            const dayDifference = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_28__[\"default\"])(overData.date, (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_29__[\"default\"])(originalStart));\n            newStart = (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(originalStart, dayDifference);\n        } else {\n            return;\n        }\n        const newEnd = new Date(newStart.getTime() + duration);\n        const recordId = eventToUpdate.record.id;\n        const newValues = {\n            [definition.eventStartColumnId]: newStart.toISOString(),\n            ...definition.eventEndColumnId && {\n                [definition.eventEndColumnId]: newEnd.toISOString()\n            }\n        };\n        try {\n            await updateRecordValues(definition.databaseId, [\n                recordId\n            ], newValues);\n            sonner__WEBPACK_IMPORTED_MODULE_14__.toast.success('Event \"'.concat(eventToUpdate.title, '\" updated.'));\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Failed to update event.\");\n            console.error(\"Error updating event:\", error);\n        }\n    };\n    const handleMouseMove = (event)=>{\n        pointerCoordinates.current = {\n            x: event.clientX,\n            y: event.clientY\n        };\n    };\n    const handleTouchMove = (event)=>{\n        const touch = event.touches[0];\n        pointerCoordinates.current = {\n            x: touch.clientX,\n            y: touch.clientY\n        };\n    };\n    const events = getFilteredEvents();\n    const goToToday = ()=>setSelectedDate(new Date());\n    const goToPrevious = ()=>{\n        switch(viewType){\n            case \"day\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(prevDate, -1));\n                break;\n            case \"week\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_31__[\"default\"])(prevDate, 1));\n                break;\n            case \"month\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_32__[\"default\"])(prevDate, 1));\n                break;\n        }\n    };\n    const goToNext = ()=>{\n        switch(viewType){\n            case \"day\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(prevDate, 1));\n                break;\n            case \"week\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_33__[\"default\"])(prevDate, 1));\n                break;\n            case \"month\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_34__[\"default\"])(prevDate, 1));\n                break;\n        }\n    };\n    const handleRequestCreateEvent = function(date) {\n        let useSystemTime = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (useSystemTime) {\n            const now = new Date();\n            const newDate = new Date(date);\n            newDate.setHours(now.getHours(), now.getMinutes(), now.getSeconds(), now.getMilliseconds());\n            handleCreateEvent(newDate);\n        } else {\n            handleCreateEvent(date);\n        }\n    };\n    const handleCreateEvent = async function() {\n        let date = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : new Date();\n        if (!canEditData) return;\n        const startTime = new Date(date);\n        const endTime = new Date(startTime.getTime() + (definition.defaultDuration || 30) * 60000);\n        const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getDatabaseTitleCol)(database.database);\n        try {\n            const recordValues = {\n                [definition.eventStartColumnId]: startTime.toISOString(),\n                ...definition.eventEndColumnId && {\n                    [definition.eventEndColumnId]: endTime.toISOString()\n                }\n            };\n            if (titleColOpts.titleColId) {\n                recordValues[titleColOpts.titleColId] = \"New Event\";\n            }\n            const result = await createRecords(definition.databaseId, [\n                recordValues\n            ]);\n            if (result && result.records && result.records.length > 0) {\n                const newRecordId = result.records[0].id;\n                if (newRecordId) {\n                    await refreshDatabase(definition.databaseId);\n                    setPeekRecordId(newRecordId);\n                    sonner__WEBPACK_IMPORTED_MODULE_14__.toast.success(\"New event created\");\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Error accessing the new event\");\n                }\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Failed to create event properly\");\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Failed to create event\");\n        }\n    };\n    const handleEventClick = (event)=>{\n        if (event && event.id) {\n            const containerId = viewType === \"day\" ? \"day-view-container\" : \"week-view-container\";\n            const container = document.getElementById(containerId);\n            if (container) {\n                savedScrollTop.current = container.scrollTop;\n            }\n            openRecord(event.id, event.record.databaseId);\n            setSelectedEvent(event.id);\n        }\n    };\n    const getHeaderDateDisplay = ()=>{\n        switch(viewType){\n            case \"day\":\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(selectedDate, \"MMMM d, yyyy\");\n            case \"week\":\n                return \"\".concat((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(selectedDate, -selectedDate.getDay()), \"MMM d\"), \" - \").concat((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(selectedDate, 6 - selectedDate.getDay()), \"MMM d, yyyy\"));\n            case \"month\":\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(selectedDate, \"MMMM yyyy\");\n            default:\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(selectedDate, \"MMMM d, yyyy\");\n        }\n    };\n    const handleEventDelete = async (event)=>{\n        if (!canEditData) return;\n        try {\n            await deleteRecords(database.database.id, [\n                event.record.id\n            ]);\n        } catch (error) {\n            console.error(\"Error deleting event:\", error);\n        }\n    };\n    const viewTypeOptions = [\n        {\n            id: \"day\",\n            value: \"day\",\n            title: \"Day\",\n            data: \"day\"\n        },\n        {\n            id: \"week\",\n            value: \"week\",\n            title: \"Week\",\n            data: \"week\"\n        },\n        {\n            id: \"month\",\n            value: \"month\",\n            title: \"Month\",\n            data: \"month\"\n        }\n    ];\n    const selectedViewOption = viewType === \"day\" ? [\n        \"day\"\n    ] : viewType === \"week\" ? [\n        \"week\"\n    ] : [\n        \"month\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"border-b border-neutral-300 bg-white\", isInRecordTab && \"py-1\"),\n                children: [\n                    isMobile ? /* Mobile Header Layout */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"p-2\", isInRecordTab && \"py-1\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xs font-semibold text-black truncate flex-1 mr-2\",\n                                        children: getHeaderDateDisplay()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: ()=>setShowSideCalendar(!showSideCalendar),\n                                        className: \"rounded-full h-8 px-3 text-xs text-black hover:bg-gray-50\",\n                                        children: \"Calendar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 509,\n                                columnNumber: 12\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToToday,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs text-black hover:bg-gray-50\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                children: \"Today\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        variant: \"ghost\",\n                                                        onClick: goToPrevious,\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleLeftIcon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 20\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 18\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        variant: \"ghost\",\n                                                        onClick: goToNext,\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleRightIcon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 20\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 18\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 16\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__.CustomSelect, {\n                                                options: viewTypeOptions,\n                                                selectedIds: selectedViewOption,\n                                                onChange: (selected)=>{\n                                                    if (selected.length > 0) {\n                                                        setViewType(selected[0]);\n                                                    }\n                                                },\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"px-3 text-xs border-neutral-300 rounded-full bg-white hover:bg-gray-50 text-black w-20\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                placeholder: \"View\",\n                                                hideSearch: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                onClick: ()=>handleRequestCreateEvent(selectedDate, true),\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs border border-neutral-300 bg-white hover:bg-gray-50 text-black\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.PlusIcon, {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 584,\n                                                    columnNumber: 20\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 577,\n                                                columnNumber: 18\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 558,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 522,\n                                columnNumber: 12\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 508,\n                        columnNumber: 10\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"flex items-center justify-between px-4\", isInRecordTab ? \"py-1\" : \"py-2\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: goToToday,\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs text-black hover:bg-gray-50\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                        children: \"Today\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 596,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToPrevious,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleLeftIcon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 615,\n                                                    columnNumber: 16\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 14\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToNext,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleRightIcon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 625,\n                                                    columnNumber: 16\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 617,\n                                                columnNumber: 14\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 606,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xs font-semibold text-black\",\n                                        children: getHeaderDateDisplay()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 629,\n                                        columnNumber: 12\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 595,\n                                columnNumber: 10\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                                placeholder: \"Search events...\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"w-48 pr-8 h-8 text-xs border-neutral-300 bg-transparent shadow-none\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 637,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.MagnifyingGlassIcon, {\n                                                className: \"h-3 w-3 absolute right-2 top-1/2 transform -translate-y-1/2 text-neutral-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 643,\n                                                columnNumber: 16\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 636,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__.CustomSelect, {\n                                        options: viewTypeOptions,\n                                        selectedIds: selectedViewOption,\n                                        onChange: (selected)=>{\n                                            if (selected.length > 0) {\n                                                setViewType(selected[0]);\n                                            }\n                                        },\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"px-3 text-xs border-neutral-300 rounded-full bg-white hover:bg-gray-50 text-black\", isInRecordTab ? \"h-6 w-20\" : \"h-8 w-28\"),\n                                        placeholder: \"View\",\n                                        hideSearch: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 647,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        onClick: ()=>handleRequestCreateEvent(selectedDate, true),\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs border border-neutral-300 bg-white hover:bg-gray-50 text-black gap-1\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.PlusIcon, {\n                                                className: \"h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 671,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Add Event\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 672,\n                                                columnNumber: 35\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 664,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 634,\n                                columnNumber: 10\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 591,\n                        columnNumber: 8\n                    }, undefined),\n                    isMobile && !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-2 pb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                    placeholder: \"Search events...\",\n                                    value: searchTerm,\n                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                    className: \"w-full pr-8 h-8 text-xs border-neutral-300 bg-transparent shadow-none\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 682,\n                                    columnNumber: 12\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.MagnifyingGlassIcon, {\n                                    className: \"h-3 w-3 absolute right-2 top-1/2 transform -translate-y-1/2 text-neutral-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 688,\n                                    columnNumber: 12\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                            lineNumber: 681,\n                            columnNumber: 10\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 680,\n                        columnNumber: 8\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                lineNumber: 502,\n                columnNumber: 6\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex min-h-0\",\n                children: [\n                    showSideCalendar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"flex-none bg-white\", isMobile ? \"w-full absolute z-50 backdrop-blur-sm h-full shadow-lg\" : \"w-fit border-r border-neutral-300\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center p-2 border-b border-neutral-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xs font-semibold text-black\",\n                                        children: \"Calendar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 702,\n                                        columnNumber: 10\n                                    }, undefined),\n                                    isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: ()=>setShowSideCalendar(false),\n                                        className: \"rounded-full h-8 w-8 p-1 text-black hover:bg-neutral-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Close\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 709,\n                                                columnNumber: 14\n                                            }, undefined),\n                                            \"\\xd7\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 704,\n                                        columnNumber: 12\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 701,\n                                columnNumber: 8\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                mode: \"single\",\n                                selected: selectedDate,\n                                onSelect: (date)=>{\n                                    if (date) {\n                                        setSelectedDate(date);\n                                        if (isMobile) {\n                                            setShowSideCalendar(false);\n                                        }\n                                    }\n                                },\n                                className: \"rounded-md border-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 714,\n                                columnNumber: 8\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 697,\n                        columnNumber: 6\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.DndContext, {\n                        sensors: sensors,\n                        onDragStart: onDragStart,\n                        onDragEnd: onDragEnd,\n                        modifiers: [\n                            restrictToCalendarContainer\n                        ],\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                \"data-calendar-content\": \"true\",\n                                children: [\n                                    viewType === \"day\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DayView__WEBPACK_IMPORTED_MODULE_19__.DayView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        openAddEventForm: handleRequestCreateEvent,\n                                        canEditData: canEditData,\n                                        savedScrollTop: savedScrollTop,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 738,\n                                        columnNumber: 7\n                                    }, undefined),\n                                    viewType === \"week\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_WeekView__WEBPACK_IMPORTED_MODULE_20__.WeekView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        setSelectedDate: setSelectedDate,\n                                        openAddEventForm: handleRequestCreateEvent,\n                                        canEditData: canEditData,\n                                        savedScrollTop: savedScrollTop,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 751,\n                                        columnNumber: 7\n                                    }, undefined),\n                                    viewType === \"month\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MonthView__WEBPACK_IMPORTED_MODULE_21__.MonthView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        setSelectedDate: setSelectedDate,\n                                        openAddEventForm: (date)=>handleRequestCreateEvent(date, true),\n                                        canEditData: canEditData,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 765,\n                                        columnNumber: 7\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 736,\n                                columnNumber: 6\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.DragOverlay, {\n                                dropAnimation: null,\n                                children: activeDragData && activeDragData.type === \"segment\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_26__.CalendarEventSegment, {\n                                    segment: activeDragData.payload,\n                                    view: viewType === \"day\" ? \"day\" : \"week\",\n                                    onClick: ()=>{},\n                                    style: {\n                                        width: activeDragData.width,\n                                        height: activeDragData.height\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 781,\n                                    columnNumber: 11\n                                }, undefined) : activeDragData && activeDragData.type === \"event\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CalendarEventItem__WEBPACK_IMPORTED_MODULE_22__.CalendarEventItem, {\n                                    event: activeDragData.payload,\n                                    view: viewType,\n                                    onClick: ()=>{},\n                                    style: {\n                                        width: activeDragData.width,\n                                        height: activeDragData.height\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 791,\n                                    columnNumber: 11\n                                }, undefined) : null\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 779,\n                                columnNumber: 6\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 730,\n                        columnNumber: 6\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                lineNumber: 695,\n                columnNumber: 4\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n        lineNumber: 501,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(CalendarView, \"gC+3ORgbOSOWj17lR2zBDE1vWrs=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace,\n        _providers_page__WEBPACK_IMPORTED_MODULE_5__.usePage,\n        _providers_screenSize__WEBPACK_IMPORTED_MODULE_16__.useScreenSize,\n        _providers_record__WEBPACK_IMPORTED_MODULE_17__.useMaybeRecord,\n        _providers_shared__WEBPACK_IMPORTED_MODULE_6__.useMaybeShared,\n        _providers_template__WEBPACK_IMPORTED_MODULE_7__.useMaybeTemplate,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViews,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewFiltering,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewSelection,\n        usePrevious,\n        _providers_stackedPeek__WEBPACK_IMPORTED_MODULE_18__.useStackedPeek,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensors\n    ];\n});\n_c = CalendarView;\nvar _c;\n$RefreshReg$(_c, \"CalendarView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/index.tsx\n"));

/***/ })

});