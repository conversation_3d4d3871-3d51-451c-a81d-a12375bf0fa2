"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/WeekView.tsx":
/*!******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/WeekView.tsx ***!
  \******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WeekView: function() { return /* binding */ WeekView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/setHours/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CalendarEventSegment */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx\");\n/* harmony import */ var _AllDayRow__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./AllDayRow */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/AllDayRow.tsx\");\n/* harmony import */ var _NoEvents__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./NoEvents */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/NoEvents.tsx\");\n/* harmony import */ var _utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/multiDayEventUtils */ \"(app-pages-browser)/./src/utils/multiDayEventUtils.ts\");\n/* harmony import */ var _utils_eventCollisionUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/eventCollisionUtils */ \"(app-pages-browser)/./src/utils/eventCollisionUtils.ts\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst TimeSlot = (param)=>{\n    let { day, hour, children, onDoubleClick } = param;\n    _s();\n    const [isHovering, setIsHovering] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const timeSlotRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Create minute segments only when hovering\n    const minuteSegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (!isHovering) return [];\n        return Array.from({\n            length: 60\n        }, (_, i)=>i);\n    }, [\n        isHovering\n    ]);\n    const handleMouseEnter = ()=>{\n        setIsHovering(true);\n    };\n    const handleMouseLeave = ()=>{\n        setIsHovering(false);\n        setMousePosition(null);\n    };\n    const handleMouseMove = (e)=>{\n        if (!timeSlotRef.current) return;\n        const rect = timeSlotRef.current.getBoundingClientRect();\n        setMousePosition({\n            x: e.clientX - rect.left,\n            y: e.clientY - rect.top\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: timeSlotRef,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-1 border-r border-neutral-300 last:border-r-0 relative min-h-[60px] cursor-pointer\"),\n        onMouseEnter: handleMouseEnter,\n        onMouseLeave: handleMouseLeave,\n        onMouseMove: handleMouseMove,\n        children: [\n            isHovering && mousePosition && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex flex-col\",\n                children: minuteSegments.map((minute)=>{\n                    const segmentTop = minute / 60 * 100;\n                    const segmentBottom = (minute + 1) / 60 * 100;\n                    const isActive = mousePosition.y >= segmentTop / 100 * 60 && mousePosition.y < segmentBottom / 100 * 60;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MinuteSegment, {\n                        day: day,\n                        hour: hour,\n                        minute: minute,\n                        style: {\n                            height: \"\".concat(100 / 60, \"%\"),\n                            top: \"\".concat(segmentTop, \"%\"),\n                            opacity: isActive ? 0.1 : 0\n                        },\n                        onDoubleClick: ()=>onDoubleClick(minute)\n                    }, minute, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 15\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 88,\n                columnNumber: 9\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TimeSlot, \"dQOYw4CYBhxjpdw8b31LvmtVyxA=\");\n_c = TimeSlot;\n// MinuteSegment component optimized for performance\nconst MinuteSegment = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(_s1((param)=>{\n    let { day, hour, minute, style, onDoubleClick } = param;\n    _s1();\n    const { setNodeRef, isOver } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__.useDroppable)({\n        id: \"timeslot-\".concat((0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day, \"yyyy-MM-dd\"), \"-\").concat(hour, \"-\").concat(minute),\n        data: {\n            date: day,\n            hour,\n            minute,\n            type: \"timeslot-minute\"\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: setNodeRef,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"absolute w-full\", isOver && \"bg-blue-50\"),\n        style: style,\n        onDoubleClick: onDoubleClick\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n        lineNumber: 142,\n        columnNumber: 5\n    }, undefined);\n}, \"DmJTTt6A5xWIX/faBiFge3FOLrw=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__.useDroppable\n    ];\n}));\n_c1 = MinuteSegment;\nconst WeekView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, setSelectedDate, openAddEventForm, canEditData, savedScrollTop, handleEventClick, activeDragData } = param;\n    _s2();\n    // Memoize week-related calculations\n    const weekCalculations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const weekStart = (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        });\n        const weekEnd = (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        });\n        const days = Array.from({\n            length: 7\n        }, (_, i)=>(0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(weekStart, i));\n        const todayIndex = days.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(day));\n        return {\n            weekStart,\n            weekEnd,\n            days,\n            todayIndex\n        };\n    }, [\n        selectedDate\n    ]);\n    const { days, todayIndex } = weekCalculations;\n    const hours = Array.from({\n        length: 24\n    }, (_, i)=>i);\n    // Memoize week segments\n    const weekSegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const allSegments = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.eventsToSegments)(events);\n        return (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getSegmentsForWeek)(allSegments, weekCalculations.weekStart, weekCalculations.weekEnd);\n    }, [\n        events,\n        weekCalculations.weekStart,\n        weekCalculations.weekEnd\n    ]);\n    // Separate all-day and time-slot segments\n    const allDaySegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getAllDaySegments)(weekSegments), [\n        weekSegments\n    ]);\n    const timeSlotSegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getTimeSlotSegments)(weekSegments), [\n        weekSegments\n    ]);\n    // Memoize current time position\n    const currentTimePosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>todayIndex !== -1 ? {\n            dayIndex: todayIndex,\n            hour: new Date().getHours(),\n            minutes: new Date().getMinutes()\n        } : null, [\n        todayIndex\n    ]);\n    // Helper to get event duration in minutes\n    const getEventDurationInMinutes = (event)=>{\n        const start = new Date(event.start);\n        const end = new Date(event.end);\n        return Math.max(20, (end.getTime() - start.getTime()) / (1000 * 60));\n    };\n    // Render empty state when no events\n    const renderEmptyState = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NoEvents__WEBPACK_IMPORTED_MODULE_5__.NoEvents, {\n            title: \"No events this week\",\n            message: \"Your week is completely free. Add some events to get organized!\",\n            showCreateButton: canEditData,\n            onCreate: ()=>openAddEventForm(selectedDate)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n            lineNumber: 215,\n            columnNumber: 5\n        }, undefined);\n    // Render time slots with events\n    const renderTimeSlots = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 relative bg-white border-b border-neutral-300 overflow-y-auto lg:overflow-auto\",\n            id: \"week-view-container\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative overflow-x-auto lg:overflow-x-visible\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col min-w-[700px] lg:min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: hours.map((hour, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex border-b border-neutral-300 hover:bg-neutral-50 transition-colors\", i === hours.length - 1 && \"border-b-neutral-300\"),\n                                    style: {\n                                        height: \"60px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            \"data-time-labels\": \"true\",\n                                            className: \"sticky left-0 flex items-start justify-end pr-4 pt-2 text-xs font-medium text-black border-r border-neutral-200 bg-white z-20 w-14 lg:w-20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs font-semibold\",\n                                                    children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(new Date(), hour), \"h a\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        days.map((day)=>{\n                                            const daySegments = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getSegmentsForDay)(timeSlotSegments, day);\n                                            const { segmentLayouts } = (0,_utils_eventCollisionUtils__WEBPACK_IMPORTED_MODULE_7__.calculateLayout)(daySegments);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TimeSlot, {\n                                                day: day,\n                                                hour: hour,\n                                                onDoubleClick: (minute)=>{\n                                                    if (canEditData) {\n                                                        const newDate = new Date(day);\n                                                        newDate.setHours(hour, minute, 0, 0);\n                                                        openAddEventForm(newDate);\n                                                    }\n                                                },\n                                                children: segmentLayouts.map((layout)=>{\n                                                    var _activeDragData_payload;\n                                                    const segmentStart = layout.segment.startTime;\n                                                    const isFirstHour = segmentStart.getHours() === hour;\n                                                    if (!isFirstHour) return null;\n                                                    const segmentHeight = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getSegmentHeight)(layout.segment);\n                                                    const topOffset = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getSegmentTopOffset)(layout.segment);\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__.CalendarEventSegment, {\n                                                        segment: layout.segment,\n                                                        style: {\n                                                            height: \"\".concat(segmentHeight, \"px\"),\n                                                            position: \"absolute\",\n                                                            top: \"\".concat(topOffset, \"px\"),\n                                                            left: \"\".concat(layout.left, \"%\"),\n                                                            width: \"\".concat(layout.width, \"%\"),\n                                                            zIndex: selectedEvent === layout.segment.originalEventId ? 20 : layout.zIndex,\n                                                            paddingRight: \"2px\",\n                                                            border: layout.hasOverlap ? \"1px solid white\" : \"none\"\n                                                        },\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            const container = document.getElementById(\"week-view-container\");\n                                                            if (container) {\n                                                                savedScrollTop.current = container.scrollTop;\n                                                            }\n                                                            setSelectedEvent(layout.segment.originalEventId);\n                                                            handleEventClick(layout.segment.originalEvent);\n                                                        },\n                                                        view: \"week\",\n                                                        isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === layout.segment.id\n                                                    }, layout.segment.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 27\n                                                    }, undefined);\n                                                })\n                                            }, \"\".concat(day.toISOString(), \"-\").concat(hour), false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 21\n                                            }, undefined);\n                                        })\n                                    ]\n                                }, hour, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 11\n                        }, undefined),\n                        currentTimePosition && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-0 bottom-0 left-14 lg:left-20 right-0 pointer-events-none z-30\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative h-full w-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute flex items-center\",\n                                    style: {\n                                        top: \"\".concat((currentTimePosition.hour + currentTimePosition.minutes / 60) * 60, \"px\"),\n                                        left: \"\".concat(currentTimePosition.dayIndex / 7 * 100, \"%\"),\n                                        width: \"\".concat(1 / 7 * 100, \"%\")\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 rounded-full bg-red-500 border-2 border-white shadow-lg -ml-1.5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 border-t-2 border-red-500 shadow-sm\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n            lineNumber: 225,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                \"data-day-headers\": \"true\",\n                className: \"border-b border-neutral-300 bg-white sticky top-0 z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex overflow-x-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"sticky left-0 bg-white z-10 w-14 lg:w-20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 343,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-1 min-w-[calc(100vw-3.5rem)] lg:min-w-0\",\n                            children: days.map((day, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 text-center cursor-pointer py-3 px-0 lg:py-4\",\n                                    onClick: ()=>setSelectedDate(day),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-semibold text-black mb-1\", \"text-xs\"),\n                                            children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day, \"EEE\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center font-medium text-xs w-6 h-6 rounded-full\", (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(day) ? \"bg-black text-white\" : \"text-black hover:bg-neutral-100\"),\n                                            children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day, \"d\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 344,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                    lineNumber: 342,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 338,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AllDayRow__WEBPACK_IMPORTED_MODULE_4__.AllDayRow, {\n                selectedDate: selectedDate,\n                segments: allDaySegments,\n                selectedEvent: selectedEvent,\n                setSelectedEvent: setSelectedEvent,\n                handleEventClick: handleEventClick,\n                canEditData: canEditData,\n                openAddEventForm: openAddEventForm,\n                view: \"week\",\n                activeDragData: activeDragData\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 372,\n                columnNumber: 7\n            }, undefined),\n            weekSegments.length === 0 ? renderEmptyState() : renderTimeSlots()\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n        lineNumber: 336,\n        columnNumber: 5\n    }, undefined);\n};\n_s2(WeekView, \"OfgQ1j/ZHWAQ0Q+mdykk0ntfinw=\");\n_c2 = WeekView;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"TimeSlot\");\n$RefreshReg$(_c1, \"MinuteSegment\");\n$RefreshReg$(_c2, \"WeekView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/WeekView.tsx\n"));

/***/ })

});