"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx":
/*!*******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/MonthView.tsx ***!
  \*******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MonthView: function() { return /* binding */ MonthView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CalendarEventItem */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx\");\n/* harmony import */ var _NoEvents__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./NoEvents */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/NoEvents.tsx\");\n/* harmony import */ var _CalendarSideCard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./CalendarSideCard */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarSideCard.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst DayCell = (param)=>{\n    let { date, children, onClick, isCurrentMonth } = param;\n    _s();\n    const { setNodeRef, isOver } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__.useDroppable)({\n        id: \"daycell-\".concat((0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(date, \"yyyy-MM-dd\")),\n        data: {\n            date: date,\n            type: \"daycell\"\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: setNodeRef,\n        onClick: onClick,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-b border-r border-neutral-300 relative cursor-pointer transition-colors group flex flex-col p-2 min-h-[120px] lg:p-3 lg:min-h-[140px]\", isCurrentMonth ? \"bg-white hover:bg-neutral-50\" : \"bg-neutral-100 hover:bg-neutral-200\", isOver && \"bg-blue-50 border-blue-200\"),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DayCell, \"DmJTTt6A5xWIX/faBiFge3FOLrw=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__.useDroppable\n    ];\n});\n_c = DayCell;\n// New helper function to process events for the entire month with proper slot tracking\nconst useMonthEvents = (weeks, events)=>{\n    _s1();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const positionedEventsByWeek = new Map();\n        const cellSlotUsage = new Map(); // Track events occupying each cell\n        weeks.forEach((week, weekIndex)=>{\n            const weekStart = week[0];\n            const weekEnd = week[6];\n            const weekEvents = events.filter((event)=>{\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                return eventStart <= weekEnd && eventEnd >= weekStart;\n            });\n            const spanningEvents = [];\n            weekEvents.forEach((event)=>{\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                const startDayIndex = week.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(day, eventStart));\n                const endDayIndex = week.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(day, eventEnd));\n                const eventSpansWeek = startDayIndex !== -1 || endDayIndex !== -1 || eventStart < weekStart && eventEnd > weekEnd;\n                if (eventSpansWeek) {\n                    const start = startDayIndex !== -1 ? startDayIndex : 0;\n                    const end = endDayIndex !== -1 ? endDayIndex : 6;\n                    spanningEvents.push({\n                        event,\n                        startDayIndex: start,\n                        endDayIndex: end,\n                        colSpan: end - start + 1\n                    });\n                }\n            });\n            const positioned = [];\n            const rows = [];\n            const sortedEvents = spanningEvents.sort((a, b)=>a.startDayIndex - b.startDayIndex || b.colSpan - a.colSpan);\n            sortedEvents.forEach((event)=>{\n                let assigned = false;\n                for(let i = 0; i < rows.length; i++){\n                    const row = rows[i];\n                    if (!row.some((e)=>event.startDayIndex <= e.endDayIndex && event.endDayIndex >= e.startDayIndex)) {\n                        row.push(event);\n                        positioned.push({\n                            ...event,\n                            row: i\n                        });\n                        assigned = true;\n                        break;\n                    }\n                }\n                if (!assigned) {\n                    rows.push([\n                        event\n                    ]);\n                    positioned.push({\n                        ...event,\n                        row: rows.length - 1\n                    });\n                }\n            });\n            // Track slot usage for each cell in this week\n            positioned.forEach((positionedEvent)=>{\n                for(let dayIndex = positionedEvent.startDayIndex; dayIndex <= positionedEvent.endDayIndex; dayIndex++){\n                    const cellKey = \"\".concat(weekIndex, \"-\").concat(dayIndex);\n                    if (!cellSlotUsage.has(cellKey)) {\n                        cellSlotUsage.set(cellKey, []);\n                    }\n                    cellSlotUsage.get(cellKey).push(positionedEvent);\n                }\n            });\n            positionedEventsByWeek.set(weekIndex, positioned);\n        });\n        return {\n            positionedEventsByWeek,\n            cellSlotUsage\n        };\n    }, [\n        weeks,\n        events\n    ]);\n};\n_s1(useMonthEvents, \"nwk+m61qLgjDVUp4IGV/072DDN4=\");\nconst MonthView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, setSelectedDate, openAddEventForm, canEditData, handleEventClick, activeDragData } = param;\n    _s2();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_5__.useMaybeRecord)();\n    const isInRecordTab = !!maybeRecord;\n    // Memoize month calculations\n    const monthCalculations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const monthStart = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(selectedDate);\n        const monthEnd = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(selectedDate);\n        const startDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(monthStart, {\n            weekStartsOn: 0\n        });\n        const endDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(monthEnd, {\n            weekStartsOn: 0\n        });\n        const days = [];\n        let day = startDay;\n        while(day <= endDay){\n            days.push(day);\n            day = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(day, 1);\n        }\n        const weeks = [];\n        for(let i = 0; i < days.length; i += 7){\n            weeks.push(days.slice(i, i + 7));\n        }\n        return {\n            monthStart,\n            monthEnd,\n            startDay,\n            endDay,\n            days,\n            weeks\n        };\n    }, [\n        selectedDate\n    ]);\n    // Memoize month events\n    const monthEvents = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>events.filter((event)=>{\n            const eventStart = new Date(event.start);\n            return eventStart >= monthCalculations.startDay && eventStart <= monthCalculations.endDay;\n        }), [\n        events,\n        monthCalculations.startDay,\n        monthCalculations.endDay\n    ]);\n    const { positionedEventsByWeek, cellSlotUsage } = useMonthEvents(monthCalculations.weeks, events);\n    // Render empty state when no events\n    const renderEmptyState = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-7 border-b border-neutral-300 bg-white\",\n                    children: [\n                        \"Sunday\",\n                        \"Monday\",\n                        \"Tuesday\",\n                        \"Wednesday\",\n                        \"Thursday\",\n                        \"Friday\",\n                        \"Saturday\"\n                    ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-black\", \"py-2 text-xs\"),\n                            children: dayName.substring(0, 3)\n                        }, dayName, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NoEvents__WEBPACK_IMPORTED_MODULE_7__.NoEvents, {\n                    title: \"No events this month\",\n                    message: \"\".concat((0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate, \"MMMM yyyy\"), \" is completely free. Start planning your month!\"),\n                    showCreateButton: canEditData,\n                    onCreate: ()=>openAddEventForm(selectedDate)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n            lineNumber: 191,\n            columnNumber: 5\n        }, undefined);\n    // Render day cell content\n    const renderDayCellContent = (day, dayEvents, allCellEvents)=>{\n        const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n        const isCurrentDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(day);\n        const MAX_VISIBLE_EVENTS = 4;\n        const ROW_HEIGHT = 28;\n        // Use allCellEvents to determine if we have overflow, but only render dayEvents\n        // This ensures multi-day events consume slots even if they don't render in this cell\n        const sortedEvents = dayEvents.sort((a, b)=>a.row - b.row);\n        const visibleEvents = sortedEvents.slice(0, MAX_VISIBLE_EVENTS);\n        const hasMore = allCellEvents.length > MAX_VISIBLE_EVENTS;\n        // To fix the gap after the \"+more\" link, we must calculate the container's height\n        // instead of letting it expand. The link is placed at the start of the 5th row,\n        // and we'll give it 20px of height.\n        const maxRow = visibleEvents.reduce((max, event)=>Math.max(max, event.row), -1);\n        const containerHeight = hasMore ? MAX_VISIBLE_EVENTS * ROW_HEIGHT + 20 : (maxRow + 1) * ROW_HEIGHT;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center rounded-full font-semibold text-xs w-6 h-6\", isCurrentDay ? \"bg-black text-white\" : isCurrentMonth ? \"text-black hover:bg-neutral-100\" : \"text-neutral-400\"),\n                            children: (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(day, \"d\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, undefined),\n                        canEditData && isCurrentMonth && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            className: \"rounded-full h-6 w-6 p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-neutral-100 hidden lg:inline-flex\",\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                setTimeout(()=>openAddEventForm(day), 150);\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"h-3 w-3 text-black\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 235,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    style: {\n                        height: \"\".concat(containerHeight, \"px\")\n                    },\n                    children: [\n                        visibleEvents.map((pe)=>{\n                            var _activeDragData_payload;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute\",\n                                style: {\n                                    top: \"\".concat(pe.row * ROW_HEIGHT, \"px\"),\n                                    left: \"2px\",\n                                    width: pe.colSpan > 1 ? \"calc(\".concat(pe.colSpan * 100, \"% + \").concat((pe.colSpan - 1) * 19, \"px)\") : \"calc(100% - 4px)\",\n                                    // zIndex: pe.colSpan > 1 ? 10 : 1,\n                                    zIndex: 10 + pe.row\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__.CalendarEventItem, {\n                                    event: pe.event,\n                                    view: \"month\",\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        setSelectedEvent(pe.event.id);\n                                        handleEventClick(pe.event);\n                                    },\n                                    isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === pe.event.id\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, pe.event.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 13\n                            }, undefined);\n                        }),\n                        hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-black hover:text-black font-medium text-xs cursor-pointer\",\n                            style: {\n                                position: \"absolute\",\n                                top: \"\".concat(MAX_VISIBLE_EVENTS * ROW_HEIGHT, \"px\"),\n                                left: \"2px\"\n                            },\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                setSelectedDate(day);\n                            },\n                            children: [\n                                \"+ \",\n                                allCellEvents.length - MAX_VISIBLE_EVENTS,\n                                \" more\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 256,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true);\n    };\n    // Render main view\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-background flex flex-col lg:flex-row\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col min-h-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        \"data-day-headers\": \"true\",\n                        className: \"grid grid-cols-7 border-b border-neutral-300 sticky top-0 z-10 bg-white\",\n                        children: [\n                            \"Sunday\",\n                            \"Monday\",\n                            \"Tuesday\",\n                            \"Wednesday\",\n                            \"Thursday\",\n                            \"Friday\",\n                            \"Saturday\"\n                        ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-black\", \"py-2 text-xs\"),\n                                children: dayName.substring(0, 3)\n                            }, dayName, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n                        className: \"flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-7 border-neutral-300 border-b\",\n                            children: monthCalculations.weeks.map((week, weekIndex)=>week.map((day, dayIndex)=>{\n                                    // Get all events that occupy this cell (both starting and passing through)\n                                    const cellKey = \"\".concat(weekIndex, \"-\").concat(dayIndex);\n                                    const cellEvents = cellSlotUsage.get(cellKey) || [];\n                                    // Filter to only show events that start on this day for rendering\n                                    // (multi-day events are rendered from their start cell but consume slots in all cells)\n                                    const dayEvents = cellEvents.filter((pe)=>pe.startDayIndex === dayIndex);\n                                    const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DayCell, {\n                                        date: day,\n                                        isCurrentMonth: isCurrentMonth,\n                                        onClick: ()=>setSelectedDate(day),\n                                        children: renderDayCellContent(day, dayEvents, cellEvents)\n                                    }, \"\".concat(weekIndex, \"-\").concat(dayIndex), false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 19\n                                    }, undefined);\n                                }))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 308,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarSideCard__WEBPACK_IMPORTED_MODULE_8__.CalendarSideCard, {\n                selectedDate: selectedDate,\n                events: events,\n                selectedEvent: selectedEvent,\n                setSelectedEvent: setSelectedEvent,\n                handleEventClick: handleEventClick\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 354,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 307,\n        columnNumber: 5\n    }, undefined);\n};\n_s2(MonthView, \"sXVDlSg9iY1uQso5u/htz79pNhI=\", false, function() {\n    return [\n        _providers_record__WEBPACK_IMPORTED_MODULE_5__.useMaybeRecord,\n        useMonthEvents\n    ];\n});\n_c1 = MonthView;\nfunction isMultiDay(event) {\n    return !(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(new Date(event.start), new Date(event.end));\n}\nvar _c, _c1;\n$RefreshReg$(_c, \"DayCell\");\n$RefreshReg$(_c1, \"MonthView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx\n"));

/***/ })

});