"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx":
/*!*******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/MonthView.tsx ***!
  \*******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MonthView: function() { return /* binding */ MonthView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfDay,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CalendarEventItem */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx\");\n/* harmony import */ var _NoEvents__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./NoEvents */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/NoEvents.tsx\");\n/* harmony import */ var _CalendarSideCard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./CalendarSideCard */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarSideCard.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Helper function to count events in a cell\nconst getEventsForCell = (date, events, spanningEvents)=>{\n    const cellEvents = [];\n    // Add spanning (multi-day) events that pass through this cell\n    spanningEvents.forEach((spanningEvent)=>{\n        const eventStart = new Date(spanningEvent.segment.originalEvent.start);\n        const eventEnd = new Date(spanningEvent.segment.originalEvent.end);\n        if (date >= (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(eventStart) && date <= (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(eventEnd)) {\n            cellEvents.push({\n                event: spanningEvent.segment.originalEvent,\n                isMultiDay: true,\n                row: spanningEvent.row,\n                colSpan: spanningEvent.colSpan\n            });\n        }\n    });\n    // Add single-day events for this cell\n    events.forEach((event)=>{\n        const eventStart = new Date(event.start);\n        if ((0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(date, eventStart)) {\n            cellEvents.push({\n                event,\n                isMultiDay: false\n            });\n        }\n    });\n    return {\n        visibleEvents: cellEvents.slice(0, 4),\n        hiddenCount: Math.max(0, cellEvents.length - 4)\n    };\n};\n// New helper function to process events for the entire month\nconst useMonthEvents = (weeks, events)=>{\n    _s();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const positionedEventsByWeek = new Map();\n        weeks.forEach((week, weekIndex)=>{\n            const weekStart = week[0];\n            const weekEnd = week[6];\n            const weekEvents = events.filter((event)=>{\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                return eventStart <= weekEnd && eventEnd >= weekStart;\n            });\n            const spanningEvents = [];\n            weekEvents.forEach((event)=>{\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                const startDayIndex = week.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(day, eventStart));\n                const endDayIndex = week.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(day, eventEnd));\n                const eventSpansWeek = startDayIndex !== -1 || endDayIndex !== -1 || eventStart < weekStart && eventEnd > weekEnd;\n                if (eventSpansWeek) {\n                    const start = startDayIndex !== -1 ? startDayIndex : 0;\n                    const end = endDayIndex !== -1 ? endDayIndex : 6;\n                    spanningEvents.push({\n                        event,\n                        startDayIndex: start,\n                        endDayIndex: end,\n                        colSpan: end - start + 1\n                    });\n                }\n            });\n            const positioned = [];\n            const rows = [];\n            const sortedEvents = spanningEvents.sort((a, b)=>a.startDayIndex - b.startDayIndex || b.colSpan - a.colSpan);\n            sortedEvents.forEach((event)=>{\n                let assigned = false;\n                for(let i = 0; i < rows.length; i++){\n                    const row = rows[i];\n                    if (!row.some((e)=>event.startDayIndex <= e.endDayIndex && event.endDayIndex >= e.startDayIndex)) {\n                        row.push(event);\n                        positioned.push({\n                            ...event,\n                            row: i\n                        });\n                        assigned = true;\n                        break;\n                    }\n                }\n                if (!assigned) {\n                    rows.push([\n                        event\n                    ]);\n                    positioned.push({\n                        ...event,\n                        row: rows.length - 1\n                    });\n                }\n            });\n            positionedEventsByWeek.set(weekIndex, positioned);\n        });\n        return positionedEventsByWeek;\n    }, [\n        weeks,\n        events\n    ]);\n};\n_s(useMonthEvents, \"nwk+m61qLgjDVUp4IGV/072DDN4=\");\n// Helper function to check if two events overlap\nconst hasOverlap = (a, b)=>{\n    const aStart = new Date(a.segment.originalEvent.start);\n    const aEnd = new Date(a.segment.originalEvent.end);\n    const bStart = new Date(b.segment.originalEvent.start);\n    const bEnd = new Date(b.segment.originalEvent.end);\n    return aStart <= bEnd && aEnd >= bStart;\n};\n// Helper function to position spanning events\nconst positionSpanningEvents = (events)=>{\n    const positioned = [];\n    const rows = [];\n    // Sort events by their span length (longer events first)\n    const sortedEvents = [\n        ...events\n    ].sort((a, b)=>a.colSpan === b.colSpan ? 0 : b.colSpan - a.colSpan);\n    sortedEvents.forEach((event)=>{\n        let assigned = false;\n        // Try to fit in existing rows\n        for(let i = 0; i < rows.length; i++){\n            const row = rows[i];\n            if (!row.some((e)=>hasOverlap(event, e))) {\n                row.push(event);\n                positioned.push({\n                    ...event,\n                    row: i\n                });\n                assigned = true;\n                break;\n            }\n        }\n        // If no existing row works, create a new row\n        if (!assigned) {\n            rows.push([\n                event\n            ]);\n            positioned.push({\n                ...event,\n                row: rows.length - 1\n            });\n        }\n    });\n    return positioned;\n};\nconst DayCell = (param)=>{\n    let { date, isCurrentMonth, onClick, events, spanningEvents, onEventClick, selectedEvent } = param;\n    _s1();\n    const { setNodeRef, isOver } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__.useDroppable)({\n        id: \"daycell-\".concat((0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(date, \"yyyy-MM-dd\")),\n        data: {\n            type: \"daycell\",\n            date: date\n        }\n    });\n    const { visibleEvents, hiddenCount } = getEventsForCell(date, events, spanningEvents);\n    const isCurrentDay = (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(date);\n    const MAX_VISIBLE_EVENTS = 4;\n    const ROW_HEIGHT = 28;\n    // Calculate container height\n    const maxRow = visibleEvents.reduce((max, event)=>{\n        if (\"row\" in event) {\n            return Math.max(max, event.row || 0);\n        }\n        return max;\n    }, 0);\n    const containerHeight = hiddenCount > 0 ? MAX_VISIBLE_EVENTS * ROW_HEIGHT + 20 : (maxRow + 1) * ROW_HEIGHT;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: setNodeRef,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"group min-h-[120px] p-2 border-b border-r border-neutral-300\", \"cursor-pointer hover:bg-neutral-50 transition-colors\", !isCurrentMonth && \"bg-neutral-50\", isOver && \"bg-blue-50\"),\n        onClick: onClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center rounded-full font-semibold text-xs w-6 h-6\", isCurrentDay ? \"bg-black text-white\" : isCurrentMonth ? \"text-black hover:bg-neutral-100\" : \"text-neutral-400\"),\n                    children: (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(date, \"d\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 240,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                style: {\n                    height: \"\".concat(containerHeight, \"px\")\n                },\n                children: [\n                    visibleEvents.map((eventInfo)=>{\n                        const row = \"row\" in eventInfo && eventInfo.row !== undefined ? eventInfo.row : 0;\n                        const colSpan = \"colSpan\" in eventInfo && eventInfo.colSpan !== undefined ? eventInfo.colSpan : 1;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute\",\n                            style: {\n                                top: \"\".concat(row * ROW_HEIGHT, \"px\"),\n                                left: \"2px\",\n                                width: colSpan > 1 ? \"calc(\".concat(colSpan * 100, \"% + \").concat((colSpan - 1) * 19, \"px)\") : \"calc(100% - 4px)\",\n                                zIndex: 10 + row\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__.CalendarEventItem, {\n                                event: eventInfo.event,\n                                view: \"month\",\n                                onClick: (e)=>{\n                                    e.stopPropagation();\n                                    onEventClick(eventInfo.event);\n                                },\n                                isDragging: false\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 15\n                            }, undefined)\n                        }, eventInfo.event.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 13\n                        }, undefined);\n                    }),\n                    hiddenCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-black hover:text-black font-medium text-xs cursor-pointer\",\n                        style: {\n                            position: \"absolute\",\n                            top: \"\".concat(MAX_VISIBLE_EVENTS * ROW_HEIGHT, \"px\"),\n                            left: \"2px\"\n                        },\n                        onClick: (e)=>{\n                            e.stopPropagation();\n                            onClick();\n                        },\n                        children: [\n                            \"+ \",\n                            hiddenCount,\n                            \" more\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 230,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(DayCell, \"DmJTTt6A5xWIX/faBiFge3FOLrw=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__.useDroppable\n    ];\n});\n_c = DayCell;\nconst MonthView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, setSelectedDate, openAddEventForm, canEditData, handleEventClick, activeDragData } = param;\n    _s2();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_5__.useMaybeRecord)();\n    const isInRecordTab = !!maybeRecord;\n    // Memoize month calculations\n    const monthCalculations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const monthStart = (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(selectedDate);\n        const monthEnd = (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(selectedDate);\n        const startDay = (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(monthStart, {\n            weekStartsOn: 0\n        });\n        const endDay = (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_18__[\"default\"])(monthEnd, {\n            weekStartsOn: 0\n        });\n        const days = [];\n        let day = startDay;\n        while(day <= endDay){\n            days.push(day);\n            day = (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_19__[\"default\"])(day, 1);\n        }\n        const weeks = [];\n        for(let i = 0; i < days.length; i += 7){\n            weeks.push(days.slice(i, i + 7));\n        }\n        return {\n            monthStart,\n            monthEnd,\n            startDay,\n            endDay,\n            days,\n            weeks\n        };\n    }, [\n        selectedDate\n    ]);\n    // Get all events for the visible month range\n    const monthEvents = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>events.filter((event)=>{\n            const eventStart = new Date(event.start);\n            const eventEnd = new Date(event.end);\n            return eventStart <= monthCalculations.endDay && eventEnd >= monthCalculations.startDay;\n        }), [\n        events,\n        monthCalculations.startDay,\n        monthCalculations.endDay\n    ]);\n    // Calculate spanning events for each week\n    const weeklySpanningEvents = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const spanningEventsByWeek = new Map();\n        monthCalculations.weeks.forEach((week, weekIndex)=>{\n            const weekStart = week[0];\n            const weekEnd = week[6];\n            // Filter events that span this week\n            const weekEvents = monthEvents.filter((event)=>{\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                return eventStart <= weekEnd && eventEnd >= weekStart;\n            });\n            // Process spanning events\n            const spanningEvents = [];\n            weekEvents.forEach((event)=>{\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                const startDayIndex = week.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(day, eventStart) || day > eventStart);\n                const endDayIndex = week.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(day, eventEnd) || day > eventEnd);\n                const start = startDayIndex !== -1 ? startDayIndex : 0;\n                const end = endDayIndex !== -1 ? endDayIndex : 6;\n                spanningEvents.push({\n                    segment: {\n                        originalEvent: {\n                            ...event,\n                            startDayIndex: start\n                        }\n                    },\n                    row: 0,\n                    colSpan: end - start + 1\n                });\n            });\n            // Position spanning events in rows\n            const positioned = positionSpanningEvents(spanningEvents);\n            spanningEventsByWeek.set(weekIndex, positioned);\n        });\n        return spanningEventsByWeek;\n    }, [\n        monthEvents,\n        monthCalculations.weeks\n    ]);\n    // Render empty state when no events\n    const renderEmptyState = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-7 border-b border-neutral-300 bg-white\",\n                    children: [\n                        \"Sunday\",\n                        \"Monday\",\n                        \"Tuesday\",\n                        \"Wednesday\",\n                        \"Thursday\",\n                        \"Friday\",\n                        \"Saturday\"\n                    ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-black\", \"py-2 text-xs\"),\n                            children: dayName.substring(0, 3)\n                        }, dayName, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 11\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 402,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NoEvents__WEBPACK_IMPORTED_MODULE_7__.NoEvents, {\n                    title: \"No events this month\",\n                    message: \"\".concat((0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(selectedDate, \"MMMM yyyy\"), \" is completely free. Start planning your month!\"),\n                    showCreateButton: canEditData,\n                    onCreate: ()=>openAddEventForm(selectedDate)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 413,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n            lineNumber: 401,\n            columnNumber: 5\n        }, undefined);\n    // Render day cell content\n    const renderDayCellContent = (day, dayEvents)=>{\n        const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n        const isCurrentDay = (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(day);\n        const MAX_VISIBLE_EVENTS = 4;\n        const ROW_HEIGHT = 28;\n        const sortedEvents = dayEvents.sort((a, b)=>a.row - b.row);\n        const visibleEvents = sortedEvents.slice(0, MAX_VISIBLE_EVENTS);\n        const hasMore = sortedEvents.length > MAX_VISIBLE_EVENTS;\n        // To fix the gap after the \"+more\" link, we must calculate the container's height\n        // instead of letting it expand. The link is placed at the start of the 5th row,\n        // and we'll give it 20px of height.\n        const maxRow = visibleEvents.reduce((max, event)=>Math.max(max, event.row), -1);\n        const containerHeight = hasMore ? MAX_VISIBLE_EVENTS * ROW_HEIGHT + 20 : (maxRow + 1) * ROW_HEIGHT;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center rounded-full font-semibold text-xs w-6 h-6\", isCurrentDay ? \"bg-black text-white\" : isCurrentMonth ? \"text-black hover:bg-neutral-100\" : \"text-neutral-400\"),\n                            children: (0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(day, \"d\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 444,\n                            columnNumber: 11\n                        }, undefined),\n                        canEditData && isCurrentMonth && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            className: \"rounded-full h-6 w-6 p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-neutral-100 hidden lg:inline-flex\",\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                setTimeout(()=>openAddEventForm(day), 150);\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                className: \"h-3 w-3 text-black\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 459,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 451,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 443,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    style: {\n                        height: \"\".concat(containerHeight, \"px\")\n                    },\n                    children: [\n                        visibleEvents.map((pe)=>{\n                            var _activeDragData_payload;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute\",\n                                style: {\n                                    top: \"\".concat(pe.row * ROW_HEIGHT, \"px\"),\n                                    left: \"2px\",\n                                    width: pe.colSpan > 1 ? \"calc(\".concat(pe.colSpan * 100, \"% + \").concat((pe.colSpan - 1) * 19, \"px)\") : \"calc(100% - 4px)\",\n                                    // zIndex: pe.colSpan > 1 ? 10 : 1,\n                                    zIndex: 10 + pe.row\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__.CalendarEventItem, {\n                                    event: pe.event,\n                                    view: \"month\",\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        setSelectedEvent(pe.event.id);\n                                        handleEventClick(pe.event);\n                                    },\n                                    isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === pe.event.id\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                    lineNumber: 479,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, pe.event.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 466,\n                                columnNumber: 13\n                            }, undefined);\n                        }),\n                        hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-black hover:text-black font-medium text-xs cursor-pointer\",\n                            style: {\n                                position: \"absolute\",\n                                top: \"\".concat(MAX_VISIBLE_EVENTS * ROW_HEIGHT, \"px\"),\n                                left: \"2px\"\n                            },\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                setSelectedDate(day);\n                            },\n                            children: [\n                                \"+ \",\n                                sortedEvents.length - MAX_VISIBLE_EVENTS,\n                                \" more\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 493,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 464,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true);\n    };\n    // Render main view\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-background flex flex-col lg:flex-row\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col min-h-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        \"data-day-headers\": \"true\",\n                        className: \"grid grid-cols-7 border-b border-neutral-300 sticky top-0 z-10 bg-white\",\n                        children: [\n                            \"Sunday\",\n                            \"Monday\",\n                            \"Tuesday\",\n                            \"Wednesday\",\n                            \"Thursday\",\n                            \"Friday\",\n                            \"Saturday\"\n                        ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-black\", \"py-2 text-xs\"),\n                                children: dayName.substring(0, 3)\n                            }, dayName, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 523,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 518,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n                        className: \"flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-7 border-neutral-300 border-b\",\n                            children: monthCalculations.weeks.map((week, weekIndex)=>week.map((day, dayIndex)=>{\n                                    const weekEvents = weeklySpanningEvents.get(weekIndex) || [];\n                                    const dayEvents = weekEvents.filter((pe)=>pe.segment.originalEvent.startDayIndex === dayIndex);\n                                    const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DayCell, {\n                                        date: day,\n                                        isCurrentMonth: isCurrentMonth,\n                                        onClick: ()=>setSelectedDate(day),\n                                        events: monthEvents,\n                                        spanningEvents: weekEvents,\n                                        onEventClick: handleEventClick,\n                                        selectedEvent: selectedEvent\n                                    }, \"\".concat(weekIndex, \"-\").concat(dayIndex), false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                        lineNumber: 542,\n                                        columnNumber: 19\n                                    }, undefined);\n                                }))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 534,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 533,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 516,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarSideCard__WEBPACK_IMPORTED_MODULE_8__.CalendarSideCard, {\n                selectedDate: selectedDate,\n                events: events,\n                selectedEvent: selectedEvent,\n                setSelectedEvent: setSelectedEvent,\n                handleEventClick: handleEventClick\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 559,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 515,\n        columnNumber: 5\n    }, undefined);\n};\n_s2(MonthView, \"kPDDlaHW0EW5WI3Ne0bDTWgAARs=\", false, function() {\n    return [\n        _providers_record__WEBPACK_IMPORTED_MODULE_5__.useMaybeRecord\n    ];\n});\n_c1 = MonthView;\nfunction isMultiDay(event) {\n    return !(0,_barrel_optimize_names_addDays_endOfDay_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(new Date(event.start), new Date(event.end));\n}\nvar _c, _c1;\n$RefreshReg$(_c, \"DayCell\");\n$RefreshReg$(_c1, \"MonthView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx\n"));

/***/ })

});