"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx":
/*!*******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/MonthView.tsx ***!
  \*******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MonthView: function() { return /* binding */ MonthView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CalendarEventItem */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx\");\n/* harmony import */ var _NoEvents__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./NoEvents */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/NoEvents.tsx\");\n/* harmony import */ var _CalendarSideCard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./CalendarSideCard */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarSideCard.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst DayCell = (param)=>{\n    let { date, children, onClick, isCurrentMonth } = param;\n    _s();\n    const { setNodeRef, isOver } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__.useDroppable)({\n        id: \"daycell-\".concat((0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(date, \"yyyy-MM-dd\")),\n        data: {\n            date: date,\n            type: \"daycell\"\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: setNodeRef,\n        onClick: onClick,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-b border-r border-neutral-300 relative cursor-pointer transition-colors group flex flex-col p-2 min-h-[120px] lg:p-3 lg:min-h-[140px]\", isCurrentMonth ? \"bg-white hover:bg-neutral-50\" : \"bg-neutral-100 hover:bg-neutral-200\", isOver && \"bg-blue-50 border-blue-200\"),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DayCell, \"DmJTTt6A5xWIX/faBiFge3FOLrw=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__.useDroppable\n    ];\n});\n_c = DayCell;\n// Helper function to check if an event affects a specific day\nconst eventAffectsDay = (event, day)=>{\n    const eventStart = new Date(event.start);\n    const eventEnd = new Date(event.end);\n    const dayStart = new Date(day);\n    const dayEnd = new Date(day);\n    dayEnd.setHours(23, 59, 59, 999);\n    return eventStart <= dayEnd && eventEnd >= dayStart;\n};\n// New helper function to process events for the entire month with proper slot tracking\nconst useMonthEvents = (weeks, events)=>{\n    _s1();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const positionedEventsByWeek = new Map();\n        const slotUsageByDay = new Map(); // Track slot usage per day\n        weeks.forEach((week, weekIndex)=>{\n            const weekStart = week[0];\n            const weekEnd = week[6];\n            // Get all events that intersect with this week\n            const weekEvents = events.filter((event)=>{\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                return eventStart <= weekEnd && eventEnd >= weekStart;\n            });\n            const spanningEvents = [];\n            weekEvents.forEach((event)=>{\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                // Find which days this event spans in this week\n                const startDayIndex = week.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(day, eventStart));\n                const endDayIndex = week.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(day, eventEnd));\n                // Calculate actual span within this week\n                let actualStart = 0;\n                let actualEnd = 6;\n                // For events that start in this week\n                if (startDayIndex !== -1) {\n                    actualStart = startDayIndex;\n                }\n                // For events that end in this week\n                if (endDayIndex !== -1) {\n                    actualEnd = endDayIndex;\n                }\n                // For events that span through this week entirely\n                if (startDayIndex === -1 && endDayIndex === -1 && eventStart < weekStart && eventEnd > weekEnd) {\n                    actualStart = 0;\n                    actualEnd = 6;\n                }\n                // Check if event intersects with this week\n                const eventSpansWeek = startDayIndex !== -1 || endDayIndex !== -1 || eventStart < weekStart && eventEnd > weekEnd;\n                if (eventSpansWeek) {\n                    spanningEvents.push({\n                        event,\n                        startDayIndex: actualStart,\n                        endDayIndex: actualEnd,\n                        colSpan: actualEnd - actualStart + 1\n                    });\n                }\n            });\n            // Sort events by start day, then by span length (longer events first)\n            const sortedEvents = spanningEvents.sort((a, b)=>{\n                if (a.startDayIndex !== b.startDayIndex) {\n                    return a.startDayIndex - b.startDayIndex;\n                }\n                return b.colSpan - a.colSpan;\n            });\n            // Position events and track slot usage\n            const positioned = [];\n            const rows = [];\n            sortedEvents.forEach((eventData)=>{\n                // Check if this event can be placed (all days it spans have available slots)\n                const affectedDays = week.slice(eventData.startDayIndex, eventData.endDayIndex + 1);\n                const canPlace = affectedDays.every((day)=>{\n                    const dayKey = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(day, \"yyyy-MM-dd\");\n                    const currentUsage = slotUsageByDay.get(dayKey) || 0;\n                    return currentUsage < 4; // Maximum 4 slots per day\n                });\n                if (!canPlace) {\n                    // Event cannot be placed, skip it (it will be in the \"+more\" count)\n                    return;\n                }\n                // Find available row\n                let assigned = false;\n                for(let i = 0; i < rows.length; i++){\n                    const row = rows[i];\n                    const hasConflict = row.some((existingEvent)=>eventData.startDayIndex <= existingEvent.endDayIndex && eventData.endDayIndex >= existingEvent.startDayIndex);\n                    if (!hasConflict) {\n                        row.push(eventData);\n                        positioned.push({\n                            ...eventData,\n                            row: i\n                        });\n                        assigned = true;\n                        break;\n                    }\n                }\n                if (!assigned) {\n                    rows.push([\n                        eventData\n                    ]);\n                    positioned.push({\n                        ...eventData,\n                        row: rows.length - 1\n                    });\n                }\n                // Update slot usage for all affected days\n                affectedDays.forEach((day)=>{\n                    const dayKey = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(day, \"yyyy-MM-dd\");\n                    const currentUsage = slotUsageByDay.get(dayKey) || 0;\n                    slotUsageByDay.set(dayKey, currentUsage + 1);\n                });\n            });\n            positionedEventsByWeek.set(weekIndex, positioned);\n        });\n        return {\n            positionedEventsByWeek,\n            slotUsageByDay\n        };\n    }, [\n        weeks,\n        events\n    ]);\n};\n_s1(useMonthEvents, \"nwk+m61qLgjDVUp4IGV/072DDN4=\");\nconst MonthView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, setSelectedDate, openAddEventForm, canEditData, handleEventClick, activeDragData } = param;\n    _s2();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_5__.useMaybeRecord)();\n    const isInRecordTab = !!maybeRecord;\n    // Memoize month calculations\n    const monthCalculations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const monthStart = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(selectedDate);\n        const monthEnd = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(selectedDate);\n        const startDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(monthStart, {\n            weekStartsOn: 0\n        });\n        const endDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(monthEnd, {\n            weekStartsOn: 0\n        });\n        const days = [];\n        let day = startDay;\n        while(day <= endDay){\n            days.push(day);\n            day = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(day, 1);\n        }\n        const weeks = [];\n        for(let i = 0; i < days.length; i += 7){\n            weeks.push(days.slice(i, i + 7));\n        }\n        return {\n            monthStart,\n            monthEnd,\n            startDay,\n            endDay,\n            days,\n            weeks\n        };\n    }, [\n        selectedDate\n    ]);\n    // Memoize month events\n    const monthEvents = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>events.filter((event)=>{\n            const eventStart = new Date(event.start);\n            const eventEnd = new Date(event.end);\n            return eventStart <= monthCalculations.endDay && eventEnd >= monthCalculations.startDay;\n        }), [\n        events,\n        monthCalculations.startDay,\n        monthCalculations.endDay\n    ]);\n    const { positionedEventsByWeek, slotUsageByDay } = useMonthEvents(monthCalculations.weeks, monthEvents);\n    // Render empty state when no events\n    const renderEmptyState = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-7 border-b border-neutral-300 bg-white\",\n                    children: [\n                        \"Sunday\",\n                        \"Monday\",\n                        \"Tuesday\",\n                        \"Wednesday\",\n                        \"Thursday\",\n                        \"Friday\",\n                        \"Saturday\"\n                    ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-black\", \"py-2 text-xs\"),\n                            children: dayName.substring(0, 3)\n                        }, dayName, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NoEvents__WEBPACK_IMPORTED_MODULE_7__.NoEvents, {\n                    title: \"No events this month\",\n                    message: \"\".concat((0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate, \"MMMM yyyy\"), \" is completely free. Start planning your month!\"),\n                    showCreateButton: canEditData,\n                    onCreate: ()=>openAddEventForm(selectedDate)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 263,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n            lineNumber: 251,\n            columnNumber: 5\n        }, undefined);\n    // Render day cell content\n    const renderDayCellContent = (day, dayEvents)=>{\n        const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n        const isCurrentDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(day);\n        const MAX_VISIBLE_EVENTS = 4;\n        const ROW_HEIGHT = 28;\n        const dayKey = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(day, \"yyyy-MM-dd\");\n        // Get all events that affect this day (for the +more count)\n        const allDayEvents = monthEvents.filter((event)=>eventAffectsDay(event, day));\n        const totalEventsForDay = allDayEvents.length;\n        const sortedEvents = dayEvents.sort((a, b)=>a.row - b.row);\n        const visibleEvents = sortedEvents.slice(0, MAX_VISIBLE_EVENTS);\n        const hasMore = totalEventsForDay > MAX_VISIBLE_EVENTS;\n        const hiddenEventsCount = Math.max(0, totalEventsForDay - MAX_VISIBLE_EVENTS);\n        // Calculate container height\n        const maxRow = visibleEvents.reduce((max, event)=>Math.max(max, event.row), -1);\n        const containerHeight = hasMore ? MAX_VISIBLE_EVENTS * ROW_HEIGHT + 20 : (maxRow + 1) * ROW_HEIGHT;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center rounded-full font-semibold text-xs w-6 h-6\", isCurrentDay ? \"bg-black text-white\" : isCurrentMonth ? \"text-black hover:bg-neutral-100\" : \"text-neutral-400\"),\n                            children: (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(day, \"d\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 11\n                        }, undefined),\n                        canEditData && isCurrentMonth && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            className: \"rounded-full h-6 w-6 p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-neutral-100 hidden lg:inline-flex\",\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                setTimeout(()=>openAddEventForm(day), 150);\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"h-3 w-3 text-black\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 298,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    style: {\n                        height: \"\".concat(containerHeight, \"px\")\n                    },\n                    children: [\n                        visibleEvents.map((pe)=>{\n                            var _activeDragData_payload;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute\",\n                                style: {\n                                    top: \"\".concat(pe.row * ROW_HEIGHT, \"px\"),\n                                    left: \"2px\",\n                                    width: pe.colSpan > 1 ? \"calc(\".concat(pe.colSpan * 100, \"% + \").concat((pe.colSpan - 1) * 19, \"px)\") : \"calc(100% - 4px)\",\n                                    zIndex: 10 + pe.row\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__.CalendarEventItem, {\n                                    event: pe.event,\n                                    view: \"month\",\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        setSelectedEvent(pe.event.id);\n                                        handleEventClick(pe.event);\n                                    },\n                                    isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === pe.event.id\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, pe.event.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 13\n                            }, undefined);\n                        }),\n                        hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-black hover:text-black font-medium text-xs cursor-pointer\",\n                            style: {\n                                position: \"absolute\",\n                                top: \"\".concat(MAX_VISIBLE_EVENTS * ROW_HEIGHT, \"px\"),\n                                left: \"2px\"\n                            },\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                setSelectedDate(day);\n                            },\n                            children: [\n                                \"+ \",\n                                hiddenEventsCount,\n                                \" more\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 319,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true);\n    };\n    // Render main view\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-background flex flex-col lg:flex-row\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col min-h-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        \"data-day-headers\": \"true\",\n                        className: \"grid grid-cols-7 border-b border-neutral-300 sticky top-0 z-10 bg-white\",\n                        children: [\n                            \"Sunday\",\n                            \"Monday\",\n                            \"Tuesday\",\n                            \"Wednesday\",\n                            \"Thursday\",\n                            \"Friday\",\n                            \"Saturday\"\n                        ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-black\", \"py-2 text-xs\"),\n                                children: dayName.substring(0, 3)\n                            }, dayName, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 372,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n                        className: \"flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-7 border-neutral-300 border-b\",\n                            children: monthCalculations.weeks.map((week, weekIndex)=>week.map((day, dayIndex)=>{\n                                    const weekEvents = positionedEventsByWeek.get(weekIndex) || [];\n                                    // Get events that start on this day OR span through this day\n                                    const dayEvents = weekEvents.filter((pe)=>pe.startDayIndex === dayIndex || pe.startDayIndex < dayIndex && pe.endDayIndex >= dayIndex);\n                                    const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DayCell, {\n                                        date: day,\n                                        isCurrentMonth: isCurrentMonth,\n                                        onClick: ()=>setSelectedDate(day),\n                                        children: renderDayCellContent(day, dayEvents)\n                                    }, \"\".concat(weekIndex, \"-\").concat(dayIndex), false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 19\n                                    }, undefined);\n                                }))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 388,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 387,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 370,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarSideCard__WEBPACK_IMPORTED_MODULE_8__.CalendarSideCard, {\n                selectedDate: selectedDate,\n                events: events,\n                selectedEvent: selectedEvent,\n                setSelectedEvent: setSelectedEvent,\n                handleEventClick: handleEventClick\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 415,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 369,\n        columnNumber: 5\n    }, undefined);\n};\n_s2(MonthView, \"t9SuGQQKM9r/+gjBud8WIP3gfyg=\", false, function() {\n    return [\n        _providers_record__WEBPACK_IMPORTED_MODULE_5__.useMaybeRecord,\n        useMonthEvents\n    ];\n});\n_c1 = MonthView;\nfunction isMultiDay(event) {\n    return !(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(new Date(event.start), new Date(event.end));\n} // import React, { useMemo } from 'react';\n // import { format, startOfMonth, endOfMonth, startOfWeek, endOfWeek, addDays, isSameDay, isToday } from 'date-fns';\n // import { cn } from '@/lib/utils';\n // import { Button } from '@/components/ui/button';\n // import { PlusIcon } from '@heroicons/react/24/outline';\n // import { ScrollArea } from '@/components/ui/scroll-area';\n // import { CalendarEvent } from '@/typings/page';\n // import { useMaybeRecord } from '@/providers/record';\n // import { CalendarEventItem } from './CalendarEventItem';\n // import { NoEvents } from './NoEvents';\n // import { CalendarSideCard } from './CalendarSideCard';\n // import { useDroppable } from '@dnd-kit/core';\n // interface MonthViewProps {\n //   selectedDate: Date;\n //   events: CalendarEvent[];\n //   selectedEvent: string | null;\n //   setSelectedEvent: (id: string) => void;\n //   setSelectedDate: (date: Date) => void;\n //   openAddEventForm: (date: Date) => void;\n //   canEditData: boolean;\n //   handleEventClick: (event: CalendarEvent) => void;\n //   activeDragData: any;\n // }\n // const DayCell = ({\n //   date,\n //   children,\n //   onClick,\n //   isCurrentMonth\n // }: {\n //   date: Date;\n //   children: React.ReactNode;\n //   onClick: () => void;\n //   isCurrentMonth: boolean;\n // }) => {\n //   const { setNodeRef, isOver } = useDroppable({\n //     id: `daycell-${format(date, 'yyyy-MM-dd')}`,\n //     data: {\n //       date: date,\n //       type: 'daycell'\n //     }\n //   });\n //   return (\n //     <div\n //       ref={setNodeRef}\n //       onClick={onClick}\n //       className={cn(\n //         \"border-b border-r border-neutral-300 relative cursor-pointer transition-colors group flex flex-col p-2 min-h-[120px] lg:p-3 lg:min-h-[140px]\",\n //         isCurrentMonth\n //           ? \"bg-white hover:bg-neutral-50\"\n //           : \"bg-neutral-100 hover:bg-neutral-200\",\n //         isOver && \"bg-blue-50 border-blue-200\"\n //       )}\n //     >\n //       {children}\n //     </div>\n //   );\n // };\n // // Helper function to check if an event affects a specific day\n // const eventAffectsDay = (event: CalendarEvent, day: Date): boolean => {\n //   const eventStart = new Date(event.start);\n //   const eventEnd = new Date(event.end);\n //   const dayStart = new Date(day);\n //   const dayEnd = new Date(day);\n //   dayEnd.setHours(23, 59, 59, 999);\n //   return eventStart <= dayEnd && eventEnd >= dayStart;\n // };\n // // New helper function to process events for the entire month with proper slot tracking\n // const useMonthEvents = (weeks: Date[][], events: CalendarEvent[]) => {\n //   return useMemo(() => {\n //     const positionedEventsByWeek = new Map<number, any[]>();\n //     const slotUsageByDay = new Map<string, number>(); // Track slot usage per day\n //     weeks.forEach((week, weekIndex) => {\n //       const weekStart = week[0];\n //       const weekEnd = week[6];\n //       // Get all events that intersect with this week\n //       const weekEvents = events.filter(event => {\n //         const eventStart = new Date(event.start);\n //         const eventEnd = new Date(event.end);\n //         return eventStart <= weekEnd && eventEnd >= weekStart;\n //       });\n //       const spanningEvents: any[] = [];\n //       weekEvents.forEach(event => {\n //         const eventStart = new Date(event.start);\n //         const eventEnd = new Date(event.end);\n //         // Find which days this event spans in this week\n //         const startDayIndex = week.findIndex(day => isSameDay(day, eventStart));\n //         const endDayIndex = week.findIndex(day => isSameDay(day, eventEnd));\n //         // Calculate actual span within this week\n //         let actualStart = 0;\n //         let actualEnd = 6;\n //         if (startDayIndex !== -1) {\n //           actualStart = startDayIndex;\n //         }\n //         if (endDayIndex !== -1) {\n //           actualEnd = endDayIndex;\n //         }\n //         // Check if event intersects with this week\n //         const eventSpansWeek = startDayIndex !== -1 || endDayIndex !== -1 || (eventStart < weekStart && eventEnd > weekEnd);\n //         if (eventSpansWeek) {\n //           spanningEvents.push({\n //             event,\n //             startDayIndex: actualStart,\n //             endDayIndex: actualEnd,\n //             colSpan: actualEnd - actualStart + 1,\n //           });\n //         }\n //       });\n //       // Sort events by start day, then by span length (longer events first)\n //       const sortedEvents = spanningEvents.sort((a, b) => {\n //         if (a.startDayIndex !== b.startDayIndex) {\n //           return a.startDayIndex - b.startDayIndex;\n //         }\n //         return b.colSpan - a.colSpan;\n //       });\n //       // Position events and track slot usage\n //       const positioned: any[] = [];\n //       const rows: any[][] = [];\n //       sortedEvents.forEach(eventData => {\n //         // Check if this event can be placed (all days it spans have available slots)\n //         const affectedDays = week.slice(eventData.startDayIndex, eventData.endDayIndex + 1);\n //         const canPlace = affectedDays.every(day => {\n //           const dayKey = format(day, 'yyyy-MM-dd');\n //           const currentUsage = slotUsageByDay.get(dayKey) || 0;\n //           return currentUsage < 4; // Maximum 4 slots per day\n //         });\n //         if (!canPlace) {\n //           // Event cannot be placed, skip it (it will be in the \"+more\" count)\n //           return;\n //         }\n //         // Find available row\n //         let assigned = false;\n //         for (let i = 0; i < rows.length; i++) {\n //           const row = rows[i];\n //           const hasConflict = row.some(existingEvent => \n //             eventData.startDayIndex <= existingEvent.endDayIndex && \n //             eventData.endDayIndex >= existingEvent.startDayIndex\n //           );\n //           if (!hasConflict) {\n //             row.push(eventData);\n //             positioned.push({ ...eventData, row: i });\n //             assigned = true;\n //             break;\n //           }\n //         }\n //         if (!assigned) {\n //           rows.push([eventData]);\n //           positioned.push({ ...eventData, row: rows.length - 1 });\n //         }\n //         // Update slot usage for all affected days\n //         affectedDays.forEach(day => {\n //           const dayKey = format(day, 'yyyy-MM-dd');\n //           const currentUsage = slotUsageByDay.get(dayKey) || 0;\n //           slotUsageByDay.set(dayKey, currentUsage + 1);\n //         });\n //       });\n //       positionedEventsByWeek.set(weekIndex, positioned);\n //     });\n //     return { positionedEventsByWeek, slotUsageByDay };\n //   }, [weeks, events]);\n // };\n // export const MonthView: React.FC<MonthViewProps> = ({\n //   selectedDate,\n //   events,\n //   selectedEvent,\n //   setSelectedEvent,\n //   setSelectedDate,\n //   openAddEventForm,\n //   canEditData,\n //   handleEventClick,\n //   activeDragData,\n // }) => {\n //   const maybeRecord = useMaybeRecord();\n //   const isInRecordTab = !!maybeRecord;\n //   // Memoize month calculations\n //   const monthCalculations = useMemo(() => {\n //     const monthStart = startOfMonth(selectedDate);\n //     const monthEnd = endOfMonth(selectedDate);\n //     const startDay = startOfWeek(monthStart, { weekStartsOn: 0 });\n //     const endDay = endOfWeek(monthEnd, { weekStartsOn: 0 });\n //     const days = [];\n //     let day = startDay;\n //     while (day <= endDay) {\n //       days.push(day);\n //       day = addDays(day, 1);\n //     }\n //     const weeks = [];\n //     for (let i = 0; i < days.length; i += 7) {\n //       weeks.push(days.slice(i, i + 7));\n //     }\n //     return { monthStart, monthEnd, startDay, endDay, days, weeks };\n //   }, [selectedDate]);\n //   // Memoize month events\n //   const monthEvents = useMemo(() => \n //     events.filter(event => {\n //       const eventStart = new Date(event.start);\n //       const eventEnd = new Date(event.end);\n //       return eventStart <= monthCalculations.endDay && \n //              eventEnd >= monthCalculations.startDay;\n //     }), \n //     [events, monthCalculations.startDay, monthCalculations.endDay]\n //   );\n //   const { positionedEventsByWeek, slotUsageByDay } = useMonthEvents(monthCalculations.weeks, monthEvents);\n //   // Render empty state when no events\n //   const renderEmptyState = () => (\n //     <div className=\"flex flex-col h-full bg-background\">\n //       <div className=\"grid grid-cols-7 border-b border-neutral-300 bg-white\">\n //         {['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'].map(dayName => (\n //           <div key={dayName} className={cn(\n //             \"text-center font-semibold text-black\",\n //             \"py-2 text-xs\"\n //           )}>\n //             {dayName.substring(0, 3)}\n //           </div>\n //         ))}\n //       </div>\n //       <NoEvents\n //         title=\"No events this month\"\n //         message={`${format(selectedDate, 'MMMM yyyy')} is completely free. Start planning your month!`}\n //         showCreateButton={canEditData}\n //         onCreate={() => openAddEventForm(selectedDate)}\n //       />\n //     </div>\n //   );\n //   // Render day cell content\n //   const renderDayCellContent = (day: Date, dayEvents: any[]) => {\n //     const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n //     const isCurrentDay = isToday(day);\n //     const MAX_VISIBLE_EVENTS = 4;\n //     const ROW_HEIGHT = 28;\n //     const dayKey = format(day, 'yyyy-MM-dd');\n //     // Get all events that affect this day (for the +more count)\n //     const allDayEvents = monthEvents.filter(event => eventAffectsDay(event, day));\n //     const totalEventsForDay = allDayEvents.length;\n //     const sortedEvents = dayEvents.sort((a, b) => a.row - b.row);\n //     const visibleEvents = sortedEvents.slice(0, MAX_VISIBLE_EVENTS);\n //     const hasMore = totalEventsForDay > MAX_VISIBLE_EVENTS;\n //     const hiddenEventsCount = Math.max(0, totalEventsForDay - MAX_VISIBLE_EVENTS);\n //     // Calculate container height\n //     const maxRow = visibleEvents.reduce((max, event) => Math.max(max, event.row), -1);\n //     const containerHeight = hasMore\n //       ? (MAX_VISIBLE_EVENTS * ROW_HEIGHT) + 20 \n //       : (maxRow + 1) * ROW_HEIGHT;\n //     return (\n //       <>\n //         <div className=\"flex items-center justify-between mb-2\">\n //           <span className={cn(\n //             \"inline-flex items-center justify-center rounded-full font-semibold text-xs w-6 h-6\",\n //             isCurrentDay ? \"bg-black text-white\" : isCurrentMonth ? \"text-black hover:bg-neutral-100\" : \"text-neutral-400\"\n //           )}>\n //             {format(day, 'd')}\n //           </span>\n //           {canEditData && isCurrentMonth && (\n //             <Button\n //               variant=\"ghost\"\n //               className=\"rounded-full h-6 w-6 p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-neutral-100 hidden lg:inline-flex\"\n //               onClick={(e) => {\n //                 e.stopPropagation();\n //                 openAddEventForm(day);\n //               }}\n //             >\n //               <PlusIcon className=\"h-3 w-3 text-black\" />\n //             </Button>\n //           )}\n //         </div>\n //         <div className=\"relative\" style={{ height: `${containerHeight}px` }}>\n //           {visibleEvents.map(pe => (\n //             <div\n //               key={pe.event.id}\n //               className=\"absolute\"\n //               style={{\n //                 top: `${pe.row * ROW_HEIGHT}px`,\n //                 left: '2px',\n //                 width: pe.colSpan > 1 \n //                   ? `calc(${pe.colSpan * 100}% + ${(pe.colSpan - 1) * 19}px)`\n //                   : 'calc(100% - 4px)',\n //                 zIndex: 10 + pe.row,\n //               }}\n //             >\n //               <CalendarEventItem\n //                 event={pe.event}\n //                 view=\"month\"\n //                 onClick={(e) => {\n //                   e.stopPropagation();\n //                   setSelectedEvent(pe.event.id);\n //                   handleEventClick(pe.event);\n //                 }}\n //                 isDragging={activeDragData?.payload?.id === pe.event.id}\n //               />\n //             </div>\n //           ))}\n //           {hasMore && (\n //             <div \n //               className=\"text-black hover:text-black font-medium text-xs cursor-pointer\"\n //               style={{\n //                 position: 'absolute',\n //                 top: `${MAX_VISIBLE_EVENTS * ROW_HEIGHT}px`,\n //                 left: '2px',\n //               }}\n //               onClick={(e) => {\n //                 e.stopPropagation();\n //                 setSelectedDate(day);\n //               }}\n //             >\n //               + {hiddenEventsCount} more\n //             </div>\n //           )}\n //         </div>\n //       </>\n //     );\n //   };\n //   // Render main view\n //   return (\n //     <div className=\"h-full bg-background flex flex-col lg:flex-row\">\n //       <div className=\"flex-1 flex flex-col min-h-0\">\n //         {/* Day Headers */}\n //         <div \n //           data-day-headers=\"true\"\n //           className=\"grid grid-cols-7 border-b border-neutral-300 sticky top-0 z-10 bg-white\"\n //         >\n //           {['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'].map(dayName => (\n //             <div key={dayName} className={cn(\n //               \"text-center font-semibold text-black\",\n //               \"py-2 text-xs\"\n //             )}>\n //               {dayName.substring(0, 3)}\n //             </div>\n //           ))}\n //         </div>\n //         {/* Month Grid */}\n //         <ScrollArea className=\"flex-1\">\n //           <div className=\"grid grid-cols-7 border-neutral-300 border-b\">\n //             {monthCalculations.weeks.map((week, weekIndex) =>\n //               week.map((day, dayIndex) => {\n //                 const weekEvents = positionedEventsByWeek.get(weekIndex) || [];\n //                 const dayEvents = weekEvents.filter(pe => pe.startDayIndex === dayIndex);\n //                 const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n //                 return (\n //                   <DayCell\n //                     key={`${weekIndex}-${dayIndex}`}\n //                     date={day}\n //                     isCurrentMonth={isCurrentMonth}\n //                     onClick={() => setSelectedDate(day)}\n //                   >\n //                     {renderDayCellContent(day, dayEvents)}\n //                   </DayCell>\n //                 );\n //               }),\n //             )}\n //           </div>\n //         </ScrollArea>\n //       </div>\n //       <CalendarSideCard\n //         selectedDate={selectedDate}\n //         events={events}\n //         selectedEvent={selectedEvent}\n //         setSelectedEvent={setSelectedEvent}\n //         handleEventClick={handleEventClick}\n //       />\n //     </div>\n //   );\n // };\n // function isMultiDay(event: CalendarEvent): boolean {\n //   return !isSameDay(new Date(event.start), new Date(event.end));\n // }\nvar _c, _c1;\n$RefreshReg$(_c, \"DayCell\");\n$RefreshReg$(_c1, \"MonthView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx\n"));

/***/ })

});