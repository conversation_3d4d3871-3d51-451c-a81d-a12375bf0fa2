/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/WeekView.tsx":
/*!******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/WeekView.tsx ***!
  \******************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// import React, { useMemo } from 'react';\n// import { format, startOfWeek, endOfWeek, isToday, isSameDay, addDays, setHours } from 'date-fns';\n// import { cn } from '@/lib/utils';\n// import { Button } from '@/components/ui/button';\n// import { CalendarEvent } from '@/typings/page';\n// import { CalendarEventItem } from './CalendarEventItem';\n// import { CalendarEventSegment } from './CalendarEventSegment';\n// import { AllDayRow } from './AllDayRow';\n// import { NoEvents } from './NoEvents';\n// import { eventsToSegments, \n//   getSegmentsForWeek, \n//   getSegmentsForDay, \n//   getAllDaySegments, \n//   getTimeSlotSegments, \n//   getSegmentHeight, \n//   getSegmentTopOffset,\n//   EventSegment\n// } from '@/utils/multiDayEventUtils';\n// import { calculateLayout } from '@/utils/eventCollisionUtils';\n// import { useDroppable } from '@dnd-kit/core';\n// import { MoreIndicator } from './MoreIndicator';\n// interface WeekViewProps {\n//   selectedDate: Date;\n//   events: CalendarEvent[];\n//   selectedEvent: string | null;\n//   setSelectedEvent: (id: string) => void;\n//   setSelectedDate: (date: Date) => void;\n//   openAddEventForm: (date: Date) => void;\n//   canEditData: boolean;\n//   savedScrollTop: React.MutableRefObject<number>;\n//   handleEventClick: (event: CalendarEvent) => void;\n//   activeDragData: any;\n// }\n// const TimeSlot = ({\n//   day,\n//   hour,\n//   children,\n//   onDoubleClick\n// }: {\n//   day: Date;\n//   hour: number;\n//   children: React.ReactNode;\n//   onDoubleClick: (minute: number) => void;\n// }) => {\n//   // Create 60 minute segments for precise dropping\n//   const minuteSegments = Array.from({ length: 60 }, (_, i) => i);\n//   return (\n//     <div\n//       className={cn(\n//         \"flex-1 border-r border-neutral-300 last:border-r-0 relative min-h-[60px] cursor-pointer\",\n//       )}\n//     >\n//       {/* Render invisible minute segments */}\n//       <div className=\"absolute inset-0 flex flex-col\">\n//         {minuteSegments.map((minute) => (\n//           <MinuteSegment\n//             key={minute}\n//             day={day}\n//             hour={hour}\n//             minute={minute}\n//             style={{\n//               height: `${100/60}%`,  // Each segment takes 1/60th of the hour cell\n//               top: `${(minute/60) * 100}%`\n//             }}\n//             onDoubleClick={() => onDoubleClick(minute)}\n//           />\n//         ))}\n//       </div>\n//       {children}\n//     </div>\n//   );\n// };\n// // New component for minute-level droppable segments\n// const MinuteSegment = ({\n//   day,\n//   hour,\n//   minute,\n//   style,\n//   onDoubleClick\n// }: {\n//   day: Date;\n//   hour: number;\n//   minute: number;\n//   style: React.CSSProperties;\n//   onDoubleClick: () => void;\n// }) => {\n//   const { setNodeRef, isOver } = useDroppable({\n//     id: `timeslot-${format(day, 'yyyy-MM-dd')}-${hour}-${minute}`,\n//     data: {\n//       date: day,\n//       hour,\n//       minute,\n//       type: 'timeslot-minute'\n//     }\n//   });\n//   return (\n//     <div\n//       ref={setNodeRef}\n//       className={cn(\n//         \"absolute w-full\",\n//         isOver && \"bg-blue-50\"\n//       )}\n//       style={style}\n//       onDoubleClick={onDoubleClick}\n//     />\n//   );\n// };\n// export const WeekView: React.FC<WeekViewProps> = ({\n//   selectedDate,\n//   events,\n//   selectedEvent,\n//   setSelectedEvent,\n//   setSelectedDate,\n//   openAddEventForm,\n//   canEditData,\n//   savedScrollTop,\n//   handleEventClick,\n//   activeDragData,\n// }) => {\n//   // Memoize week-related calculations\n//   const weekCalculations = useMemo(() => {\n//     const weekStart = startOfWeek(selectedDate, { weekStartsOn: 0 });\n//     const weekEnd = endOfWeek(selectedDate, { weekStartsOn: 0 });\n//     const days = Array.from({ length: 7 }, (_, i) => addDays(weekStart, i));\n//     const todayIndex = days.findIndex(day => isToday(day));\n//     return { \n//       weekStart, \n//       weekEnd, \n//       days, \n//       todayIndex \n//     };\n//   }, [selectedDate]);\n//   const { days, todayIndex } = weekCalculations;\n//   const hours = Array.from({ length: 24 }, (_, i) => i);\n//   // Memoize week segments\n//   const weekSegments = useMemo(() => {\n//     const allSegments = eventsToSegments(events);\n//     return getSegmentsForWeek(allSegments, weekCalculations.weekStart, weekCalculations.weekEnd);\n//   }, [events, weekCalculations.weekStart, weekCalculations.weekEnd]);\n//   // Separate all-day and time-slot segments\n//   const allDaySegments = useMemo(() => getAllDaySegments(weekSegments), [weekSegments]);\n//   const timeSlotSegments = useMemo(() => getTimeSlotSegments(weekSegments), [weekSegments]);\n//   // Memoize current time position\n//   const currentTimePosition = useMemo(() => \n//     todayIndex !== -1 \n//       ? {\n//           dayIndex: todayIndex,\n//           hour: new Date().getHours(),\n//           minutes: new Date().getMinutes()\n//         } \n//       : null, \n//     [todayIndex]\n//   );\n//   // Helper to get event duration in minutes\n//   const getEventDurationInMinutes = (event: CalendarEvent): number => {\n//     const start = new Date(event.start);\n//     const end = new Date(event.end);\n//     return Math.max(20, (end.getTime() - start.getTime()) / (1000 * 60));\n//   };\n//   // Render empty state when no events\n//   const renderEmptyState = () => (\n//     <NoEvents\n//       title=\"No events this week\"\n//       message=\"Your week is completely free. Add some events to get organized!\"\n//       showCreateButton={canEditData}\n//       onCreate={() => openAddEventForm(selectedDate)}\n//     />\n//   );\n//   // Render time slots with events\n//   const renderTimeSlots = () => (\n//     <div className=\"flex-1 relative bg-white border-b border-neutral-300 overflow-y-auto lg:overflow-auto\" id=\"week-view-container\">\n//       <div className=\"relative overflow-x-auto lg:overflow-x-visible\">\n//         <div className=\"flex flex-col min-w-[700px] lg:min-w-0\">\n//           <div className=\"relative\">\n//             {hours.map((hour, i) => (\n//               <div\n//                 key={hour}\n//                 className={cn(\n//                   \"flex border-b border-neutral-300 hover:bg-neutral-50 transition-colors\",\n//                   i === hours.length - 1 && \"border-b-neutral-300\"\n//                 )}\n//                 style={{ height: '60px' }}\n//               >\n//                 {/* Time Label */}\n//                 <div \n//                   data-time-labels=\"true\"\n//                   className=\"sticky left-0 flex items-start justify-end pr-4 pt-2 text-xs font-medium text-black border-r border-neutral-200 bg-white z-20 w-14 lg:w-20\"\n//                 >\n//                   <div className=\"text-right\">\n//                     <div className=\"text-xs font-semibold\">\n//                       {format(setHours(new Date(), hour), 'h a')}\n//                     </div>\n//                   </div>\n//                 </div>\n//                 {/* Time Slots */}\n//                 {days.map((day) => {\n//                   const daySegments = getSegmentsForDay(timeSlotSegments, day);\n//                   const { segmentLayouts } = calculateLayout(daySegments);\n//                   return (\n//                     <TimeSlot\n//                       key={`${day.toISOString()}-${hour}`}\n//                       day={day}\n//                       hour={hour}\n//                       onDoubleClick={(minute) => {\n//                         if (canEditData) {\n//                           const newDate = new Date(day);\n//                           newDate.setHours(hour, minute, 0, 0);\n//                           openAddEventForm(newDate);\n//                         }\n//                       }}\n//                     >\n//                       {segmentLayouts.map((layout) => {\n//                         const segmentStart = layout.segment.startTime;\n//                         const isFirstHour = segmentStart.getHours() === hour;\n//                         if (!isFirstHour) return null;\n//                         const segmentHeight = getSegmentHeight(layout.segment);\n//                         const topOffset = getSegmentTopOffset(layout.segment);\n//                         return (\n//                           <CalendarEventSegment\n//                             key={layout.segment.id}\n//                             segment={layout.segment}\n//                             style={{\n//                               height: `${segmentHeight}px`,\n//                               position: 'absolute',\n//                               top: `${topOffset}px`,\n//                               left: `${layout.left}%`,\n//                               width: `${layout.width}%`,\n//                               zIndex: selectedEvent === layout.segment.originalEventId ? 20 : layout.zIndex,\n//                               paddingRight: '2px',\n//                               border: layout.hasOverlap ? '1px solid white' : 'none',\n//                             }}\n//                             onClick={(e) => {\n//                               e.stopPropagation();\n//                               const container = document.getElementById('week-view-container');\n//                               if (container) {\n//                                 savedScrollTop.current = container.scrollTop;\n//                               }\n//                               setSelectedEvent(layout.segment.originalEventId);\n//                               handleEventClick(layout.segment.originalEvent);\n//                             }}\n//                             view=\"week\"\n//                             isDragging={activeDragData?.payload?.id === layout.segment.id}\n//                           />\n//                         );\n//                       })}\n//                     </TimeSlot>\n//                   );\n//                 })}\n//               </div>\n//             ))}\n//           </div>\n//           {/* Current Time Indicator */}\n//           {currentTimePosition && (\n//             <div className=\"absolute top-0 bottom-0 left-14 lg:left-20 right-0 pointer-events-none z-30\">\n//               <div className=\"relative h-full w-full\">\n//                 <div\n//                   className=\"absolute flex items-center\"\n//                   style={{\n//                     top: `${(currentTimePosition.hour + currentTimePosition.minutes / 60) * 60}px`,\n//                     left: `${(currentTimePosition.dayIndex / 7) * 100}%`,\n//                     width: `${(1 / 7) * 100}%`,\n//                   }}\n//                 >\n//                   <div className=\"w-3 h-3 rounded-full bg-red-500 border-2 border-white shadow-lg -ml-1.5\" />\n//                   <div className=\"flex-1 border-t-2 border-red-500 shadow-sm\" />\n//                 </div>\n//               </div>\n//             </div>\n//           )}\n//         </div>\n//       </div>\n//     </div>\n//   );\n//   return (\n//     <div className=\"h-full flex flex-col bg-white\">\n//       {/* Header */}\n//       <div \n//         data-day-headers=\"true\"\n//         className=\"border-b border-neutral-300 bg-white sticky top-0 z-20\"\n//       >\n//         <div className=\"flex overflow-x-hidden\">\n//           <div className=\"sticky left-0 bg-white z-10 w-14 lg:w-20\"></div>\n//           <div className=\"flex flex-1 min-w-[calc(100vw-3.5rem)] lg:min-w-0\">\n//             {days.map((day, i) => (\n//               <div\n//                 key={i}\n//                 className=\"flex-1 text-center cursor-pointer py-3 px-0 lg:py-4\"\n//                 onClick={() => setSelectedDate(day)}\n//               >\n//                 <div className={cn(\n//                   \"font-semibold text-black mb-1\",\n//                   \"text-xs\"\n//                 )}>\n//                   {format(day, 'EEE')}\n//                 </div>\n//                 <div className={cn(\n//                   \"inline-flex items-center justify-center font-medium text-xs w-6 h-6 rounded-full\",\n//                   isToday(day)\n//                     ? \"bg-black text-white\"\n//                     : \"text-black hover:bg-neutral-100\"\n//                 )}>\n//                   {format(day, 'd')}\n//                 </div>\n//               </div>\n//             ))}\n//           </div>\n//         </div>\n//       </div>\n//       {/* All-Day Row */}\n//       <AllDayRow\n//         selectedDate={selectedDate}\n//         segments={allDaySegments}\n//         selectedEvent={selectedEvent}\n//         setSelectedEvent={setSelectedEvent}\n//         handleEventClick={handleEventClick}\n//         canEditData={canEditData}\n//         openAddEventForm={openAddEventForm}\n//         view=\"week\"\n//         activeDragData={activeDragData}\n//       />\n//       {/* Main Content */}\n//       {weekSegments.length === 0 \n//         ? renderEmptyState() \n//         : renderTimeSlots()}\n//     </div>\n//   );\n// };\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/WeekView.tsx\n"));

/***/ })

});