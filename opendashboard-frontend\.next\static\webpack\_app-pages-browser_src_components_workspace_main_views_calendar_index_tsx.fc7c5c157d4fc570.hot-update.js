"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/DayView.tsx":
/*!*****************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/DayView.tsx ***!
  \*****************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DayView: function() { return /* binding */ DayView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=format,isToday,setHours!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=format,isToday,setHours!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=format,isToday,setHours!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/setHours/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CalendarEventSegment */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx\");\n/* harmony import */ var _NoEvents__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./NoEvents */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/NoEvents.tsx\");\n/* harmony import */ var _utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/multiDayEventUtils */ \"(app-pages-browser)/./src/utils/multiDayEventUtils.ts\");\n/* harmony import */ var _utils_eventCollisionUtils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/eventCollisionUtils */ \"(app-pages-browser)/./src/utils/eventCollisionUtils.ts\");\n/* harmony import */ var _AllDayRow__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./AllDayRow */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/AllDayRow.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst TimeSlot = (param)=>{\n    let { hour, date, onDoubleClick, children } = param;\n    // Create 60 minute segments for precise dropping\n    const minuteSegments = Array.from({\n        length: 60\n    }, (_, i)=>i);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-1 relative min-h-[60px] cursor-pointer\"),\n        style: {\n            height: \"60px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex flex-col\",\n                children: minuteSegments.map((minute)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MinuteSegment, {\n                        date: date,\n                        hour: hour,\n                        minute: minute,\n                        style: {\n                            height: \"\".concat(100 / 60, \"%\"),\n                            top: \"\".concat(minute / 60 * 100, \"%\")\n                        },\n                        onDoubleClick: ()=>onDoubleClick(minute)\n                    }, minute, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, undefined);\n};\n_c = TimeSlot;\n// New component for minute-level droppable segments\nconst MinuteSegment = (param)=>{\n    let { date, hour, minute, style, onDoubleClick } = param;\n    _s();\n    const { setNodeRef, isOver } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__.useDroppable)({\n        id: \"timeslot-\".concat((0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(date, \"yyyy-MM-dd\"), \"-\").concat(hour, \"-\").concat(minute),\n        data: {\n            date: date,\n            hour,\n            minute,\n            type: \"timeslot-minute\"\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: setNodeRef,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"absolute w-full\", isOver && \"bg-blue-50\"),\n        style: style,\n        onDoubleClick: onDoubleClick\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n        lineNumber: 99,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MinuteSegment, \"DmJTTt6A5xWIX/faBiFge3FOLrw=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__.useDroppable\n    ];\n});\n_c1 = MinuteSegment;\nconst DayView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, openAddEventForm, canEditData, savedScrollTop, handleEventClick, activeDragData } = param;\n    _s1();\n    const hours = Array.from({\n        length: 24\n    }, (_, i)=>i);\n    // Memoize event segments to prevent unnecessary recalculations\n    const daySegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const allSegments = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.eventsToSegments)(events);\n        return (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.getSegmentsForDay)(allSegments, selectedDate);\n    }, [\n        events,\n        selectedDate\n    ]);\n    // Separate all-day and time-slot segments\n    const allDaySegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.getAllDaySegments)(daySegments), [\n        daySegments\n    ]);\n    const timeSlotSegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.getTimeSlotSegments)(daySegments), [\n        daySegments\n    ]);\n    // Calculate layout for overlapping segments\n    const { segmentLayouts } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return (0,_utils_eventCollisionUtils__WEBPACK_IMPORTED_MODULE_6__.calculateLayout)(timeSlotSegments);\n    }, [\n        timeSlotSegments\n    ]);\n    // Memoize current time position\n    const currentTimePosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate) ? {\n            hour: new Date().getHours(),\n            minutes: new Date().getMinutes()\n        } : null, [\n        selectedDate\n    ]);\n    // Render empty state when no events\n    const renderEmptyState = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NoEvents__WEBPACK_IMPORTED_MODULE_4__.NoEvents, {\n            title: \"No events scheduled\",\n            message: (0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate) ? \"You have a free day ahead! Add an event to get started.\" : \"\".concat((0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(selectedDate, \"EEEE, MMMM d\"), \" is completely free.\"),\n            showCreateButton: canEditData,\n            onCreate: ()=>{\n                const newDate = new Date(selectedDate);\n                newDate.setHours(9, 0, 0, 0);\n                openAddEventForm(newDate);\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n            lineNumber: 152,\n            columnNumber: 5\n        }, undefined);\n    // Render time slots with events\n    const renderTimeSlots = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 overflow-auto relative bg-white\",\n            id: \"day-view-container\",\n            children: [\n                hours.map((hour, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex border-b border-neutral-300 hover:bg-neutral-50 transition-colors\", i === hours.length - 1 && \"border-b-neutral-300\"),\n                        style: {\n                            height: \"60px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                \"data-time-labels\": \"true\",\n                                className: \"flex items-start justify-end pr-4 pt-2 text-xs font-medium text-black border-r border-neutral-300 sticky left-0 bg-white z-10 w-14 lg:w-20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs font-semibold\",\n                                            children: (0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(selectedDate, hour), \"h\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-black opacity-60\",\n                                            children: (0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(selectedDate, hour), \"a\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TimeSlot, {\n                                hour: hour,\n                                date: selectedDate,\n                                onDoubleClick: (minute)=>{\n                                    if (canEditData) {\n                                        const newDate = new Date(selectedDate);\n                                        newDate.setHours(hour, minute, 0, 0);\n                                        openAddEventForm(newDate);\n                                    }\n                                },\n                                children: segmentLayouts.map((layout)=>{\n                                    var _activeDragData_payload;\n                                    const segmentStart = layout.segment.startTime;\n                                    const isFirstHour = segmentStart.getHours() === hour;\n                                    if (!isFirstHour) return null;\n                                    const segmentHeight = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.getSegmentHeight)(layout.segment);\n                                    const topOffset = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.getSegmentTopOffset)(layout.segment);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__.CalendarEventSegment, {\n                                        segment: layout.segment,\n                                        style: {\n                                            height: \"\".concat(segmentHeight, \"px\"),\n                                            position: \"absolute\",\n                                            top: \"\".concat(topOffset, \"px\"),\n                                            left: \"\".concat(layout.left, \"%\"),\n                                            width: \"\".concat(layout.width, \"%\"),\n                                            zIndex: selectedEvent === layout.segment.originalEventId ? 20 : layout.zIndex,\n                                            paddingRight: \"2px\",\n                                            border: layout.hasOverlap ? \"1px solid white\" : \"none\"\n                                        },\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            const container = document.getElementById(\"day-view-container\");\n                                            if (container) {\n                                                savedScrollTop.current = container.scrollTop;\n                                            }\n                                            setSelectedEvent(layout.segment.originalEventId);\n                                            handleEventClick(layout.segment.originalEvent);\n                                        },\n                                        view: \"day\",\n                                        isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === layout.segment.id\n                                    }, layout.segment.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 17\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, i, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, undefined)),\n                currentTimePosition && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute flex items-center z-30 pointer-events-none left-14 lg:left-20\",\n                    style: {\n                        top: \"\".concat((currentTimePosition.hour + currentTimePosition.minutes / 60) * 60, \"px\"),\n                        right: \"4px\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-3 h-3 rounded-full bg-red-500 border-2 border-white shadow-lg -ml-1.5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 border-t-2 border-red-500 shadow-sm\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n            lineNumber: 168,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center bg-white border-b border-neutral-300 py-2 px-2 lg:px-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"font-semibold text-black mb-1 text-xs\",\n                        children: (0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(selectedDate, \"EEEE\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center font-medium text-xs w-6 h-6 rounded-full\", (0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate) ? \"bg-black text-white\" : \"text-black hover:bg-neutral-100\"),\n                        children: (0,_barrel_optimize_names_format_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(selectedDate, \"d\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                lineNumber: 265,\n                columnNumber: 7\n            }, undefined),\n            daySegments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AllDayRow__WEBPACK_IMPORTED_MODULE_7__.AllDayRow, {\n                selectedDate: selectedDate,\n                segments: allDaySegments,\n                selectedEvent: selectedEvent,\n                setSelectedEvent: setSelectedEvent,\n                handleEventClick: handleEventClick,\n                canEditData: canEditData,\n                openAddEventForm: openAddEventForm,\n                view: \"day\",\n                activeDragData: activeDragData\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                lineNumber: 281,\n                columnNumber: 9\n            }, undefined),\n            daySegments.length === 0 ? renderEmptyState() : renderTimeSlots()\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n        lineNumber: 263,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(DayView, \"rKQaW4qzJjohXAHqKTEXOPEYpLE=\");\n_c2 = DayView;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"TimeSlot\");\n$RefreshReg$(_c1, \"MinuteSegment\");\n$RefreshReg$(_c2, \"DayView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/DayView.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/eventCollisionUtils.ts":
/*!******************************************!*\
  !*** ./src/utils/eventCollisionUtils.ts ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateAllDayLayout: function() { return /* binding */ calculateAllDayLayout; },\n/* harmony export */   calculateEventLayout: function() { return /* binding */ calculateEventLayout; },\n/* harmony export */   calculateLayout: function() { return /* binding */ calculateLayout; },\n/* harmony export */   calculateSegmentLayout: function() { return /* binding */ calculateSegmentLayout; },\n/* harmony export */   eventsOverlap: function() { return /* binding */ eventsOverlap; }\n/* harmony export */ });\n/* harmony import */ var _barrel_optimize_names_isSameDay_date_fns__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! __barrel_optimize__?names=isSameDay!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n\n// --- NEW CONSTANTS ---\nconst GUTTER_WIDTH_PERCENT = 10; // Space on the right for creating new events\nconst CASCADE_STAGGER_PERCENT = 15; // How much to offset each cascading event\nconst MIN_EVENT_WIDTH_PERCENT = 50; // Minimum width for events to remain readable\n/**\n * Checks if two event segments overlap in time on the same day.\n */ const segmentsOverlap = (segment1, segment2)=>{\n    // Must be on the same day\n    if (!(0,_barrel_optimize_names_isSameDay_date_fns__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(segment1.date, segment2.date)) {\n        return false;\n    }\n    return segment1.startTime < segment2.endTime && segment2.startTime < segment1.endTime;\n};\n/**\n * The primary function to calculate layout for event segments, incorporating a \"+N more\" button\n * and a gutter for creating new events.\n */ const calculateLayout = (segments)=>{\n    const finalLayout = {\n        segmentLayouts: []\n    };\n    if (!segments.length) {\n        return finalLayout;\n    }\n    // Sort all segments by start time, then by duration (longer events first to establish columns)\n    const sortedSegments = [\n        ...segments\n    ].sort((a, b)=>{\n        const startDiff = a.startTime.getTime() - b.startTime.getTime();\n        if (startDiff !== 0) return startDiff;\n        const durationB = b.endTime.getTime() - b.startTime.getTime();\n        const durationA = a.endTime.getTime() - a.startTime.getTime();\n        return durationB - durationA;\n    });\n    const processedSegments = new Set();\n    for (const segment of sortedSegments){\n        if (processedSegments.has(segment.id)) {\n            continue;\n        }\n        // Find all overlapping segments for the current segment\n        const overlappingGroup = sortedSegments.filter((s)=>segmentsOverlap(segment, s));\n        // This will hold the columns of segments for the current overlapping group\n        const columns = [];\n        overlappingGroup.forEach((groupSegment)=>{\n            let placed = false;\n            // Find the first column where this segment can fit\n            for (const column of columns){\n                if (column.every((s)=>!segmentsOverlap(groupSegment, s))) {\n                    column.push(groupSegment);\n                    placed = true;\n                    break;\n                }\n            }\n            // If it doesn't fit in any existing column, create a new one\n            if (!placed) {\n                columns.push([\n                    groupSegment\n                ]);\n            }\n        });\n        const maxColumns = columns.length;\n        const availableWidth = 100 - GUTTER_WIDTH_PERCENT;\n        // --- SMART CASCADING LOGIC ---\n        // Calculate how much space we need for cascading\n        const totalCascadeSpace = (maxColumns - 1) * CASCADE_STAGGER_PERCENT;\n        const calculatedWidth = availableWidth - totalCascadeSpace;\n        // If calculated width would be too narrow, adjust the cascade strategy\n        let eventWidth;\n        let actualStagger;\n        if (calculatedWidth < MIN_EVENT_WIDTH_PERCENT) {\n            // Use minimum width and reduce stagger to fit\n            eventWidth = MIN_EVENT_WIDTH_PERCENT;\n            const maxPossibleStagger = (availableWidth - MIN_EVENT_WIDTH_PERCENT) / Math.max(1, maxColumns - 1);\n            actualStagger = Math.max(5, Math.min(CASCADE_STAGGER_PERCENT, maxPossibleStagger));\n        } else {\n            // Normal cascading\n            eventWidth = calculatedWidth;\n            actualStagger = CASCADE_STAGGER_PERCENT;\n        }\n        columns.forEach((column, colIndex)=>{\n            column.forEach((seg)=>{\n                if (!processedSegments.has(seg.id)) {\n                    const leftPosition = colIndex * actualStagger;\n                    // Ensure the event doesn't extend beyond the available width (preserving gutter)\n                    const maxAllowedWidth = availableWidth - leftPosition;\n                    const finalWidth = Math.min(eventWidth, maxAllowedWidth);\n                    finalLayout.segmentLayouts.push({\n                        segment: seg,\n                        left: leftPosition,\n                        width: finalWidth,\n                        zIndex: 10 + colIndex,\n                        hasOverlap: maxColumns > 1\n                    });\n                    processedSegments.add(seg.id);\n                }\n            });\n        });\n        // Mark all segments in the processed group so we don't re-calculate them\n        overlappingGroup.forEach((s)=>processedSegments.add(s.id));\n    }\n    return finalLayout;\n};\n// Deprecated old functions, replaced by calculateLayout\n/**\n * @deprecated Use calculateLayout instead. This function will be removed.\n */ const calculateSegmentLayout = (segments)=>{\n    console.warn(\"calculateSegmentLayout is deprecated. Use calculateLayout instead.\");\n    return [];\n};\n/**\n * @deprecated Use calculateLayout instead. This function will be removed.\n */ const calculateEventLayout = (events)=>{\n    console.warn(\"calculateEventLayout is deprecated. Use calculateLayout instead.\");\n    return [];\n};\n/**\n * Checks if two events overlap in time\n * This might still be useful elsewhere, so we keep it for now.\n */ const eventsOverlap = (event1, event2)=>{\n    const start1 = new Date(event1.start);\n    const end1 = new Date(event1.end);\n    const start2 = new Date(event2.start);\n    const end2 = new Date(event2.end);\n    return start1 < end2 && start2 < end1;\n};\n/**\n * A dedicated layout calculator for all-day events, which are laid out horizontally.\n * It determines which events to show and creates a \"more\" indicator for the rest.\n */ const calculateAllDayLayout = function(segments) {\n    let maxVisible = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3;\n    if (segments.length <= maxVisible) {\n        return {\n            visibleSegments: segments,\n            moreCount: 0\n        };\n    }\n    // Sort by start time, then duration\n    const sorted = [\n        ...segments\n    ].sort((a, b)=>{\n        const startDiff = a.startTime.getTime() - b.startTime.getTime();\n        if (startDiff !== 0) return startDiff;\n        return b.endTime.getTime() - b.startTime.getTime() - (a.endTime.getTime() - a.startTime.getTime());\n    });\n    const visibleSegments = sorted.slice(0, maxVisible - 1);\n    const moreCount = segments.length - visibleSegments.length;\n    return {\n        visibleSegments,\n        moreCount\n    };\n}; // ... keep other utility functions like groupOverlappingEvents if they might be used elsewhere ...\n // For this refactor, we are focusing on replacing the main layout calculation.\n // The rest of the file can be cleaned up later.\n // Remove the old grouping and layout functions as their logic is now inside calculateLayout\n /*\nexport const groupOverlappingSegments = (segments: EventSegment[]): EventSegment[][] => {\n    ...\n};\n*/ \n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/eventCollisionUtils.ts\n"));

/***/ })

});